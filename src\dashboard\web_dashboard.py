"""
实时Web仪表板
基于FastAPI的现代化Web仪表板，提供实时训练监控、注意力可视化和系统控制功能
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
import plotly.graph_objects as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.monitoring.metrics_collector import MetricsCollector
from src.monitoring.attention_visualizer import AttentionVisualizer


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.metrics_collector = MetricsCollector()
        self.attention_visualizer = AttentionVisualizer()
    
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections.append(websocket)
        print(f"WebSocket连接已建立，当前连接数: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        print(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            print(f"发送消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                print(f"广播消息失败: {e}")
                disconnected.append(connection)
        
        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)
    
    async def send_metrics_update(self, metrics: Dict[str, Any]):
        """发送指标更新"""
        message = {
            "type": "metrics_update",
            "data": metrics,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast(json.dumps(message))
    
    async def send_attention_update(self, attention_data: Dict[str, Any]):
        """发送注意力数据更新"""
        message = {
            "type": "attention_update", 
            "data": attention_data,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast(json.dumps(message))


class AGVDashboard:
    """AGV训练监控仪表板"""
    
    def __init__(self, host: str = "localhost", port: int = 8000):
        self.host = host
        self.port = port
        self.app = FastAPI(title="AGV训练监控仪表板", version="1.0.0")
        self.websocket_manager = WebSocketManager()
        
        # 创建静态文件和模板目录
        self.static_dir = Path(__file__).parent / "static"
        self.templates_dir = Path(__file__).parent / "templates"
        self.static_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)
        
        # 配置静态文件和模板
        self.app.mount("/static", StaticFiles(directory=str(self.static_dir)), name="static")
        self.templates = Jinja2Templates(directory=str(self.templates_dir))
        
        # 设置路由
        self._setup_routes()
        
        # 启动后台任务
        self._setup_background_tasks()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """仪表板主页"""
            return self.templates.TemplateResponse("dashboard.html", {"request": request})
        
        @self.app.get("/api/metrics")
        async def get_metrics():
            """获取当前指标"""
            try:
                metrics = self.websocket_manager.metrics_collector.get_current_metrics()
                return JSONResponse(content=metrics)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/attention")
        async def get_attention_data():
            """获取注意力数据"""
            try:
                attention_data = self.websocket_manager.attention_visualizer.get_latest_attention_data()
                return JSONResponse(content=attention_data)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/training_status")
        async def get_training_status():
            """获取训练状态"""
            try:
                status = {
                    "is_training": self.websocket_manager.metrics_collector.is_training(),
                    "current_episode": self.websocket_manager.metrics_collector.get_current_episode(),
                    "total_episodes": self.websocket_manager.metrics_collector.get_total_episodes(),
                    "elapsed_time": self.websocket_manager.metrics_collector.get_elapsed_time(),
                    "estimated_remaining": self.websocket_manager.metrics_collector.get_estimated_remaining_time()
                }
                return JSONResponse(content=status)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/control/start_training")
        async def start_training():
            """开始训练"""
            try:
                # 这里可以集成训练启动逻辑
                message = {"type": "training_control", "action": "start", "timestamp": datetime.now().isoformat()}
                await self.websocket_manager.broadcast(json.dumps(message))
                return {"status": "success", "message": "训练已开始"}
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/control/stop_training")
        async def stop_training():
            """停止训练"""
            try:
                message = {"type": "training_control", "action": "stop", "timestamp": datetime.now().isoformat()}
                await self.websocket_manager.broadcast(json.dumps(message))
                return {"status": "success", "message": "训练已停止"}
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/control/pause_training")
        async def pause_training():
            """暂停训练"""
            try:
                message = {"type": "training_control", "action": "pause", "timestamp": datetime.now().isoformat()}
                await self.websocket_manager.broadcast(json.dumps(message))
                return {"status": "success", "message": "训练已暂停"}
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket端点"""
            await self.websocket_manager.connect(websocket)
            try:
                while True:
                    # 接收客户端消息
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # 处理不同类型的消息
                    if message.get("type") == "ping":
                        await self.websocket_manager.send_personal_message(
                            json.dumps({"type": "pong", "timestamp": datetime.now().isoformat()}),
                            websocket
                        )
                    elif message.get("type") == "request_metrics":
                        metrics = self.websocket_manager.metrics_collector.get_current_metrics()
                        await self.websocket_manager.send_metrics_update(metrics)
                    elif message.get("type") == "request_attention":
                        attention_data = self.websocket_manager.attention_visualizer.get_latest_attention_data()
                        await self.websocket_manager.send_attention_update(attention_data)
                        
            except WebSocketDisconnect:
                self.websocket_manager.disconnect(websocket)
            except Exception as e:
                print(f"WebSocket错误: {e}")
                self.websocket_manager.disconnect(websocket)
    
    def _setup_background_tasks(self):
        """设置后台任务"""
        
        @self.app.on_event("startup")
        async def startup_event():
            """启动事件"""
            print("🚀 AGV训练监控仪表板启动中...")
            # 启动指标收集任务
            asyncio.create_task(self._metrics_update_task())
            # 启动注意力数据更新任务
            asyncio.create_task(self._attention_update_task())
            print(f"✅ 仪表板已启动: http://{self.host}:{self.port}")
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """关闭事件"""
            print("🛑 AGV训练监控仪表板正在关闭...")
    
    async def _metrics_update_task(self):
        """指标更新后台任务"""
        while True:
            try:
                # 获取最新指标
                metrics = self.websocket_manager.metrics_collector.get_current_metrics()
                
                # 如果有活跃连接，发送更新
                if self.websocket_manager.active_connections:
                    await self.websocket_manager.send_metrics_update(metrics)
                
                # 等待5秒
                await asyncio.sleep(5)
                
            except Exception as e:
                print(f"指标更新任务错误: {e}")
                await asyncio.sleep(10)
    
    async def _attention_update_task(self):
        """注意力数据更新后台任务"""
        while True:
            try:
                # 获取最新注意力数据
                attention_data = self.websocket_manager.attention_visualizer.get_latest_attention_data()
                
                # 如果有活跃连接，发送更新
                if self.websocket_manager.active_connections and attention_data:
                    await self.websocket_manager.send_attention_update(attention_data)
                
                # 等待10秒
                await asyncio.sleep(10)
                
            except Exception as e:
                print(f"注意力数据更新任务错误: {e}")
                await asyncio.sleep(15)
    
    def run(self, debug: bool = False):
        """运行仪表板"""
        print(f"🌐 启动AGV训练监控仪表板...")
        print(f"📊 访问地址: http://{self.host}:{self.port}")
        print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
        
        if debug:
            uvicorn.run(
                "src.dashboard.web_dashboard:create_dashboard().app",
                host=self.host,
                port=self.port,
                reload=True,
                log_level="info"
            )
        else:
            uvicorn.run(
                self.app,
                host=self.host,
                port=self.port,
                log_level="warning"
            )


def create_dashboard(host: str = "localhost", port: int = 8000) -> AGVDashboard:
    """创建仪表板实例"""
    return AGVDashboard(host=host, port=port)


if __name__ == "__main__":
    # 创建并运行仪表板
    dashboard = create_dashboard(host="0.0.0.0", port=8000)
    dashboard.run(debug=True)
