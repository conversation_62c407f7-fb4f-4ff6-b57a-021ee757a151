"""
仓储环境实现
26×10网格世界多AGV协同调度环境
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, List, Tuple, Any, Optional
import random
from enum import Enum

from config.env_config import EnvironmentConfig, DEFAULT_ENV_CONFIG
from .agv_entity import AGVEntity
from .task_manager import TaskManager, TaskStatus


class CellType(Enum):
    """网格单元类型"""
    FREE = 0        # 自由空间
    SHELF = 1       # 货架
    AGV = 2         # AGV占用
    TASK = 3        # 任务位置


class WarehouseEnv(gym.Env):
    """
    多AGV仓储调度环境

    观察空间：
    - 全局状态：所有AGV和任务的状态信息
    - 局部观察：每个AGV的局部观察信息

    动作空间：
    - 离散动作：上、下、左、右、等待

    奖励函数：
    - 任务完成奖励
    - 移动效率奖励
    - 碰撞惩罚
    - 协作奖励
    """

    metadata = {"render_modes": ["human", "rgb_array"], "render_fps": 4}

    def __init__(self, config: EnvironmentConfig = None, render_mode: str = None):
        """
        初始化仓储环境

        Args:
            config: 环境配置
            render_mode: 渲染模式
        """
        super().__init__()

        self.config = config or DEFAULT_ENV_CONFIG
        self.render_mode = render_mode

        # 环境基础属性
        self.map_width = self.config.map_width
        self.map_height = self.config.map_height
        self.num_agvs = self.config.num_agvs
        self.num_tasks = self.config.num_tasks

        # 初始化地图
        self.grid = np.zeros((self.map_height, self.map_width), dtype=int)
        self.shelf_positions = set()
        self._setup_shelves()

        # 初始化AGV和任务管理器
        self.agvs: List[AGVEntity] = []
        self.task_manager = TaskManager(self.config)

        # 环境状态
        self.current_step = 0
        self.episode_rewards = []
        self.collision_count = 0

        # 定义动作空间（每个AGV的动作）
        self.action_space = spaces.MultiDiscrete([5] * self.num_agvs)  # 5个动作：上下左右等待

        # 定义观察空间（稍后在reset中具体设置）
        self._setup_observation_space()

        # 可视化相关
        self.window = None
        self.clock = None

    def _setup_shelves(self):
        """设置货架布局"""
        shelf_layout = self.config.get_shelf_layout()

        for x, y, width, height in shelf_layout:
            for i in range(height):
                for j in range(width):
                    shelf_x = x + j
                    shelf_y = y + i
                    if 0 <= shelf_x < self.map_width and 0 <= shelf_y < self.map_height:
                        self.grid[shelf_y, shelf_x] = CellType.SHELF.value
                        self.shelf_positions.add((shelf_x, shelf_y))

    def _setup_observation_space(self):
        """设置观察空间"""
        # AGV状态维度：位置(2) + 载重(1) + 队列长度(1) + 目标任务(1) + 状态(1) = 6
        agv_obs_dim = 6

        # 任务状态维度：位置(2) + 重量(1) + 状态(1) = 4
        task_obs_dim = 4

        # 全局观察空间
        global_obs_dim = self.num_agvs * agv_obs_dim + self.num_tasks * task_obs_dim

        # 局部观察空间（每个AGV）
        local_obs_dim = agv_obs_dim + self.num_tasks * task_obs_dim + (self.num_agvs - 1) * agv_obs_dim

        self.observation_space = spaces.Dict({
            "global": spaces.Box(
                low=-np.inf, high=np.inf,
                shape=(global_obs_dim,), dtype=np.float32
            ),
            "local": spaces.Box(
                low=-np.inf, high=np.inf,
                shape=(self.num_agvs, local_obs_dim), dtype=np.float32
            )
        })

    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[Dict, Dict]:
        """
        重置环境

        Returns:
            observation: 初始观察
            info: 额外信息
        """
        super().reset(seed=seed)

        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

        # 重置环境状态
        self.current_step = 0
        self.episode_rewards = []
        self.collision_count = 0

        # 清除AGV位置
        for agv in self.agvs:
            if agv.position:
                x, y = agv.position
                if self.grid[y, x] == CellType.AGV.value:
                    self.grid[y, x] = CellType.FREE.value

        # 重新初始化AGV
        self.agvs = []
        self._spawn_agvs()

        # 重置任务管理器
        self.task_manager.reset()
        self._spawn_tasks()

        # 获取初始观察
        observation = self._get_observation()
        info = self._get_info()

        return observation, info

    def step(self, actions: np.ndarray) -> Tuple[Dict, List[float], bool, bool, Dict]:
        """
        执行一步环境交互

        Args:
            actions: 所有AGV的动作

        Returns:
            observation: 新的观察
            rewards: 每个AGV的奖励
            terminated: 是否终止
            truncated: 是否截断
            info: 额外信息
        """
        self.current_step += 1

        # 执行动作
        rewards = self._execute_actions(actions)

        # 更新任务状态
        self.task_manager.update()

        # 检查终止条件
        terminated = self._check_terminated()
        truncated = self.current_step >= self.config.max_episode_steps

        # 获取新观察
        observation = self._get_observation()
        info = self._get_info()

        # 记录奖励
        self.episode_rewards.append(sum(rewards))

        return observation, rewards, terminated, truncated, info

    def _spawn_agvs(self):
        """生成AGV"""
        free_positions = self._get_free_positions()

        for i in range(self.num_agvs):
            if not free_positions:
                raise ValueError("没有足够的自由位置来放置AGV")

            # 随机选择位置
            position = random.choice(free_positions)
            free_positions.remove(position)

            # 创建AGV
            agv = AGVEntity(
                agv_id=i,
                position=position,
                capacity=self.config.agv_capacity,
                speed=self.config.agv_speed
            )

            self.agvs.append(agv)

            # 在地图上标记AGV位置
            x, y = position
            self.grid[y, x] = CellType.AGV.value

    def _spawn_tasks(self):
        """生成任务（在货架上）"""
        # 获取货架位置而不是自由位置
        shelf_positions = list(self.shelf_positions)

        if len(shelf_positions) < self.num_tasks:
            print(f"警告：货架位置({len(shelf_positions)})少于任务数量({self.num_tasks})")

        # 随机选择货架位置放置任务
        selected_positions = random.sample(shelf_positions, min(self.num_tasks, len(shelf_positions)))

        for i in range(len(selected_positions)):
            position = selected_positions[i]

            # 根据配置生成任务属性（只有重量，没有优先级）
            weight = random.choice(self.config.task_weights)

            # 创建任务
            self.task_manager.create_task(
                task_id=i,
                position=position,
                weight=weight
            )

    def _get_free_positions(self) -> List[Tuple[int, int]]:
        """获取所有自由位置"""
        free_positions = []

        for y in range(self.map_height):
            for x in range(self.map_width):
                if self.grid[y, x] == CellType.FREE.value:
                    free_positions.append((x, y))

        return free_positions

    def _execute_actions(self, actions: np.ndarray) -> List[float]:
        """
        执行所有AGV的动作

        Args:
            actions: 动作数组

        Returns:
            rewards: 每个AGV的奖励
        """
        rewards = []

        # 动作映射：0=上, 1=下, 2=左, 3=右, 4=等待
        action_map = {
            0: (0, -1),   # 上
            1: (0, 1),    # 下
            2: (-1, 0),   # 左
            3: (1, 0),    # 右
            4: (0, 0)     # 等待
        }

        # 计算所有AGV的新位置
        new_positions = []
        for i, agv in enumerate(self.agvs):
            action = actions[i]
            dx, dy = action_map[action]

            current_x, current_y = agv.position
            new_x = current_x + dx
            new_y = current_y + dy

            # 边界检查
            if not (0 <= new_x < self.map_width and 0 <= new_y < self.map_height):
                new_x, new_y = current_x, current_y

            # 障碍物检查
            if self.grid[new_y, new_x] == CellType.SHELF.value:
                new_x, new_y = current_x, current_y

            new_positions.append((new_x, new_y))

        # 碰撞检测和解决
        final_positions = self._resolve_collisions(new_positions)

        # 更新AGV位置并计算奖励
        for i, agv in enumerate(self.agvs):
            old_pos = agv.position
            new_pos = final_positions[i]

            # 更新地图
            if old_pos != new_pos:
                self.grid[old_pos[1], old_pos[0]] = CellType.FREE.value
                self.grid[new_pos[1], new_pos[0]] = CellType.AGV.value

            # 更新AGV位置
            agv.position = new_pos

            # 计算奖励
            reward = self._calculate_reward(agv, actions[i], old_pos, new_pos)
            rewards.append(reward)

        return rewards

    def _resolve_collisions(self, proposed_positions: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """
        解决AGV间的碰撞冲突

        Args:
            proposed_positions: 提议的新位置

        Returns:
            final_positions: 解决冲突后的最终位置
        """
        final_positions = []
        position_count = {}

        # 统计每个位置的AGV数量
        for pos in proposed_positions:
            position_count[pos] = position_count.get(pos, 0) + 1

        # 解决冲突
        for i, pos in enumerate(proposed_positions):
            if position_count[pos] > 1:
                # 发生碰撞，AGV保持原位置
                final_positions.append(self.agvs[i].position)
                self.collision_count += 1
            else:
                final_positions.append(pos)

        return final_positions

    def _calculate_reward(self, agv: AGVEntity, action: int, old_pos: Tuple[int, int],
                         new_pos: Tuple[int, int]) -> float:
        """
        计算单个AGV的奖励

        Args:
            agv: AGV实体
            action: 执行的动作
            old_pos: 旧位置
            new_pos: 新位置

        Returns:
            reward: 奖励值
        """
        reward = 0.0

        # 任务完成奖励
        completed_tasks = agv.get_completed_tasks()
        if completed_tasks:
            reward += self.config.completion_reward * len(completed_tasks)

        # 移动效率奖励/惩罚
        if action == 4:  # 等待
            reward += self.config.waiting_penalty
        elif old_pos != new_pos:  # 移动
            reward += self.config.movement_penalty

            # 距离引导奖励
            if agv.current_target:
                target_pos = self.task_manager.get_task_position(agv.current_target)
                if target_pos:
                    old_dist = self._manhattan_distance(old_pos, target_pos)
                    new_dist = self._manhattan_distance(new_pos, target_pos)
                    if new_dist < old_dist:
                        reward += self.config.distance_reward
                    elif new_dist > old_dist:
                        reward -= self.config.distance_reward

        # 碰撞惩罚
        if old_pos == new_pos and action != 4:
            reward += self.config.collision_penalty

        # 空闲惩罚
        if not agv.current_target and not agv.task_queue:
            reward += self.config.idle_penalty

        return reward

    def _manhattan_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> int:
        """计算曼哈顿距离"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])

    def _get_observation(self) -> Dict:
        """
        获取当前观察

        Returns:
            observation: 观察字典
        """
        # 全局观察
        global_obs = []

        # AGV状态
        for agv in self.agvs:
            agv_state = agv.get_state_vector(self.map_width, self.map_height)
            global_obs.extend(agv_state)

        # 任务状态
        for task in self.task_manager.get_all_tasks():
            task_state = task.get_state_vector(self.map_width, self.map_height)
            global_obs.extend(task_state)

        # 局部观察
        local_obs = []
        for i, agv in enumerate(self.agvs):
            agv_local_obs = []

            # 自身状态
            agv_state = agv.get_state_vector(self.map_width, self.map_height)
            agv_local_obs.extend(agv_state)

            # 所有任务状态
            for task in self.task_manager.get_all_tasks():
                task_state = task.get_state_vector(self.map_width, self.map_height)
                agv_local_obs.extend(task_state)

            # 其他AGV状态
            for j, other_agv in enumerate(self.agvs):
                if i != j:
                    other_state = other_agv.get_state_vector(self.map_width, self.map_height)
                    agv_local_obs.extend(other_state)

            local_obs.append(agv_local_obs)

        return {
            "global": np.array(global_obs, dtype=np.float32),
            "local": np.array(local_obs, dtype=np.float32)
        }

    def _get_info(self) -> Dict:
        """获取额外信息"""
        completed_tasks = sum(len(agv.get_completed_tasks()) for agv in self.agvs)
        total_tasks = self.task_manager.get_total_tasks()

        return {
            "step": self.current_step,
            "completed_tasks": completed_tasks,
            "total_tasks": total_tasks,
            "completion_rate": completed_tasks / total_tasks if total_tasks > 0 else 0.0,
            "collision_count": self.collision_count,
            "agv_positions": [agv.position for agv in self.agvs],
            "task_positions": [task.position for task in self.task_manager.get_all_tasks()]
        }

    def _check_terminated(self) -> bool:
        """检查是否终止"""
        # 所有任务完成
        if self.task_manager.all_tasks_completed():
            return True

        # 超时
        if self.current_step >= self.config.max_episode_steps:
            return True

        return False

    def render(self):
        """渲染环境"""
        if self.render_mode == "human":
            return self._render_human()
        elif self.render_mode == "rgb_array":
            return self._render_rgb_array()

    def _render_human(self):
        """人类可视化渲染"""
        try:
            import pygame
        except ImportError:
            raise ImportError("pygame is required for human rendering")

        if self.window is None:
            pygame.init()
            pygame.display.init()
            self.window = pygame.display.set_mode((800, 400))

        if self.clock is None:
            self.clock = pygame.time.Clock()

        canvas = pygame.Surface((800, 400))
        canvas.fill((255, 255, 255))  # 白色背景

        # 计算网格大小
        cell_width = 800 // self.map_width
        cell_height = 400 // self.map_height

        # 绘制网格
        for x in range(self.map_width + 1):
            pygame.draw.line(canvas, (200, 200, 200),
                           (x * cell_width, 0), (x * cell_width, 400))
        for y in range(self.map_height + 1):
            pygame.draw.line(canvas, (200, 200, 200),
                           (0, y * cell_height), (800, y * cell_height))

        # 绘制货架
        for (x, y) in self.shelf_positions:
            rect = pygame.Rect(x * cell_width, y * cell_height, cell_width, cell_height)
            pygame.draw.rect(canvas, (139, 69, 19), rect)  # 棕色货架

        # 绘制任务（在货架上显示为小圆点）
        for task in self.task_manager.get_all_tasks():
            if task.status == TaskStatus.PENDING:
                x, y = task.position
                center = (x * cell_width + cell_width // 2,
                         y * cell_height + cell_height // 2)
                # 根据重量设置颜色：5=黄色，10=红色
                color = (255, 255, 0) if task.weight == 5 else (255, 0, 0)
                pygame.draw.circle(canvas, color, center, 8)

                # 绘制任务重量
                font = pygame.font.Font(None, 16)
                text = font.render(str(task.weight), True, (0, 0, 0))
                text_rect = text.get_rect(center=center)
                canvas.blit(text, text_rect)

        # 绘制AGV
        colors = [(0, 0, 255), (0, 255, 0), (255, 0, 255), (0, 255, 255)]
        for i, agv in enumerate(self.agvs):
            x, y = agv.position
            center = (x * cell_width + cell_width // 2,
                     y * cell_height + cell_height // 2)
            pygame.draw.circle(canvas, colors[i % len(colors)], center,
                             min(cell_width, cell_height) // 3)

            # 绘制AGV ID
            font = pygame.font.Font(None, 24)
            text = font.render(str(agv.agv_id), True, (255, 255, 255))
            text_rect = text.get_rect(center=center)
            canvas.blit(text, text_rect)

        self.window.blit(canvas, canvas.get_rect())
        pygame.event.pump()
        pygame.display.update()
        self.clock.tick(self.metadata["render_fps"])

    def _render_rgb_array(self):
        """RGB数组渲染"""
        # 创建简单的RGB数组表示
        rgb_array = np.zeros((self.map_height, self.map_width, 3), dtype=np.uint8)

        # 设置背景为白色
        rgb_array.fill(255)

        # 绘制货架（棕色）
        for (x, y) in self.shelf_positions:
            rgb_array[y, x] = [139, 69, 19]

        # 绘制任务（在货架上叠加显示）
        for task in self.task_manager.get_all_tasks():
            if task.status == TaskStatus.PENDING:
                x, y = task.position
                # 在货架颜色基础上叠加任务颜色，根据重量区分
                if task.weight == 5:
                    rgb_array[y, x] = [200, 200, 0]   # 暗黄色（重量5）
                else:  # weight == 10
                    rgb_array[y, x] = [200, 0, 0]     # 暗红色（重量10）

        # 绘制AGV（蓝色/绿色/紫色/青色）
        colors = [[0, 0, 255], [0, 255, 0], [255, 0, 255], [0, 255, 255]]
        for i, agv in enumerate(self.agvs):
            x, y = agv.position
            rgb_array[y, x] = colors[i % len(colors)]

        return rgb_array

    def close(self):
        """关闭环境"""
        if self.window is not None:
            import pygame
            pygame.display.quit()
            pygame.quit()

    def get_action_mask(self, agv_id: int) -> np.ndarray:
        """
        获取动作掩码（哪些动作是有效的）

        Args:
            agv_id: AGV ID

        Returns:
            mask: 动作掩码数组
        """
        if agv_id >= len(self.agvs):
            return np.zeros(5, dtype=bool)

        agv = self.agvs[agv_id]
        x, y = agv.position
        mask = np.ones(5, dtype=bool)  # 默认所有动作都有效

        # 检查边界和障碍物
        # 上
        if y <= 0 or self.grid[y-1, x] == CellType.SHELF.value:
            mask[0] = False
        # 下
        if y >= self.map_height - 1 or self.grid[y+1, x] == CellType.SHELF.value:
            mask[1] = False
        # 左
        if x <= 0 or self.grid[y, x-1] == CellType.SHELF.value:
            mask[2] = False
        # 右
        if x >= self.map_width - 1 or self.grid[y, x+1] == CellType.SHELF.value:
            mask[3] = False
        # 等待总是有效
        mask[4] = True

        return mask

    def get_valid_actions(self) -> List[np.ndarray]:
        """获取所有AGV的有效动作掩码"""
        return [self.get_action_mask(i) for i in range(self.num_agvs)]

    def seed(self, seed: int):
        """设置随机种子"""
        random.seed(seed)
        np.random.seed(seed)

    def get_env_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "map_size": (self.map_width, self.map_height),
            "num_agvs": self.num_agvs,
            "num_tasks": self.num_tasks,
            "shelf_count": len(self.shelf_positions),
            "action_space_size": 5,
            "observation_space": {
                "global_dim": self.observation_space["global"].shape[0],
                "local_dim": self.observation_space["local"].shape[1]
            }
        }