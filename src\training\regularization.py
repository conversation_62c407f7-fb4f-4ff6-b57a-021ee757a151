"""
正则化技术组件
实现各种正则化技术以提高训练稳定性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import math


@dataclass
class RegularizationConfig:
    """正则化配置"""
    weight_decay: float = 1e-4
    dropout_rate: float = 0.1
    attention_dropout: float = 0.1
    layer_norm_eps: float = 1e-5
    spectral_norm: bool = False
    gradient_penalty: float = 0.0
    attention_entropy_reg: float = 0.01
    collaboration_sparsity_reg: float = 0.1


class WeightDecayRegularizer:
    """权重衰减正则化器"""
    
    def __init__(self, weight_decay: float = 1e-4, exclude_bias: bool = True):
        """
        初始化权重衰减正则化器
        
        Args:
            weight_decay: 权重衰减系数
            exclude_bias: 是否排除偏置项
        """
        self.weight_decay = weight_decay
        self.exclude_bias = exclude_bias
        
    def compute_penalty(self, model: nn.Module) -> torch.Tensor:
        """
        计算权重衰减惩罚
        
        Args:
            model: 模型
            
        Returns:
            penalty: 权重衰减惩罚
        """
        penalty = 0.0
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                # 排除偏置项
                if self.exclude_bias and 'bias' in name:
                    continue
                
                # L2正则化
                penalty += torch.sum(param ** 2)
        
        return self.weight_decay * penalty


class AttentionRegularizer:
    """注意力机制正则化器"""
    
    def __init__(self, 
                 entropy_weight: float = 0.01,
                 sparsity_weight: float = 0.1,
                 diversity_weight: float = 0.05):
        """
        初始化注意力正则化器
        
        Args:
            entropy_weight: 熵正则化权重
            sparsity_weight: 稀疏性正则化权重
            diversity_weight: 多样性正则化权重
        """
        self.entropy_weight = entropy_weight
        self.sparsity_weight = sparsity_weight
        self.diversity_weight = diversity_weight
        
    def compute_entropy_penalty(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """
        计算注意力熵惩罚
        
        Args:
            attention_weights: 注意力权重 [batch, heads, seq, seq]
            
        Returns:
            entropy_penalty: 熵惩罚
        """
        # 计算注意力分布的熵
        log_attention = torch.log(attention_weights + 1e-8)
        entropy = -torch.sum(attention_weights * log_attention, dim=-1)
        
        # 鼓励适度的熵（不要太集中也不要太分散）
        target_entropy = math.log(attention_weights.size(-1)) * 0.5  # 目标熵
        entropy_penalty = torch.mean((entropy - target_entropy) ** 2)
        
        return self.entropy_weight * entropy_penalty
    
    def compute_sparsity_penalty(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """
        计算稀疏性惩罚
        
        Args:
            attention_weights: 注意力权重
            
        Returns:
            sparsity_penalty: 稀疏性惩罚
        """
        # L1正则化鼓励稀疏性
        sparsity_penalty = torch.mean(torch.abs(attention_weights))
        
        return self.sparsity_weight * sparsity_penalty
    
    def compute_diversity_penalty(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """
        计算多样性惩罚（鼓励不同头关注不同位置）
        
        Args:
            attention_weights: 注意力权重 [batch, heads, seq, seq]
            
        Returns:
            diversity_penalty: 多样性惩罚
        """
        if attention_weights.dim() < 4:
            return torch.tensor(0.0, device=attention_weights.device)
        
        # 计算不同头之间的相似性
        batch_size, num_heads, seq_len, _ = attention_weights.shape
        
        # 重塑为 [batch, heads, seq*seq]
        flat_attention = attention_weights.view(batch_size, num_heads, -1)
        
        # 计算头之间的余弦相似性
        normalized_attention = F.normalize(flat_attention, dim=-1)
        similarity_matrix = torch.bmm(normalized_attention, normalized_attention.transpose(-2, -1))
        
        # 排除对角线（自己与自己的相似性）
        mask = torch.eye(num_heads, device=attention_weights.device).unsqueeze(0)
        similarity_matrix = similarity_matrix * (1 - mask)
        
        # 惩罚高相似性（鼓励多样性）
        diversity_penalty = torch.mean(torch.abs(similarity_matrix))
        
        return self.diversity_weight * diversity_penalty
    
    def compute_total_penalty(self, 
                            task_attention: Optional[torch.Tensor] = None,
                            collaboration_attention: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        计算总的注意力正则化惩罚
        
        Args:
            task_attention: 任务注意力权重
            collaboration_attention: 协作注意力权重
            
        Returns:
            penalties: 各种惩罚的字典
        """
        penalties = {}
        total_penalty = torch.tensor(0.0)
        
        if task_attention is not None:
            task_entropy = self.compute_entropy_penalty(task_attention)
            task_sparsity = self.compute_sparsity_penalty(task_attention)
            task_diversity = self.compute_diversity_penalty(task_attention)
            
            penalties['task_entropy'] = task_entropy
            penalties['task_sparsity'] = task_sparsity
            penalties['task_diversity'] = task_diversity
            
            total_penalty += task_entropy + task_sparsity + task_diversity
        
        if collaboration_attention is not None:
            collab_entropy = self.compute_entropy_penalty(collaboration_attention)
            collab_sparsity = self.compute_sparsity_penalty(collaboration_attention)
            collab_diversity = self.compute_diversity_penalty(collaboration_attention)
            
            penalties['collab_entropy'] = collab_entropy
            penalties['collab_sparsity'] = collab_sparsity
            penalties['collab_diversity'] = collab_diversity
            
            total_penalty += collab_entropy + collab_sparsity + collab_diversity
        
        penalties['total_attention_penalty'] = total_penalty
        return penalties


class SpectralNormalization:
    """谱归一化"""
    
    def __init__(self, module: nn.Module, name: str = 'weight', n_power_iterations: int = 1):
        """
        初始化谱归一化
        
        Args:
            module: 要应用谱归一化的模块
            name: 参数名称
            n_power_iterations: 幂迭代次数
        """
        self.module = module
        self.name = name
        self.n_power_iterations = n_power_iterations
        
        # 获取权重
        weight = getattr(module, name)
        
        # 初始化u和v向量
        height = weight.data.shape[0]
        width = weight.view(height, -1).data.shape[1]
        
        u = weight.new_empty(height).normal_(0, 1)
        v = weight.new_empty(width).normal_(0, 1)
        
        u = u / torch.norm(u)
        v = v / torch.norm(v)
        
        # 注册为缓冲区
        module.register_buffer(name + "_u", u)
        module.register_buffer(name + "_v", v)
        
        # 删除原始权重并注册为参数
        delattr(module, name)
        module.register_parameter(name + "_orig", nn.Parameter(weight.data))
    
    def compute_weight(self, module: nn.Module) -> torch.Tensor:
        """
        计算谱归一化后的权重
        
        Args:
            module: 模块
            
        Returns:
            normalized_weight: 归一化后的权重
        """
        weight_orig = getattr(module, self.name + "_orig")
        u = getattr(module, self.name + "_u")
        v = getattr(module, self.name + "_v")
        
        height = weight_orig.shape[0]
        weight_mat = weight_orig.view(height, -1)
        
        # 幂迭代法计算最大奇异值
        with torch.no_grad():
            for _ in range(self.n_power_iterations):
                v = F.normalize(torch.mv(weight_mat.t(), u), dim=0)
                u = F.normalize(torch.mv(weight_mat, v), dim=0)
        
        # 计算谱范数
        sigma = torch.dot(u, torch.mv(weight_mat, v))
        
        # 归一化权重
        weight = weight_orig / sigma
        
        return weight.view_as(weight_orig)


class GradientPenalty:
    """梯度惩罚"""
    
    def __init__(self, penalty_weight: float = 10.0):
        """
        初始化梯度惩罚
        
        Args:
            penalty_weight: 惩罚权重
        """
        self.penalty_weight = penalty_weight
    
    def compute_penalty(self, 
                       model: nn.Module,
                       real_data: torch.Tensor,
                       fake_data: torch.Tensor) -> torch.Tensor:
        """
        计算梯度惩罚
        
        Args:
            model: 模型
            real_data: 真实数据
            fake_data: 生成数据
            
        Returns:
            penalty: 梯度惩罚
        """
        batch_size = real_data.size(0)
        
        # 随机插值
        alpha = torch.rand(batch_size, 1, device=real_data.device)
        alpha = alpha.expand_as(real_data)
        
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated.requires_grad_(True)
        
        # 计算插值点的输出
        output = model(interpolated)
        
        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=output,
            inputs=interpolated,
            grad_outputs=torch.ones_like(output),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # 计算梯度范数
        gradients = gradients.view(batch_size, -1)
        gradient_norm = torch.sqrt(torch.sum(gradients ** 2, dim=1) + 1e-12)
        
        # 梯度惩罚
        penalty = torch.mean((gradient_norm - 1) ** 2)
        
        return self.penalty_weight * penalty


class ComprehensiveRegularizer:
    """综合正则化器"""
    
    def __init__(self, config: RegularizationConfig):
        """
        初始化综合正则化器
        
        Args:
            config: 正则化配置
        """
        self.config = config
        
        # 初始化各种正则化器
        self.weight_decay_reg = WeightDecayRegularizer(config.weight_decay)
        self.attention_reg = AttentionRegularizer(
            entropy_weight=config.attention_entropy_reg,
            sparsity_weight=config.collaboration_sparsity_reg
        )
        
        if config.gradient_penalty > 0:
            self.gradient_penalty = GradientPenalty(config.gradient_penalty)
        else:
            self.gradient_penalty = None
    
    def compute_regularization_loss(self, 
                                  model: nn.Module,
                                  attention_weights: Optional[Dict[str, torch.Tensor]] = None,
                                  **kwargs) -> Dict[str, torch.Tensor]:
        """
        计算总的正则化损失
        
        Args:
            model: 模型
            attention_weights: 注意力权重字典
            **kwargs: 其他参数
            
        Returns:
            reg_losses: 正则化损失字典
        """
        reg_losses = {}
        
        # 权重衰减
        weight_decay_loss = self.weight_decay_reg.compute_penalty(model)
        reg_losses['weight_decay'] = weight_decay_loss
        
        # 注意力正则化
        if attention_weights:
            attention_penalties = self.attention_reg.compute_total_penalty(
                task_attention=attention_weights.get('task_attention'),
                collaboration_attention=attention_weights.get('collaboration_attention')
            )
            reg_losses.update(attention_penalties)
        
        # 梯度惩罚
        if self.gradient_penalty and 'real_data' in kwargs and 'fake_data' in kwargs:
            grad_penalty = self.gradient_penalty.compute_penalty(
                model, kwargs['real_data'], kwargs['fake_data']
            )
            reg_losses['gradient_penalty'] = grad_penalty
        
        # 计算总损失
        total_reg_loss = sum(reg_losses.values())
        reg_losses['total_regularization'] = total_reg_loss
        
        return reg_losses
    
    def apply_spectral_norm(self, model: nn.Module):
        """
        应用谱归一化到模型
        
        Args:
            model: 模型
        """
        if not self.config.spectral_norm:
            return
        
        for name, module in model.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv2d)):
                SpectralNormalization(module)
    
    def get_regularization_stats(self, reg_losses: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        获取正则化统计信息
        
        Args:
            reg_losses: 正则化损失字典
            
        Returns:
            stats: 统计信息字典
        """
        stats = {}
        for key, value in reg_losses.items():
            if isinstance(value, torch.Tensor):
                stats[key] = value.item()
            else:
                stats[key] = value
        
        return stats
