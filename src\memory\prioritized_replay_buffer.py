"""
优先级经验回放缓冲区
专为多智能体强化学习设计的优先级经验回放实现
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
from collections import namedtuple
import random

from .sum_tree import SumTree


@dataclass
class MultiAgentExperience:
    """
    多智能体经验样本
    """
    observations: Dict[str, np.ndarray]  # 每个智能体的观察
    actions: Dict[str, Union[int, np.ndarray]]  # 每个智能体的动作
    rewards: Dict[str, float]  # 每个智能体的奖励
    next_observations: Dict[str, np.ndarray]  # 下一状态观察
    dones: Dict[str, bool]  # 完成标志
    infos: Dict[str, Dict]  # 额外信息
    
    # 注意力相关信息
    task_attention_weights: Optional[Dict[str, np.ndarray]] = None
    collaboration_attention_weights: Optional[Dict[str, np.ndarray]] = None
    
    # 优先级相关
    td_errors: Optional[Dict[str, float]] = None
    global_td_error: Optional[float] = None
    priority: Optional[float] = None
    
    # 时间戳和元数据
    timestamp: Optional[float] = None
    episode_id: Optional[int] = None
    step_id: Optional[int] = None


class PrioritizedReplayBuffer:
    """
    优先级经验回放缓冲区
    支持多智能体环境和注意力机制
    """
    
    def __init__(self, 
                 capacity: int,
                 alpha: float = 0.6,
                 beta: float = 0.4,
                 beta_increment: float = 0.001,
                 epsilon: float = 1e-6,
                 max_priority: float = 1.0,
                 attention_priority_weight: float = 0.3):
        """
        初始化优先级经验回放缓冲区
        
        Args:
            capacity: 缓冲区容量
            alpha: 优先级指数，控制优先级的重要性
            beta: 重要性采样指数，控制偏差修正
            beta_increment: beta的增长率
            epsilon: 防止零优先级的小常数
            max_priority: 最大优先级
            attention_priority_weight: 注意力权重在优先级计算中的比重
        """
        self.capacity = capacity
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.epsilon = epsilon
        self.max_priority = max_priority
        self.attention_priority_weight = attention_priority_weight
        
        # 使用SumTree进行高效采样
        self.tree = SumTree(capacity)
        
        # 统计信息
        self.total_added = 0
        self.total_sampled = 0
        
        # 缓存最近的采样索引，用于优先级更新
        self.last_sampled_indices = []
        
    def add(self, experience: MultiAgentExperience):
        """
        添加经验到缓冲区
        
        Args:
            experience: 多智能体经验样本
        """
        # 计算初始优先级
        priority = self._compute_priority(experience)
        
        # 添加到SumTree
        self.tree.add(priority, experience)
        
        # 更新统计
        self.total_added += 1
        
        # 更新最大优先级
        if priority > self.max_priority:
            self.max_priority = priority
    
    def sample(self, batch_size: int) -> Tuple[List[MultiAgentExperience], np.ndarray, List[int]]:
        """
        采样经验批次
        
        Args:
            batch_size: 批次大小
            
        Returns:
            experiences: 经验样本列表
            weights: 重要性采样权重
            indices: 采样索引
        """
        if len(self.tree) == 0:
            raise ValueError("缓冲区为空，无法采样")
        
        # 使用SumTree采样
        indices, priorities, experiences = self.tree.sample(batch_size)
        
        # 计算重要性采样权重
        weights = self._compute_importance_sampling_weights(priorities)
        
        # 缓存采样索引
        self.last_sampled_indices = indices
        
        # 更新统计
        self.total_sampled += batch_size
        
        # 更新beta
        self.beta = min(1.0, self.beta + self.beta_increment)
        
        return experiences, weights, indices
    
    def update_priorities(self, indices: List[int], td_errors: Union[List[float], np.ndarray]):
        """
        更新经验的优先级
        
        Args:
            indices: 经验索引列表
            td_errors: TD误差列表
        """
        if isinstance(td_errors, list):
            td_errors = np.array(td_errors)
        
        for idx, td_error in zip(indices, td_errors):
            # 计算新的优先级
            priority = (abs(td_error) + self.epsilon) ** self.alpha
            
            # 更新SumTree中的优先级
            self.tree.update(idx, priority)
            
            # 更新最大优先级
            if priority > self.max_priority:
                self.max_priority = priority
    
    def _compute_priority(self, experience: MultiAgentExperience) -> float:
        """
        计算经验的初始优先级
        
        Args:
            experience: 经验样本
            
        Returns:
            priority: 优先级
        """
        # 基础优先级：使用最大优先级
        base_priority = self.max_priority
        
        # 如果有TD误差信息，使用TD误差计算优先级
        if experience.global_td_error is not None:
            td_priority = (abs(experience.global_td_error) + self.epsilon) ** self.alpha
            base_priority = max(base_priority, td_priority)
        
        # 注意力增强优先级
        attention_priority = self._compute_attention_priority(experience)
        
        # 融合优先级
        final_priority = (1 - self.attention_priority_weight) * base_priority + \
                        self.attention_priority_weight * attention_priority
        
        return final_priority
    
    def _compute_attention_priority(self, experience: MultiAgentExperience) -> float:
        """
        基于注意力权重计算优先级
        
        Args:
            experience: 经验样本
            
        Returns:
            attention_priority: 注意力优先级
        """
        priority = self.max_priority  # 默认优先级
        
        # 任务注意力优先级
        if experience.task_attention_weights is not None:
            task_attention_variance = 0.0
            for agent_id, weights in experience.task_attention_weights.items():
                if weights is not None and len(weights) > 0:
                    # 计算注意力权重的方差，高方差表示重要决策
                    variance = np.var(weights)
                    task_attention_variance += variance
            
            # 归一化任务注意力优先级
            task_priority = min(task_attention_variance * 10, self.max_priority)
        else:
            task_priority = 0.0
        
        # 协作注意力优先级
        if experience.collaboration_attention_weights is not None:
            collab_attention_max = 0.0
            for agent_id, weights in experience.collaboration_attention_weights.items():
                if weights is not None and len(weights) > 0:
                    # 计算最大协作权重，高权重表示重要协作
                    max_weight = np.max(weights)
                    collab_attention_max = max(collab_attention_max, max_weight)
            
            # 归一化协作注意力优先级
            collab_priority = min(collab_attention_max * self.max_priority, self.max_priority)
        else:
            collab_priority = 0.0
        
        # 奖励优先级
        reward_priority = 0.0
        if experience.rewards is not None:
            total_reward = sum(experience.rewards.values())
            # 高奖励（正向或负向）的经验更重要
            reward_priority = min(abs(total_reward) * 0.1, self.max_priority)
        
        # 融合不同类型的注意力优先级
        attention_priority = max(task_priority, collab_priority, reward_priority, priority * 0.1)
        
        return attention_priority
    
    def _compute_importance_sampling_weights(self, priorities: List[float]) -> np.ndarray:
        """
        计算重要性采样权重
        
        Args:
            priorities: 优先级列表
            
        Returns:
            weights: 重要性采样权重
        """
        # 计算采样概率
        total_priority = self.tree.total()
        sampling_probs = np.array(priorities) / total_priority
        
        # 计算重要性采样权重
        weights = (len(self.tree) * sampling_probs) ** (-self.beta)
        
        # 归一化权重
        max_weight = np.max(weights)
        weights = weights / max_weight
        
        return weights.astype(np.float32)
    
    def get_max_weight(self) -> float:
        """
        获取最大重要性采样权重
        
        Returns:
            max_weight: 最大权重
        """
        if len(self.tree) == 0:
            return 1.0
        
        min_priority = self.tree.get_min_priority()
        total_priority = self.tree.total()
        
        if min_priority <= 0:
            return 1.0
        
        min_prob = min_priority / total_priority
        max_weight = (len(self.tree) * min_prob) ** (-self.beta)
        
        return max_weight
    
    def __len__(self) -> int:
        """
        获取缓冲区大小
        
        Returns:
            size: 当前大小
        """
        return len(self.tree)
    
    def is_ready(self, batch_size: int) -> bool:
        """
        检查是否准备好采样
        
        Args:
            batch_size: 批次大小
            
        Returns:
            ready: 是否准备好
        """
        return len(self.tree) >= batch_size
    
    def clear(self):
        """清空缓冲区"""
        self.tree = SumTree(self.capacity)
        self.total_added = 0
        self.total_sampled = 0
        self.last_sampled_indices = []
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓冲区统计信息
        
        Returns:
            stats: 统计信息字典
        """
        tree_stats = self.tree.get_stats()
        
        return {
            'capacity': self.capacity,
            'size': len(self.tree),
            'utilization': len(self.tree) / self.capacity,
            'total_added': self.total_added,
            'total_sampled': self.total_sampled,
            'alpha': self.alpha,
            'beta': self.beta,
            'max_priority': self.max_priority,
            'tree_stats': tree_stats,
            'avg_priority': tree_stats['mean_priority'],
            'priority_range': tree_stats['max_priority'] - tree_stats['min_priority']
        }
    
    def save_state(self) -> Dict[str, Any]:
        """
        保存缓冲区状态
        
        Returns:
            state: 状态字典
        """
        return {
            'alpha': self.alpha,
            'beta': self.beta,
            'max_priority': self.max_priority,
            'total_added': self.total_added,
            'total_sampled': self.total_sampled,
            'tree_data': self.tree.data[:len(self.tree)].tolist(),
            'tree_priorities': self.tree.tree[self.tree.capacity-1:self.tree.capacity-1+len(self.tree)].tolist()
        }
    
    def load_state(self, state: Dict[str, Any]):
        """
        加载缓冲区状态
        
        Args:
            state: 状态字典
        """
        self.alpha = state['alpha']
        self.beta = state['beta']
        self.max_priority = state['max_priority']
        self.total_added = state['total_added']
        self.total_sampled = state['total_sampled']
        
        # 重建SumTree
        self.tree = SumTree(self.capacity)
        
        # 恢复数据
        tree_data = state['tree_data']
        tree_priorities = state['tree_priorities']
        
        for i, (data, priority) in enumerate(zip(tree_data, tree_priorities)):
            self.tree.add(priority, data)


class MultiAgentPrioritizedReplayManager:
    """
    多智能体优先级经验回放管理器
    管理多个智能体的经验回放和优先级更新
    """

    def __init__(self,
                 capacity: int,
                 alpha: float = 0.6,
                 beta: float = 0.4,
                 beta_increment: float = 0.001,
                 epsilon: float = 1e-6,
                 attention_priority_weight: float = 0.3,
                 global_priority_weight: float = 0.7):
        """
        初始化多智能体优先级经验回放管理器

        Args:
            capacity: 缓冲区容量
            alpha: 优先级指数
            beta: 重要性采样指数
            beta_increment: beta增长率
            epsilon: 防止零优先级的小常数
            attention_priority_weight: 注意力权重在优先级计算中的比重
            global_priority_weight: 全局优先级权重
        """
        self.capacity = capacity
        self.alpha = alpha
        self.beta = beta
        self.beta_increment = beta_increment
        self.epsilon = epsilon
        self.attention_priority_weight = attention_priority_weight
        self.global_priority_weight = global_priority_weight

        # 创建主缓冲区
        self.buffer = PrioritizedReplayBuffer(
            capacity=capacity,
            alpha=alpha,
            beta=beta,
            beta_increment=beta_increment,
            epsilon=epsilon,
            attention_priority_weight=attention_priority_weight
        )

        # 统计信息
        self.episode_count = 0
        self.step_count = 0
        self.priority_update_count = 0

        # 经验质量评估
        self.experience_quality_history = []
        self.attention_diversity_history = []

    def add_experience(self,
                      observations: Dict[str, np.ndarray],
                      actions: Dict[str, Union[int, np.ndarray]],
                      rewards: Dict[str, float],
                      next_observations: Dict[str, np.ndarray],
                      dones: Dict[str, bool],
                      infos: Dict[str, Dict],
                      task_attention_weights: Optional[Dict[str, np.ndarray]] = None,
                      collaboration_attention_weights: Optional[Dict[str, np.ndarray]] = None,
                      td_errors: Optional[Dict[str, float]] = None):
        """
        添加多智能体经验

        Args:
            observations: 观察字典
            actions: 动作字典
            rewards: 奖励字典
            next_observations: 下一状态观察字典
            dones: 完成标志字典
            infos: 信息字典
            task_attention_weights: 任务注意力权重
            collaboration_attention_weights: 协作注意力权重
            td_errors: TD误差字典
        """
        # 计算全局TD误差
        global_td_error = None
        if td_errors is not None:
            global_td_error = np.mean(list(td_errors.values()))

        # 创建经验样本
        experience = MultiAgentExperience(
            observations=observations,
            actions=actions,
            rewards=rewards,
            next_observations=next_observations,
            dones=dones,
            infos=infos,
            task_attention_weights=task_attention_weights,
            collaboration_attention_weights=collaboration_attention_weights,
            td_errors=td_errors,
            global_td_error=global_td_error,
            timestamp=self.step_count,
            episode_id=self.episode_count,
            step_id=self.step_count
        )

        # 添加到缓冲区
        self.buffer.add(experience)

        # 更新统计
        self.step_count += 1

        # 评估经验质量
        self._evaluate_experience_quality(experience)

    def sample_batch(self, batch_size: int) -> Tuple[Dict[str, torch.Tensor], torch.Tensor, List[int]]:
        """
        采样经验批次并转换为张量格式

        Args:
            batch_size: 批次大小

        Returns:
            batch_data: 批次数据字典
            weights: 重要性采样权重
            indices: 采样索引
        """
        if not self.buffer.is_ready(batch_size):
            raise ValueError(f"缓冲区大小不足，需要至少{batch_size}个样本")

        # 从缓冲区采样
        experiences, weights, indices = self.buffer.sample(batch_size)

        # 转换为张量格式
        batch_data = self._convert_to_tensors(experiences)
        weights_tensor = torch.FloatTensor(weights)

        return batch_data, weights_tensor, indices

    def update_priorities(self, indices: List[int], td_errors: Union[List[float], np.ndarray]):
        """
        更新经验优先级

        Args:
            indices: 经验索引
            td_errors: TD误差
        """
        self.buffer.update_priorities(indices, td_errors)
        self.priority_update_count += 1

    def _convert_to_tensors(self, experiences: List[MultiAgentExperience]) -> Dict[str, torch.Tensor]:
        """
        将经验列表转换为张量格式

        Args:
            experiences: 经验列表

        Returns:
            batch_data: 张量格式的批次数据
        """
        batch_size = len(experiences)

        # 获取智能体列表
        agent_ids = list(experiences[0].observations.keys())

        # 初始化批次数据
        batch_data = {
            'observations': {},
            'actions': {},
            'rewards': {},
            'next_observations': {},
            'dones': {},
            'task_attention_weights': {},
            'collaboration_attention_weights': {}
        }

        # 为每个智能体收集数据
        for agent_id in agent_ids:
            # 观察
            obs_list = [exp.observations[agent_id] for exp in experiences]
            batch_data['observations'][agent_id] = torch.FloatTensor(np.array(obs_list))

            # 动作
            actions_list = [exp.actions[agent_id] for exp in experiences]
            if isinstance(actions_list[0], int):
                batch_data['actions'][agent_id] = torch.LongTensor(actions_list)
            else:
                batch_data['actions'][agent_id] = torch.FloatTensor(np.array(actions_list))

            # 奖励
            rewards_list = [exp.rewards[agent_id] for exp in experiences]
            batch_data['rewards'][agent_id] = torch.FloatTensor(rewards_list)

            # 下一状态观察
            next_obs_list = [exp.next_observations[agent_id] for exp in experiences]
            batch_data['next_observations'][agent_id] = torch.FloatTensor(np.array(next_obs_list))

            # 完成标志
            dones_list = [exp.dones[agent_id] for exp in experiences]
            batch_data['dones'][agent_id] = torch.BoolTensor(dones_list)

            # 注意力权重（如果存在）
            if experiences[0].task_attention_weights is not None:
                task_weights_list = [exp.task_attention_weights.get(agent_id, np.array([]))
                                   for exp in experiences]
                if len(task_weights_list[0]) > 0:
                    batch_data['task_attention_weights'][agent_id] = torch.FloatTensor(np.array(task_weights_list))

            if experiences[0].collaboration_attention_weights is not None:
                collab_weights_list = [exp.collaboration_attention_weights.get(agent_id, np.array([]))
                                     for exp in experiences]
                if len(collab_weights_list[0]) > 0:
                    batch_data['collaboration_attention_weights'][agent_id] = torch.FloatTensor(np.array(collab_weights_list))

        return batch_data

    def _evaluate_experience_quality(self, experience: MultiAgentExperience):
        """
        评估经验质量

        Args:
            experience: 经验样本
        """
        # 计算奖励多样性
        rewards = list(experience.rewards.values())
        reward_variance = np.var(rewards) if len(rewards) > 1 else 0.0

        # 计算注意力多样性
        attention_diversity = 0.0
        if experience.task_attention_weights is not None:
            for agent_id, weights in experience.task_attention_weights.items():
                if weights is not None and len(weights) > 0:
                    attention_diversity += np.var(weights)

        # 记录质量指标
        quality_score = reward_variance + attention_diversity * 0.1
        self.experience_quality_history.append(quality_score)
        self.attention_diversity_history.append(attention_diversity)

        # 保持历史记录长度
        max_history = 1000
        if len(self.experience_quality_history) > max_history:
            self.experience_quality_history = self.experience_quality_history[-max_history:]
            self.attention_diversity_history = self.attention_diversity_history[-max_history:]

    def end_episode(self):
        """结束当前回合"""
        self.episode_count += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        获取管理器统计信息

        Returns:
            stats: 统计信息字典
        """
        buffer_stats = self.buffer.get_stats()

        quality_stats = {}
        if self.experience_quality_history:
            quality_stats = {
                'mean_experience_quality': np.mean(self.experience_quality_history),
                'std_experience_quality': np.std(self.experience_quality_history),
                'mean_attention_diversity': np.mean(self.attention_diversity_history),
                'std_attention_diversity': np.std(self.attention_diversity_history)
            }

        return {
            'episode_count': self.episode_count,
            'step_count': self.step_count,
            'priority_update_count': self.priority_update_count,
            'buffer_stats': buffer_stats,
            'quality_stats': quality_stats,
            'alpha': self.alpha,
            'beta': self.beta,
            'attention_priority_weight': self.attention_priority_weight,
            'global_priority_weight': self.global_priority_weight
        }

    def clear(self):
        """清空管理器"""
        self.buffer.clear()
        self.episode_count = 0
        self.step_count = 0
        self.priority_update_count = 0
        self.experience_quality_history = []
        self.attention_diversity_history = []
