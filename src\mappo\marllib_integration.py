"""
MARLlib集成模块
将自定义AGV环境和注意力增强MAPPO集成到MARLlib框架
"""

import os
import sys
import numpy as np
from typing import Dict, Any, Optional, List
import ray
from ray import tune
from ray.rllib.models import ModelCatalog
from ray.rllib.env.env_context import EnvContext

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .agv_env_adapter import AGVMARLlibEnv
from .attention_enhanced_mappo import AttentionEnhancedMAPPOModel, AttentionEnhancedMAPPOTrainer
from .training_visualizer import TrainingVisualizer
from config.env_config import EnvironmentConfig, CURRICULUM_CONFIGS


def register_agv_env():
    """注册AGV环境到MARLlib"""
    from ray.tune.registry import register_env
    
    def env_creator(env_config):
        return AGVMARLlibEnv(env_config)
    
    register_env("agv_warehouse", env_creator)
    print("✓ AGV环境已注册到MARLlib")


def register_attention_model():
    """注册注意力增强模型到MARLlib"""
    ModelCatalog.register_custom_model("attention_enhanced_mappo", AttentionEnhancedMAPPOModel)
    print("✓ 注意力增强MAPPO模型已注册到MARLlib")


class MARLlibAGVTrainer:
    """
    基于MARLlib的AGV训练器
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化MARLlib AGV训练器
        
        Args:
            config: 训练配置
        """
        self.config = config or {}
        self.env_config = EnvironmentConfig()
        
        # 注册环境和模型
        register_agv_env()
        register_attention_model()
        
        # 初始化Ray
        if not ray.is_initialized():
            ray.init(
                ignore_reinit_error=True,
                num_gpus=0,  # 明确指定不使用GPU
                num_cpus=4   # 明确指定CPU数量
            )
        
        # 创建注意力增强MAPPO训练器
        self.mappo_trainer = AttentionEnhancedMAPPOTrainer(self.config)
        
        print("✓ MARLlib AGV训练器初始化完成")
    
    def create_env_config(self, stage: str = "stage3") -> Dict[str, Any]:
        """
        创建环境配置
        
        Args:
            stage: 课程学习阶段
            
        Returns:
            env_config: 环境配置字典
        """
        if stage in CURRICULUM_CONFIGS:
            stage_config = CURRICULUM_CONFIGS[stage]
            env_config = {
                "num_agvs": stage_config.num_agvs,
                "num_tasks": stage_config.num_tasks,
                "max_episode_steps": stage_config.max_episode_steps,
                "map_name": f"warehouse_{stage}",
                "curriculum_stage": stage
            }
        else:
            env_config = {
                "num_agvs": self.env_config.num_agvs,
                "num_tasks": self.env_config.num_tasks,
                "max_episode_steps": self.env_config.max_episode_steps,
                "map_name": "warehouse_default",
                "curriculum_stage": "default"
            }
        
        return env_config
    
    def create_mappo_config(self, env_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建MAPPO配置
        
        Args:
            env_config: 环境配置
            
        Returns:
            mappo_config: MAPPO配置字典
        """
        # 基础MAPPO配置
        base_config = self.mappo_trainer.create_training_config()
        
        # 环境配置
        base_config.update({
            "env": "agv_warehouse",
            "env_config": env_config,
            
            # 训练配置
            "num_workers": self.config.get("num_workers", 4),
            "num_envs_per_env_runner": self.config.get("num_envs_per_worker", 1),  # 更新为新的参数名
            "rollout_fragment_length": self.config.get("rollout_fragment_length", 200),
            "train_batch_size": self.config.get("train_batch_size", 4000),
            
            # 评估配置
            "evaluation_interval": self.config.get("evaluation_interval", 10),
            "evaluation_duration": self.config.get("evaluation_num_episodes", 10),  # 更新为新的参数名
            "evaluation_config": {
                "explore": False,
                "env_config": env_config
            },
            
            # 检查点配置
            "checkpoint_freq": self.config.get("checkpoint_freq", 50),
            "keep_checkpoints_num": self.config.get("keep_checkpoints_num", 5),
            
            # 日志配置
            "log_level": "INFO",
            "callbacks": None  # 可以添加自定义回调
        })
        
        return base_config
    
    def train(self,
              stage: str = "stage3",
              num_iterations: int = 1000,
              checkpoint_path: Optional[str] = None,
              experiment_name: Optional[str] = None,
              enable_visualization: bool = True) -> str:
        """
        开始训练

        Args:
            stage: 课程学习阶段
            num_iterations: 训练迭代次数
            checkpoint_path: 检查点路径（用于恢复训练）
            experiment_name: 实验名称
            enable_visualization: 是否启用实时可视化

        Returns:
            checkpoint_path: 最终检查点路径
        """
        print(f"\n开始训练 - 阶段: {stage}, 迭代次数: {num_iterations}")
        
        # 创建配置
        env_config = self.create_env_config(stage)
        mappo_config = self.create_mappo_config(env_config)
        
        # 设置实验名称
        if experiment_name is None:
            experiment_name = f"agv_mappo_{stage}"
        
        # 配置训练停止条件
        stop_config = {
            "training_iteration": num_iterations,
            "timesteps_total": self.config.get("max_timesteps", 1000000),
            "episode_reward_mean": self.config.get("target_reward", 1000)
        }
        
        # 使用Ray Tune进行训练
        try:
            # 尝试新版本的导入
            try:
                from ray.rllib.algorithms.ppo import PPO as PPOTrainer
            except ImportError:
                # 回退到旧版本的导入
                from ray.rllib.agents.ppo import PPOTrainer
            
            # 创建训练器配置对象
            from ray.rllib.algorithms.ppo import PPOConfig
            config_obj = PPOConfig()

            # 禁用新API栈以支持自定义模型
            config_obj.api_stack(
                enable_rl_module_and_learner=False,
                enable_env_runner_and_connector_v2=False
            )

            # 更新配置
            config_obj.update_from_dict(mappo_config)

            # 创建训练器
            trainer = config_obj.build()
            
            # 如果有检查点，恢复训练
            if checkpoint_path and os.path.exists(checkpoint_path):
                trainer.restore(checkpoint_path)
                print(f"✓ 从检查点恢复训练: {checkpoint_path}")
            
            # 初始化可视化器
            visualizer = None
            if enable_visualization:
                output_dir = self.config.get("output_dir", "./results")
                visualizer = TrainingVisualizer(output_dir, update_interval=10)
                print("✓ 训练可视化器已启用")

            # 训练循环
            best_reward = float('-inf')
            best_checkpoint = None
            episode_count = 0

            for iteration in range(num_iterations):
                # 训练一步
                result = trainer.train()

                # 记录训练信息
                episode_reward_mean = result.get("episode_reward_mean", 0)
                timesteps_total = result.get("timesteps_total", 0)

                # 更新episode计数
                episodes_this_iter = result.get("env_runners", {}).get("episodes_this_iter", 0)
                episode_count += episodes_this_iter

                # 可视化数据收集和更新
                if visualizer and episodes_this_iter > 0:
                    # 添加训练指标数据
                    visualizer.add_training_data(episode_count, result)

                    # 尝试获取AGV性能数据
                    try:
                        # 从环境中获取AGV性能指标
                        env_info = result.get("env_runners", {}).get("custom_metrics", {})
                        if env_info:
                            visualizer.add_agv_data(episode_count, env_info)
                        else:
                            # 使用模拟数据作为示例
                            agv_metrics = {
                                'task_completion_rate': min(episode_reward_mean / 100.0, 1.0),
                                'load_utilization': 0.7 + np.random.normal(0, 0.1),
                                'collision_count': max(0, int(np.random.poisson(2))),
                                'avg_path_length': 50 + np.random.normal(0, 10)
                            }
                            visualizer.add_agv_data(episode_count, agv_metrics)
                    except Exception as e:
                        pass  # 静默处理可视化错误

                    # 检查是否需要更新图表
                    if visualizer.should_update(episode_count):
                        try:
                            visualizer.update_charts(episode_count)
                        except Exception as e:
                            print(f"⚠️ 可视化更新失败: {e}")

                if iteration % 10 == 0:
                    print(f"Iteration {iteration}: "
                          f"Reward={episode_reward_mean:.2f}, "
                          f"Timesteps={timesteps_total}, "
                          f"Episodes={episode_count}")

                # 保存最佳模型
                if episode_reward_mean > best_reward:
                    best_reward = episode_reward_mean
                    best_checkpoint = trainer.save()
                    print(f"✓ 新的最佳模型保存: {best_checkpoint}, 奖励: {best_reward:.2f}")
                
                # 检查停止条件
                if (episode_reward_mean >= stop_config.get("episode_reward_mean", float('inf')) or
                    timesteps_total >= stop_config.get("timesteps_total", float('inf'))):
                    print(f"✓ 达到停止条件，训练完成")
                    break
            
            # 保存最终模型
            final_checkpoint = trainer.save()
            print(f"✓ 训练完成，最终模型保存: {final_checkpoint}")

            # 生成最终可视化图表
            if visualizer and episode_count > 0:
                try:
                    visualizer.update_charts(episode_count)
                    print(f"✓ 最终可视化图表已生成")
                except Exception as e:
                    print(f"⚠️ 最终可视化生成失败: {e}")
                finally:
                    visualizer.close()

            trainer.stop()

            return best_checkpoint or final_checkpoint
            
        except Exception as e:
            print(f"❌ 训练过程中出现错误: {e}")
            raise
    
    def evaluate(self, checkpoint_path: str, num_episodes: int = 10, render: bool = False) -> Dict[str, float]:
        """
        评估训练好的模型
        
        Args:
            checkpoint_path: 检查点路径
            num_episodes: 评估回合数
            render: 是否渲染
            
        Returns:
            evaluation_results: 评估结果
        """
        print(f"\n开始评估模型: {checkpoint_path}")
        
        try:
            # 尝试新版本的导入
            try:
                from ray.rllib.algorithms.ppo import PPO as PPOTrainer
            except ImportError:
                # 回退到旧版本的导入
                from ray.rllib.agents.ppo import PPOTrainer
            
            # 创建评估配置
            env_config = self.create_env_config("stage3")  # 使用最复杂的阶段进行评估
            eval_config = self.create_mappo_config(env_config)
            eval_config["explore"] = False  # 评估时不探索
            
            # 创建训练器并加载模型
            trainer = PPOTrainer(config=eval_config)
            trainer.restore(checkpoint_path)
            
            # 创建环境
            env = AGVMARLlibEnv(env_config)
            
            # 评估循环
            episode_rewards = []
            episode_lengths = []
            success_rates = []
            
            for episode in range(num_episodes):
                obs = env.reset()
                episode_reward = 0
                episode_length = 0
                done = False
                
                while not done:
                    # 获取动作
                    actions = {}
                    for agent_id in env.agents:
                        if agent_id in obs:
                            action = trainer.compute_action(obs[agent_id], policy_id="default_policy")
                            actions[agent_id] = action
                    
                    # 执行动作
                    obs, rewards, dones, infos = env.step(actions)
                    
                    # 累积奖励
                    episode_reward += sum(rewards.values())
                    episode_length += 1
                    
                    # 检查完成
                    done = dones.get("__all__", False)
                    
                    # 渲染
                    if render:
                        env.render()
                
                # 记录结果
                episode_rewards.append(episode_reward)
                episode_lengths.append(episode_length)
                
                # 计算成功率（基于任务完成情况）
                success = episode_reward > 0  # 简化的成功判断
                success_rates.append(1.0 if success else 0.0)
                
                print(f"Episode {episode + 1}: Reward={episode_reward:.2f}, Length={episode_length}")
            
            # 计算统计结果
            results = {
                "mean_reward": np.mean(episode_rewards),
                "std_reward": np.std(episode_rewards),
                "mean_length": np.mean(episode_lengths),
                "success_rate": np.mean(success_rates),
                "min_reward": np.min(episode_rewards),
                "max_reward": np.max(episode_rewards)
            }
            
            print(f"\n评估结果:")
            for key, value in results.items():
                print(f"  {key}: {value:.4f}")
            
            env.close()
            trainer.stop()
            
            return results
            
        except Exception as e:
            print(f"❌ 评估过程中出现错误: {e}")
            raise
    
    def curriculum_training(self, stages: List[str] = None, iterations_per_stage: int = 300) -> Dict[str, str]:
        """
        课程学习训练
        
        Args:
            stages: 训练阶段列表
            iterations_per_stage: 每个阶段的迭代次数
            
        Returns:
            checkpoints: 每个阶段的检查点路径
        """
        if stages is None:
            stages = ["stage1", "stage2", "stage3"]
        
        print(f"\n开始课程学习训练: {stages}")
        
        checkpoints = {}
        previous_checkpoint = None
        
        for stage in stages:
            print(f"\n{'='*50}")
            print(f"开始训练阶段: {stage}")
            print(f"{'='*50}")
            
            # 训练当前阶段
            checkpoint = self.train(
                stage=stage,
                num_iterations=iterations_per_stage,
                checkpoint_path=previous_checkpoint,
                experiment_name=f"agv_curriculum_{stage}"
            )
            
            checkpoints[stage] = checkpoint
            previous_checkpoint = checkpoint
            
            print(f"✓ 阶段 {stage} 训练完成: {checkpoint}")
        
        print(f"\n✅ 课程学习训练完成!")
        print("各阶段检查点:")
        for stage, checkpoint in checkpoints.items():
            print(f"  {stage}: {checkpoint}")
        
        return checkpoints
    
    def close(self):
        """关闭训练器"""
        if ray.is_initialized():
            ray.shutdown()
        print("✓ MARLlib AGV训练器已关闭")
