"""
注意力权重可视化模块
专门用于可视化双层注意力机制的权重分布和模式
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
import io
import base64

from .performance_monitor import AttentionWeights


class AttentionVisualizer:
    """注意力权重可视化器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """
        初始化可视化器
        
        Args:
            figsize: 图形大小
        """
        self.figsize = figsize
        
        # 设置样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        print("✓ 注意力可视化器初始化完成")
    
    def plot_task_attention_heatmap(self, attention_weights: AttentionWeights, 
                                   save_path: Optional[str] = None) -> go.Figure:
        """
        绘制任务分配注意力热力图
        
        Args:
            attention_weights: 注意力权重数据
            save_path: 保存路径
            
        Returns:
            fig: Plotly图形对象
        """
        try:
            weights = attention_weights.task_attention_weights
            
            if weights.size == 0:
                print("⚠️ 任务注意力权重数据为空")
                return go.Figure()
            
            # 创建热力图
            fig = go.Figure(data=go.Heatmap(
                z=weights,
                x=[f"Task {i}" for i in range(weights.shape[1])],
                y=[f"AGV {i}" for i in range(weights.shape[0])],
                colorscale='Viridis',
                colorbar=dict(title="Attention Weight"),
                hoverongaps=False
            ))
            
            fig.update_layout(
                title=f"任务分配注意力权重 - Episode {attention_weights.episode}, Step {attention_weights.step}",
                xaxis_title="任务",
                yaxis_title="AGV",
                width=800,
                height=600
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
            
        except Exception as e:
            print(f"❌ 绘制任务注意力热力图时出错: {e}")
            return go.Figure()
    
    def plot_collaboration_attention_network(self, attention_weights: AttentionWeights,
                                           agv_positions: Optional[List[Tuple[int, int]]] = None,
                                           save_path: Optional[str] = None) -> go.Figure:
        """
        绘制协作注意力网络图
        
        Args:
            attention_weights: 注意力权重数据
            agv_positions: AGV位置信息
            save_path: 保存路径
            
        Returns:
            fig: Plotly图形对象
        """
        try:
            weights = attention_weights.collaboration_weights
            
            if weights.size == 0:
                print("⚠️ 协作注意力权重数据为空")
                return go.Figure()
            
            num_agvs = weights.shape[0]
            
            # 如果没有提供位置信息，生成圆形布局
            if agv_positions is None:
                angles = np.linspace(0, 2*np.pi, num_agvs, endpoint=False)
                agv_positions = [(np.cos(angle), np.sin(angle)) for angle in angles]
            
            # 创建节点
            node_x = [pos[0] for pos in agv_positions]
            node_y = [pos[1] for pos in agv_positions]
            
            # 创建边（只显示权重大于阈值的连接）
            threshold = np.mean(weights) + np.std(weights)
            edge_x = []
            edge_y = []
            edge_weights = []
            
            for i in range(num_agvs):
                for j in range(num_agvs):
                    if i != j and weights[i, j] > threshold:
                        edge_x.extend([node_x[i], node_x[j], None])
                        edge_y.extend([node_y[i], node_y[j], None])
                        edge_weights.append(weights[i, j])
            
            # 创建图形
            fig = go.Figure()
            
            # 添加边
            if edge_x:
                fig.add_trace(go.Scatter(
                    x=edge_x, y=edge_y,
                    line=dict(width=2, color='rgba(125,125,125,0.5)'),
                    hoverinfo='none',
                    mode='lines',
                    name='协作连接'
                ))
            
            # 添加节点
            fig.add_trace(go.Scatter(
                x=node_x, y=node_y,
                mode='markers+text',
                marker=dict(
                    size=20,
                    color='lightblue',
                    line=dict(width=2, color='darkblue')
                ),
                text=[f"AGV {i}" for i in range(num_agvs)],
                textposition="middle center",
                hoverinfo='text',
                hovertext=[f"AGV {i}<br>位置: {agv_positions[i]}" for i in range(num_agvs)],
                name='AGV节点'
            ))
            
            fig.update_layout(
                title=f"协作注意力网络 - Episode {attention_weights.episode}, Step {attention_weights.step}",
                showlegend=True,
                hovermode='closest',
                margin=dict(b=20,l=5,r=5,t=40),
                annotations=[
                    dict(
                        text=f"阈值: {threshold:.3f}",
                        showarrow=False,
                        xref="paper", yref="paper",
                        x=0.005, y=-0.002,
                        xanchor='left', yanchor='bottom',
                        font=dict(size=12)
                    )
                ],
                xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                width=800,
                height=600
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
            
        except Exception as e:
            print(f"❌ 绘制协作注意力网络图时出错: {e}")
            return go.Figure()
    
    def plot_attention_evolution(self, attention_history: List[AttentionWeights],
                                attention_type: str = 'task',
                                save_path: Optional[str] = None) -> go.Figure:
        """
        绘制注意力权重演化图
        
        Args:
            attention_history: 注意力权重历史数据
            attention_type: 注意力类型 ('task' 或 'collaboration')
            save_path: 保存路径
            
        Returns:
            fig: Plotly图形对象
        """
        try:
            if not attention_history:
                print("⚠️ 注意力历史数据为空")
                return go.Figure()
            
            # 提取时间序列数据
            timestamps = [aw.timestamp for aw in attention_history]
            episodes = [aw.episode for aw in attention_history]
            
            if attention_type == 'task':
                # 计算任务注意力的统计指标
                entropies = []
                max_weights = []
                sparsities = []
                
                for aw in attention_history:
                    weights = aw.task_attention_weights
                    if weights.size > 0:
                        # 计算熵
                        entropy = -np.sum(weights * np.log(weights + 1e-8))
                        entropies.append(entropy)
                        
                        # 最大权重
                        max_weights.append(np.max(weights))
                        
                        # 稀疏性（权重小于阈值的比例）
                        sparsity = np.mean(weights < 0.1)
                        sparsities.append(sparsity)
                    else:
                        entropies.append(0)
                        max_weights.append(0)
                        sparsities.append(0)
                
                # 创建子图
                fig = make_subplots(
                    rows=3, cols=1,
                    subplot_titles=('注意力熵', '最大注意力权重', '稀疏性'),
                    vertical_spacing=0.1
                )
                
                # 添加轨迹
                fig.add_trace(go.Scatter(x=episodes, y=entropies, name='熵', line=dict(color='blue')), row=1, col=1)
                fig.add_trace(go.Scatter(x=episodes, y=max_weights, name='最大权重', line=dict(color='red')), row=2, col=1)
                fig.add_trace(go.Scatter(x=episodes, y=sparsities, name='稀疏性', line=dict(color='green')), row=3, col=1)
                
                title = "任务分配注意力演化"
                
            else:  # collaboration
                # 计算协作注意力的统计指标
                avg_weights = []
                connection_counts = []
                
                for aw in attention_history:
                    weights = aw.collaboration_weights
                    if weights.size > 0:
                        # 平均权重（排除对角线）
                        mask = ~np.eye(weights.shape[0], dtype=bool)
                        avg_weight = np.mean(weights[mask])
                        avg_weights.append(avg_weight)
                        
                        # 连接数量（权重大于阈值）
                        threshold = 0.1
                        connections = np.sum(weights[mask] > threshold)
                        connection_counts.append(connections)
                    else:
                        avg_weights.append(0)
                        connection_counts.append(0)
                
                # 创建子图
                fig = make_subplots(
                    rows=2, cols=1,
                    subplot_titles=('平均协作权重', '协作连接数量'),
                    vertical_spacing=0.15
                )
                
                # 添加轨迹
                fig.add_trace(go.Scatter(x=episodes, y=avg_weights, name='平均权重', line=dict(color='purple')), row=1, col=1)
                fig.add_trace(go.Scatter(x=episodes, y=connection_counts, name='连接数量', line=dict(color='orange')), row=2, col=1)
                
                title = "协作感知注意力演化"
            
            fig.update_layout(
                title=title,
                height=800,
                showlegend=False
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
            
        except Exception as e:
            print(f"❌ 绘制注意力演化图时出错: {e}")
            return go.Figure()
    
    def plot_temperature_distribution(self, attention_weights: AttentionWeights,
                                    save_path: Optional[str] = None) -> go.Figure:
        """
        绘制自适应温度分布图
        
        Args:
            attention_weights: 注意力权重数据
            save_path: 保存路径
            
        Returns:
            fig: Plotly图形对象
        """
        try:
            temperatures = attention_weights.adaptive_temperatures
            
            if temperatures.size == 0:
                print("⚠️ 温度数据为空")
                return go.Figure()
            
            # 创建柱状图
            fig = go.Figure(data=[
                go.Bar(
                    x=[f"AGV {i}" for i in range(len(temperatures))],
                    y=temperatures,
                    marker_color='lightcoral',
                    text=[f"{temp:.3f}" for temp in temperatures],
                    textposition='auto'
                )
            ])
            
            fig.update_layout(
                title=f"自适应温度分布 - Episode {attention_weights.episode}, Step {attention_weights.step}",
                xaxis_title="AGV",
                yaxis_title="温度值",
                width=800,
                height=400
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
            
        except Exception as e:
            print(f"❌ 绘制温度分布图时出错: {e}")
            return go.Figure()
    
    def create_attention_dashboard(self, attention_weights: AttentionWeights,
                                 agv_positions: Optional[List[Tuple[int, int]]] = None,
                                 save_path: Optional[str] = None) -> go.Figure:
        """
        创建注意力机制综合仪表板
        
        Args:
            attention_weights: 注意力权重数据
            agv_positions: AGV位置信息
            save_path: 保存路径
            
        Returns:
            fig: Plotly图形对象
        """
        try:
            # 创建子图布局
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=(
                    '任务分配注意力热力图',
                    '协作注意力网络',
                    '自适应温度分布',
                    '注意力权重统计'
                ),
                specs=[
                    [{"type": "heatmap"}, {"type": "scatter"}],
                    [{"type": "bar"}, {"type": "bar"}]
                ],
                vertical_spacing=0.1,
                horizontal_spacing=0.1
            )
            
            # 1. 任务分配注意力热力图
            if attention_weights.task_attention_weights.size > 0:
                task_weights = attention_weights.task_attention_weights
                fig.add_trace(
                    go.Heatmap(
                        z=task_weights,
                        x=[f"T{i}" for i in range(task_weights.shape[1])],
                        y=[f"AGV{i}" for i in range(task_weights.shape[0])],
                        colorscale='Viridis',
                        showscale=False
                    ),
                    row=1, col=1
                )
            
            # 2. 协作注意力网络（简化版）
            if attention_weights.collaboration_weights.size > 0:
                collab_weights = attention_weights.collaboration_weights
                num_agvs = collab_weights.shape[0]
                
                # 生成圆形布局
                if agv_positions is None:
                    angles = np.linspace(0, 2*np.pi, num_agvs, endpoint=False)
                    agv_positions = [(np.cos(angle), np.sin(angle)) for angle in angles]
                
                node_x = [pos[0] for pos in agv_positions]
                node_y = [pos[1] for pos in agv_positions]
                
                fig.add_trace(
                    go.Scatter(
                        x=node_x, y=node_y,
                        mode='markers+text',
                        marker=dict(size=15, color='lightblue'),
                        text=[f"A{i}" for i in range(num_agvs)],
                        textposition="middle center",
                        showlegend=False
                    ),
                    row=1, col=2
                )
            
            # 3. 自适应温度分布
            if attention_weights.adaptive_temperatures.size > 0:
                temperatures = attention_weights.adaptive_temperatures
                fig.add_trace(
                    go.Bar(
                        x=[f"AGV{i}" for i in range(len(temperatures))],
                        y=temperatures,
                        marker_color='lightcoral',
                        showlegend=False
                    ),
                    row=2, col=1
                )
            
            # 4. 注意力权重统计
            stats_labels = []
            stats_values = []
            
            if attention_weights.task_attention_weights.size > 0:
                task_weights = attention_weights.task_attention_weights
                stats_labels.extend(['任务注意力均值', '任务注意力最大值', '任务注意力熵'])
                stats_values.extend([
                    np.mean(task_weights),
                    np.max(task_weights),
                    -np.sum(task_weights * np.log(task_weights + 1e-8))
                ])
            
            if attention_weights.collaboration_weights.size > 0:
                collab_weights = attention_weights.collaboration_weights
                mask = ~np.eye(collab_weights.shape[0], dtype=bool)
                stats_labels.extend(['协作注意力均值', '协作连接数'])
                stats_values.extend([
                    np.mean(collab_weights[mask]),
                    np.sum(collab_weights[mask] > 0.1)
                ])
            
            if stats_labels:
                fig.add_trace(
                    go.Bar(
                        x=stats_labels,
                        y=stats_values,
                        marker_color='lightgreen',
                        showlegend=False
                    ),
                    row=2, col=2
                )
            
            # 更新布局
            fig.update_layout(
                title=f"注意力机制综合仪表板 - Episode {attention_weights.episode}, Step {attention_weights.step}",
                height=800,
                width=1200,
                showlegend=False
            )
            
            if save_path:
                fig.write_html(save_path)
            
            return fig
            
        except Exception as e:
            print(f"❌ 创建注意力仪表板时出错: {e}")
            return go.Figure()
    
    def save_attention_summary(self, attention_history: List[AttentionWeights],
                             save_dir: str = "./attention_analysis"):
        """
        保存注意力分析摘要
        
        Args:
            attention_history: 注意力权重历史数据
            save_dir: 保存目录
        """
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        try:
            # 生成各种可视化图表
            if attention_history:
                latest_weights = attention_history[-1]
                
                # 任务注意力热力图
                task_fig = self.plot_task_attention_heatmap(
                    latest_weights, 
                    os.path.join(save_dir, "task_attention_heatmap.html")
                )
                
                # 协作注意力网络
                collab_fig = self.plot_collaboration_attention_network(
                    latest_weights,
                    save_path=os.path.join(save_dir, "collaboration_network.html")
                )
                
                # 注意力演化
                task_evolution_fig = self.plot_attention_evolution(
                    attention_history, 'task',
                    os.path.join(save_dir, "task_attention_evolution.html")
                )
                
                collab_evolution_fig = self.plot_attention_evolution(
                    attention_history, 'collaboration',
                    os.path.join(save_dir, "collaboration_evolution.html")
                )
                
                # 综合仪表板
                dashboard_fig = self.create_attention_dashboard(
                    latest_weights,
                    save_path=os.path.join(save_dir, "attention_dashboard.html")
                )
                
                print(f"✓ 注意力分析摘要已保存到: {save_dir}")
            
        except Exception as e:
            print(f"❌ 保存注意力分析摘要时出错: {e}")
