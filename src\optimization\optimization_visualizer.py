"""
超参数优化可视化工具
生成优化过程和结果的可视化图表
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional
import pandas as pd
from collections import defaultdict

plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False


class OptimizationVisualizer:
    """优化可视化器"""
    
    def __init__(self, figsize: tuple = (12, 8), style: str = "whitegrid"):
        """
        初始化可视化器
        
        Args:
            figsize: 图形大小
            style: 图形样式
        """
        self.figsize = figsize
        sns.set_style(style)
        
    def plot_optimization_history(self, 
                                 optimization_history: List[Dict[str, Any]], 
                                 save_path: str,
                                 show_best: bool = True):
        """
        绘制优化历史图
        
        Args:
            optimization_history: 优化历史数据
            save_path: 保存路径
            show_best: 是否显示最佳值线
        """
        if not optimization_history:
            return
        
        # 提取数据
        trials = [trial['trial'] for trial in optimization_history]
        scores = [trial['score'] for trial in optimization_history]
        
        # 计算累积最佳值
        best_scores = []
        current_best = float('-inf')
        for score in scores:
            if score > current_best:
                current_best = score
            best_scores.append(current_best)
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, sharex=True)
        
        # 上图：所有试验的得分
        ax1.scatter(trials, scores, alpha=0.6, s=30, c='lightblue', label='试验得分')
        if show_best:
            ax1.plot(trials, best_scores, 'r-', linewidth=2, label='累积最佳')
        ax1.set_ylabel('评分')
        ax1.set_title('超参数优化历史')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下图：得分分布直方图
        ax2.hist(scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(np.mean(scores), color='red', linestyle='--', label=f'平均值: {np.mean(scores):.4f}')
        ax2.axvline(np.max(scores), color='green', linestyle='--', label=f'最大值: {np.max(scores):.4f}')
        ax2.set_xlabel('评分')
        ax2.set_ylabel('频次')
        ax2.set_title('评分分布')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_parameter_importance(self, 
                                 optimization_history: List[Dict[str, Any]], 
                                 save_path: str,
                                 top_k: int = 10):
        """
        绘制参数重要性图
        
        Args:
            optimization_history: 优化历史数据
            save_path: 保存路径
            top_k: 显示前k个重要参数
        """
        if not optimization_history:
            return
        
        # 计算参数重要性（基于相关性）
        param_importance = self._calculate_parameter_importance(optimization_history)
        
        if not param_importance:
            return
        
        # 排序并取前k个
        sorted_params = sorted(param_importance.items(), key=lambda x: abs(x[1]), reverse=True)[:top_k]
        
        if not sorted_params:
            return
        
        params, importances = zip(*sorted_params)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # 绘制条形图
        colors = ['red' if imp < 0 else 'green' for imp in importances]
        bars = ax.barh(range(len(params)), importances, color=colors, alpha=0.7)
        
        # 设置标签
        ax.set_yticks(range(len(params)))
        ax.set_yticklabels(params)
        ax.set_xlabel('重要性（相关系数）')
        ax.set_title('超参数重要性分析')
        ax.grid(True, alpha=0.3, axis='x')
        
        # 添加数值标签
        for i, (bar, imp) in enumerate(zip(bars, importances)):
            ax.text(imp + 0.01 if imp >= 0 else imp - 0.01, i, f'{imp:.3f}', 
                   va='center', ha='left' if imp >= 0 else 'right')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_convergence_analysis(self, 
                                 optimization_history: List[Dict[str, Any]], 
                                 save_path: str,
                                 window_size: int = 10):
        """
        绘制收敛分析图
        
        Args:
            optimization_history: 优化历史数据
            save_path: 保存路径
            window_size: 滑动窗口大小
        """
        if not optimization_history or len(optimization_history) < window_size:
            return
        
        # 提取数据
        trials = [trial['trial'] for trial in optimization_history]
        scores = [trial['score'] for trial in optimization_history]
        
        # 计算滑动平均
        moving_avg = []
        moving_std = []
        for i in range(window_size - 1, len(scores)):
            window_scores = scores[i - window_size + 1:i + 1]
            moving_avg.append(np.mean(window_scores))
            moving_std.append(np.std(window_scores))
        
        moving_trials = trials[window_size - 1:]
        
        # 计算累积最佳值
        best_scores = []
        current_best = float('-inf')
        for score in scores:
            if score > current_best:
                current_best = score
            best_scores.append(current_best)
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, sharex=True)
        
        # 上图：收敛趋势
        ax1.plot(trials, scores, 'o-', alpha=0.3, markersize=3, label='原始得分')
        ax1.plot(moving_trials, moving_avg, 'b-', linewidth=2, label=f'{window_size}点滑动平均')
        ax1.plot(trials, best_scores, 'r-', linewidth=2, label='累积最佳')
        
        # 添加置信区间
        if moving_std:
            moving_avg_array = np.array(moving_avg)
            moving_std_array = np.array(moving_std)
            ax1.fill_between(moving_trials, 
                           moving_avg_array - moving_std_array,
                           moving_avg_array + moving_std_array,
                           alpha=0.2, color='blue', label='±1标准差')
        
        ax1.set_ylabel('评分')
        ax1.set_title('收敛分析')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下图：改进率
        improvement_rates = []
        for i in range(1, len(best_scores)):
            if best_scores[i] > best_scores[i-1]:
                improvement_rates.append(1)
            else:
                improvement_rates.append(0)
        
        # 计算滑动改进率
        if len(improvement_rates) >= window_size:
            moving_improvement = []
            for i in range(window_size - 1, len(improvement_rates)):
                window_improvements = improvement_rates[i - window_size + 1:i + 1]
                moving_improvement.append(np.mean(window_improvements))
            
            improvement_trials = trials[window_size:len(improvement_rates) + 1]
            ax2.plot(improvement_trials, moving_improvement, 'g-', linewidth=2, label='改进率')
            ax2.fill_between(improvement_trials, 0, moving_improvement, alpha=0.3, color='green')
        
        ax2.set_xlabel('试验次数')
        ax2.set_ylabel('改进率')
        ax2.set_title('改进率趋势')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_parameter_correlation_matrix(self, 
                                         optimization_history: List[Dict[str, Any]], 
                                         save_path: str):
        """
        绘制参数相关性矩阵
        
        Args:
            optimization_history: 优化历史数据
            save_path: 保存路径
        """
        if not optimization_history:
            return
        
        # 构建数据框
        data = []
        for trial in optimization_history:
            row = trial['params'].copy()
            row['score'] = trial['score']
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # 只保留数值列
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 2:
            return
        
        df_numeric = df[numeric_cols]
        
        # 计算相关性矩阵
        correlation_matrix = df_numeric.corr()
        
        # 创建图形
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # 绘制热图
        sns.heatmap(correlation_matrix, 
                   annot=True, 
                   cmap='RdBu_r', 
                   center=0,
                   square=True,
                   fmt='.3f',
                   ax=ax)
        
        ax.set_title('参数相关性矩阵')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
    def plot_parameter_distribution(self, 
                                   optimization_history: List[Dict[str, Any]], 
                                   save_path: str,
                                   top_k: int = 6):
        """
        绘制参数分布图
        
        Args:
            optimization_history: 优化历史数据
            save_path: 保存路径
            top_k: 显示前k个参数
        """
        if not optimization_history:
            return
        
        # 构建数据框
        data = []
        for trial in optimization_history:
            data.append(trial['params'])
        
        df = pd.DataFrame(data)
        
        # 只保留数值列
        numeric_cols = df.select_dtypes(include=[np.number]).columns[:top_k]
        if len(numeric_cols) == 0:
            return
        
        # 创建子图
        n_cols = min(3, len(numeric_cols))
        n_rows = (len(numeric_cols) + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(n_cols * 4, n_rows * 3))
        if n_rows == 1 and n_cols == 1:
            axes = [axes]
        elif n_rows == 1 or n_cols == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()
        
        # 绘制每个参数的分布
        for i, col in enumerate(numeric_cols):
            if i < len(axes):
                ax = axes[i]
                values = df[col].dropna()
                
                if len(values.unique()) > 10:
                    # 连续变量：直方图
                    ax.hist(values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
                else:
                    # 离散变量：条形图
                    value_counts = values.value_counts().sort_index()
                    ax.bar(range(len(value_counts)), value_counts.values, 
                          alpha=0.7, color='skyblue', edgecolor='black')
                    ax.set_xticks(range(len(value_counts)))
                    ax.set_xticklabels(value_counts.index)
                
                ax.set_title(f'{col} 分布')
                ax.set_xlabel(col)
                ax.set_ylabel('频次')
                ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(len(numeric_cols), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
    def _calculate_parameter_importance(self, 
                                       optimization_history: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算参数重要性"""
        if not optimization_history:
            return {}
        
        # 构建数据框
        data = []
        for trial in optimization_history:
            row = trial['params'].copy()
            row['score'] = trial['score']
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # 只保留数值列
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if 'score' not in numeric_cols or len(numeric_cols) < 2:
            return {}
        
        # 计算与得分的相关性
        correlations = df[numeric_cols].corr()['score'].drop('score')
        
        return correlations.to_dict()
    
    def create_optimization_dashboard(self, 
                                     optimization_history: List[Dict[str, Any]], 
                                     save_dir: str):
        """
        创建优化仪表板（生成所有可视化图表）
        
        Args:
            optimization_history: 优化历史数据
            save_dir: 保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 生成各种图表
        self.plot_optimization_history(
            optimization_history, 
            os.path.join(save_dir, "optimization_history.png")
        )
        
        self.plot_parameter_importance(
            optimization_history, 
            os.path.join(save_dir, "parameter_importance.png")
        )
        
        self.plot_convergence_analysis(
            optimization_history, 
            os.path.join(save_dir, "convergence_analysis.png")
        )
        
        self.plot_parameter_correlation_matrix(
            optimization_history, 
            os.path.join(save_dir, "parameter_correlation.png")
        )
        
        self.plot_parameter_distribution(
            optimization_history, 
            os.path.join(save_dir, "parameter_distribution.png")
        )
        
        print(f"优化仪表板已生成: {save_dir}")
