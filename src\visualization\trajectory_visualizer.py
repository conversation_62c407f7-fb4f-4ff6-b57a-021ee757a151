"""
AGV轨迹可视化器
提供AGV移动轨迹的动态可视化和回放功能
"""

import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Rectangle, Circle, FancyBboxPatch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import json
import os
from datetime import datetime

from .trajectory_collector import TrajectoryCollector, TrajectoryPoint, TaskEvent, CollisionEvent


class TrajectoryVisualizer:
    """AGV轨迹可视化器"""
    
    def __init__(self, map_width: int = 26, map_height: int = 10, 
                 collector: Optional[TrajectoryCollector] = None):
        """
        初始化轨迹可视化器
        
        Args:
            map_width: 地图宽度
            map_height: 地图高度
            collector: 轨迹收集器
        """
        self.map_width = map_width
        self.map_height = map_height
        self.collector = collector
        
        # 可视化配置
        self.agv_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        self.agv_size = 0.3
        self.trail_length = 20  # 轨迹尾迹长度
        
        # 图形对象
        self.fig = None
        self.ax = None
        self.agv_artists = {}
        self.trail_artists = {}
        self.task_artists = {}
        self.collision_artists = {}
        
        print("✓ AGV轨迹可视化器初始化完成")
    
    def setup_plot(self, title: str = "AGV轨迹可视化"):
        """
        设置绘图环境
        
        Args:
            title: 图表标题
        """
        self.fig, self.ax = plt.subplots(figsize=(14, 8))
        self.ax.set_xlim(-0.5, self.map_width - 0.5)
        self.ax.set_ylim(-0.5, self.map_height - 0.5)
        self.ax.set_aspect('equal')
        self.ax.set_title(title, fontsize=16, fontweight='bold')
        self.ax.set_xlabel('X坐标', fontsize=12)
        self.ax.set_ylabel('Y坐标', fontsize=12)
        
        # 绘制网格
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xticks(range(self.map_width))
        self.ax.set_yticks(range(self.map_height))
        
        # 绘制仓储环境布局
        self._draw_warehouse_layout()
        
        return self.fig, self.ax
    
    def _draw_warehouse_layout(self):
        """绘制仓储环境布局"""
        # 绘制货架区域（简化版本）
        shelf_areas = [
            (2, 2, 4, 6),   # (x, y, width, height)
            (8, 2, 4, 6),
            (14, 2, 4, 6),
            (20, 2, 4, 6)
        ]
        
        for x, y, w, h in shelf_areas:
            shelf = Rectangle((x, y), w, h, 
                            facecolor='lightgray', 
                            edgecolor='black', 
                            alpha=0.5,
                            label='货架区域' if x == 2 else "")
            self.ax.add_patch(shelf)
        
        # 绘制装卸区域
        loading_zones = [(0, 0, 2, 2), (0, 8, 2, 2), (24, 0, 2, 2), (24, 8, 2, 2)]
        for x, y, w, h in loading_zones:
            zone = Rectangle((x, y), w, h, 
                           facecolor='lightblue', 
                           edgecolor='blue', 
                           alpha=0.7,
                           label='装卸区域' if x == 0 and y == 0 else "")
            self.ax.add_patch(zone)
        
        # 添加图例
        self.ax.legend(loc='upper right', bbox_to_anchor=(1.15, 1))
    
    def visualize_episode(self, episode_data: Dict[str, Any], 
                         animation_speed: float = 100, save_path: Optional[str] = None):
        """
        可视化完整回合的AGV轨迹
        
        Args:
            episode_data: 回合数据
            animation_speed: 动画速度（毫秒）
            save_path: 保存路径
        """
        if not episode_data:
            print("❌ 回合数据为空")
            return
        
        # 设置绘图环境
        episode_id = episode_data['metadata']['episode_id']
        self.setup_plot(f"AGV轨迹回放 - 回合 {episode_id}")
        
        # 提取轨迹数据
        trajectories = episode_data['trajectories']
        task_events = episode_data['task_events']
        collision_events = episode_data['collision_events']
        
        # 计算最大步数
        max_steps = max(len(traj) for traj in trajectories.values()) if trajectories else 0
        
        if max_steps == 0:
            print("❌ 没有轨迹数据")
            return
        
        # 初始化AGV艺术家对象
        self._init_agv_artists(list(trajectories.keys()))
        
        # 创建动画
        def animate(frame):
            return self._update_frame(frame, trajectories, task_events, collision_events)
        
        anim = animation.FuncAnimation(
            self.fig, animate, frames=max_steps,
            interval=animation_speed, blit=False, repeat=True
        )
        
        # 添加控制信息
        info_text = self.ax.text(0.02, 0.98, "", transform=self.ax.transAxes, 
                               verticalalignment='top', fontsize=10,
                               bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # 保存动画
        if save_path:
            print(f"正在保存动画到: {save_path}")
            anim.save(save_path, writer='pillow', fps=10)
            print("✓ 动画保存完成")
        
        plt.tight_layout()
        plt.show()
        
        return anim
    
    def _init_agv_artists(self, agv_ids: List[int]):
        """初始化AGV艺术家对象"""
        self.agv_artists.clear()
        self.trail_artists.clear()
        
        for i, agv_id in enumerate(agv_ids):
            color = self.agv_colors[i % len(self.agv_colors)]
            
            # AGV主体
            agv_circle = Circle((0, 0), self.agv_size, 
                              facecolor=color, edgecolor='black', 
                              linewidth=2, alpha=0.8)
            self.ax.add_patch(agv_circle)
            
            # AGV标签
            agv_text = self.ax.text(0, 0, f'AGV{agv_id}', 
                                  ha='center', va='center', 
                                  fontsize=8, fontweight='bold', color='white')
            
            # 轨迹尾迹
            trail_line, = self.ax.plot([], [], color=color, alpha=0.6, linewidth=2)
            
            self.agv_artists[agv_id] = {
                'circle': agv_circle,
                'text': agv_text,
                'color': color
            }
            self.trail_artists[agv_id] = trail_line
    
    def _update_frame(self, frame: int, trajectories: Dict[str, List[Dict]], 
                     task_events: List[Dict], collision_events: List[Dict]):
        """更新动画帧"""
        updated_artists = []
        
        # 更新AGV位置和轨迹
        for agv_id_str, traj_data in trajectories.items():
            agv_id = int(agv_id_str)
            
            if frame < len(traj_data):
                point = traj_data[frame]
                x, y = point['position']
                
                # 更新AGV位置
                if agv_id in self.agv_artists:
                    circle = self.agv_artists[agv_id]['circle']
                    text = self.agv_artists[agv_id]['text']
                    
                    circle.center = (x, y)
                    text.set_position((x, y))
                    
                    # 根据状态调整颜色
                    status = point['status']
                    if status == 'LOADING':
                        circle.set_facecolor('yellow')
                    elif status == 'UNLOADING':
                        circle.set_facecolor('cyan')
                    elif status == 'MOVING':
                        circle.set_facecolor(self.agv_artists[agv_id]['color'])
                    else:
                        circle.set_facecolor('lightgray')
                    
                    updated_artists.extend([circle, text])
                
                # 更新轨迹尾迹
                if agv_id in self.trail_artists:
                    start_idx = max(0, frame - self.trail_length)
                    trail_points = traj_data[start_idx:frame+1]
                    
                    if len(trail_points) > 1:
                        trail_x = [p['position'][0] for p in trail_points]
                        trail_y = [p['position'][1] for p in trail_points]
                        
                        self.trail_artists[agv_id].set_data(trail_x, trail_y)
                        updated_artists.append(self.trail_artists[agv_id])
        
        return updated_artists
    
    def visualize_static_trajectory(self, episode_data: Dict[str, Any], 
                                  show_full_paths: bool = True, 
                                  show_task_events: bool = True,
                                  save_path: Optional[str] = None):
        """
        可视化静态轨迹图
        
        Args:
            episode_data: 回合数据
            show_full_paths: 是否显示完整路径
            show_task_events: 是否显示任务事件
            save_path: 保存路径
        """
        if not episode_data:
            print("❌ 回合数据为空")
            return
        
        # 设置绘图环境
        episode_id = episode_data['metadata']['episode_id']
        self.setup_plot(f"AGV轨迹总览 - 回合 {episode_id}")
        
        trajectories = episode_data['trajectories']
        task_events = episode_data['task_events']
        
        # 绘制完整轨迹路径
        if show_full_paths:
            for i, (agv_id_str, traj_data) in enumerate(trajectories.items()):
                agv_id = int(agv_id_str)
                color = self.agv_colors[i % len(self.agv_colors)]
                
                if traj_data:
                    # 提取路径点
                    path_x = [point['position'][0] for point in traj_data]
                    path_y = [point['position'][1] for point in traj_data]
                    
                    # 绘制路径
                    self.ax.plot(path_x, path_y, color=color, alpha=0.7, 
                               linewidth=2, label=f'AGV{agv_id}路径')
                    
                    # 标记起点和终点
                    if len(path_x) > 0:
                        self.ax.scatter(path_x[0], path_y[0], color=color, 
                                      s=100, marker='o', edgecolor='black', 
                                      linewidth=2, label=f'AGV{agv_id}起点' if i == 0 else "")
                        self.ax.scatter(path_x[-1], path_y[-1], color=color, 
                                      s=100, marker='s', edgecolor='black', 
                                      linewidth=2, label=f'AGV{agv_id}终点' if i == 0 else "")
        
        # 绘制任务事件
        if show_task_events and task_events:
            pickup_events = [e for e in task_events if e['event_type'] == 'pickup_completed']
            delivery_events = [e for e in task_events if e['event_type'] == 'delivery_completed']
            
            for event in pickup_events:
                if event['position']:
                    x, y = event['position']
                    self.ax.scatter(x, y, color='green', s=80, marker='^', 
                                  edgecolor='black', alpha=0.8,
                                  label='取货点' if event == pickup_events[0] else "")
            
            for event in delivery_events:
                if event['position']:
                    x, y = event['position']
                    self.ax.scatter(x, y, color='red', s=80, marker='v', 
                                  edgecolor='black', alpha=0.8,
                                  label='送货点' if event == delivery_events[0] else "")
        
        # 添加统计信息
        metadata = episode_data['metadata']
        stats_text = (f"回合 {episode_id} 统计信息:\n"
                     f"总步数: {metadata['total_steps']}\n"
                     f"持续时间: {metadata['duration']:.1f}秒\n"
                     f"AGV数量: {metadata['num_agvs']}\n"
                     f"任务事件: {metadata['num_task_events']}\n"
                     f"碰撞事件: {metadata['num_collision_events']}")
        
        self.ax.text(0.02, 0.02, stats_text, transform=self.ax.transAxes,
                    verticalalignment='bottom', fontsize=9,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.9))
        
        # 更新图例
        self.ax.legend(loc='upper right', bbox_to_anchor=(1.25, 1))
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✓ 静态轨迹图保存到: {save_path}")
        
        plt.tight_layout()
        plt.show()
    
    def create_trajectory_heatmap(self, episode_data: Dict[str, Any], 
                                save_path: Optional[str] = None):
        """
        创建轨迹热力图
        
        Args:
            episode_data: 回合数据
            save_path: 保存路径
        """
        if not episode_data:
            print("❌ 回合数据为空")
            return
        
        # 创建热力图数据
        heatmap_data = np.zeros((self.map_height, self.map_width))
        
        trajectories = episode_data['trajectories']
        for traj_data in trajectories.values():
            for point in traj_data:
                x, y = point['position']
                if 0 <= x < self.map_width and 0 <= y < self.map_height:
                    heatmap_data[y, x] += 1
        
        # 绘制热力图
        fig, ax = plt.subplots(figsize=(12, 6))
        
        im = ax.imshow(heatmap_data, cmap='YlOrRd', aspect='equal', 
                      extent=[-0.5, self.map_width-0.5, -0.5, self.map_height-0.5],
                      origin='lower')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('访问频次', fontsize=12)
        
        # 设置标题和标签
        episode_id = episode_data['metadata']['episode_id']
        ax.set_title(f'AGV轨迹热力图 - 回合 {episode_id}', fontsize=16, fontweight='bold')
        ax.set_xlabel('X坐标', fontsize=12)
        ax.set_ylabel('Y坐标', fontsize=12)
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        ax.set_xticks(range(self.map_width))
        ax.set_yticks(range(self.map_height))
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✓ 轨迹热力图保存到: {save_path}")
        
        plt.tight_layout()
        plt.show()
    
    def compare_episodes(self, episode_ids: List[int], 
                        save_path: Optional[str] = None):
        """
        比较多个回合的轨迹
        
        Args:
            episode_ids: 回合ID列表
            save_path: 保存路径
        """
        if not self.collector:
            print("❌ 需要轨迹收集器来加载数据")
            return
        
        fig, axes = plt.subplots(1, len(episode_ids), figsize=(6*len(episode_ids), 6))
        if len(episode_ids) == 1:
            axes = [axes]
        
        for i, episode_id in enumerate(episode_ids):
            episode_data = self.collector.load_episode_data(episode_id)
            if not episode_data:
                continue
            
            ax = axes[i]
            ax.set_xlim(-0.5, self.map_width - 0.5)
            ax.set_ylim(-0.5, self.map_height - 0.5)
            ax.set_aspect('equal')
            ax.set_title(f'回合 {episode_id}', fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3)
            
            # 绘制轨迹
            trajectories = episode_data['trajectories']
            for j, (agv_id_str, traj_data) in enumerate(trajectories.items()):
                color = self.agv_colors[j % len(self.agv_colors)]
                
                if traj_data:
                    path_x = [point['position'][0] for point in traj_data]
                    path_y = [point['position'][1] for point in traj_data]
                    ax.plot(path_x, path_y, color=color, alpha=0.7, linewidth=2)
        
        plt.suptitle('回合轨迹对比', fontsize=16, fontweight='bold')
        
        # 保存图片
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✓ 轨迹对比图保存到: {save_path}")
        
        plt.tight_layout()
        plt.show()


class RealTimeTrajectoryMonitor:
    """实时轨迹监控器"""

    def __init__(self, map_width: int = 26, map_height: int = 10):
        """
        初始化实时监控器

        Args:
            map_width: 地图宽度
            map_height: 地图高度
        """
        self.map_width = map_width
        self.map_height = map_height

        # 可视化配置
        self.agv_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        self.agv_size = 0.3

        # 实时数据
        self.current_positions = {}
        self.current_status = {}
        self.recent_trails = {}  # AGV ID -> 最近的位置列表
        self.trail_length = 10

        # 图形对象
        self.fig = None
        self.ax = None
        self.agv_artists = {}
        self.trail_artists = {}
        self.status_text = None

        print("✓ 实时轨迹监控器初始化完成")

    def start_monitoring(self, title: str = "实时AGV轨迹监控"):
        """
        开始实时监控

        Args:
            title: 监控窗口标题
        """
        # 设置绘图环境
        self.fig, self.ax = plt.subplots(figsize=(14, 8))
        self.ax.set_xlim(-0.5, self.map_width - 0.5)
        self.ax.set_ylim(-0.5, self.map_height - 0.5)
        self.ax.set_aspect('equal')
        self.ax.set_title(title, fontsize=16, fontweight='bold')
        self.ax.set_xlabel('X坐标', fontsize=12)
        self.ax.set_ylabel('Y坐标', fontsize=12)

        # 绘制网格和环境
        self.ax.grid(True, alpha=0.3)
        self.ax.set_xticks(range(self.map_width))
        self.ax.set_yticks(range(self.map_height))
        self._draw_warehouse_layout()

        # 状态文本
        self.status_text = self.ax.text(0.02, 0.98, "", transform=self.ax.transAxes,
                                      verticalalignment='top', fontsize=10,
                                      bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

        plt.ion()  # 开启交互模式
        plt.show()

        print("✓ 实时监控已启动")

    def update_agv_positions(self, agvs: List, step: int = 0):
        """
        更新AGV位置

        Args:
            agvs: AGV实体列表
            step: 当前步数
        """
        if not self.fig:
            print("❌ 请先调用start_monitoring()启动监控")
            return

        current_time = datetime.now().strftime("%H:%M:%S")
        status_info = f"时间: {current_time} | 步数: {step}\n"

        for agv in agvs:
            agv_id = agv.agv_id
            position = agv.position
            status = agv.status.name if hasattr(agv.status, 'name') else str(agv.status)

            # 更新位置记录
            self.current_positions[agv_id] = position
            self.current_status[agv_id] = status

            # 更新轨迹尾迹
            if agv_id not in self.recent_trails:
                self.recent_trails[agv_id] = []

            self.recent_trails[agv_id].append(position)
            if len(self.recent_trails[agv_id]) > self.trail_length:
                self.recent_trails[agv_id].pop(0)

            # 创建或更新AGV艺术家对象
            if agv_id not in self.agv_artists:
                self._create_agv_artist(agv_id)

            self._update_agv_artist(agv_id, position, status)

            # 更新状态信息
            status_info += f"AGV{agv_id}: {position} ({status}) 载重:{agv.current_load}\n"

        # 更新状态文本
        self.status_text.set_text(status_info)

        # 刷新显示
        self.fig.canvas.draw()
        self.fig.canvas.flush_events()

    def _create_agv_artist(self, agv_id: int):
        """创建AGV艺术家对象"""
        color = self.agv_colors[agv_id % len(self.agv_colors)]

        # AGV主体
        agv_circle = Circle((0, 0), self.agv_size,
                          facecolor=color, edgecolor='black',
                          linewidth=2, alpha=0.8)
        self.ax.add_patch(agv_circle)

        # AGV标签
        agv_text = self.ax.text(0, 0, f'AGV{agv_id}',
                              ha='center', va='center',
                              fontsize=8, fontweight='bold', color='white')

        # 轨迹尾迹
        trail_line, = self.ax.plot([], [], color=color, alpha=0.6, linewidth=2)

        self.agv_artists[agv_id] = {
            'circle': agv_circle,
            'text': agv_text,
            'color': color
        }
        self.trail_artists[agv_id] = trail_line

    def _update_agv_artist(self, agv_id: int, position: Tuple[int, int], status: str):
        """更新AGV艺术家对象"""
        x, y = position

        # 更新AGV位置
        circle = self.agv_artists[agv_id]['circle']
        text = self.agv_artists[agv_id]['text']

        circle.center = (x, y)
        text.set_position((x, y))

        # 根据状态调整颜色
        if status == 'LOADING':
            circle.set_facecolor('yellow')
        elif status == 'UNLOADING':
            circle.set_facecolor('cyan')
        elif status == 'MOVING':
            circle.set_facecolor(self.agv_artists[agv_id]['color'])
        else:
            circle.set_facecolor('lightgray')

        # 更新轨迹尾迹
        if agv_id in self.recent_trails and len(self.recent_trails[agv_id]) > 1:
            trail_points = self.recent_trails[agv_id]
            trail_x = [p[0] for p in trail_points]
            trail_y = [p[1] for p in trail_points]
            self.trail_artists[agv_id].set_data(trail_x, trail_y)

    def _draw_warehouse_layout(self):
        """绘制仓储环境布局"""
        # 绘制货架区域（简化版本）
        shelf_areas = [
            (2, 2, 4, 6),   # (x, y, width, height)
            (8, 2, 4, 6),
            (14, 2, 4, 6),
            (20, 2, 4, 6)
        ]

        for x, y, w, h in shelf_areas:
            shelf = Rectangle((x, y), w, h,
                            facecolor='lightgray',
                            edgecolor='black',
                            alpha=0.5)
            self.ax.add_patch(shelf)

        # 绘制装卸区域
        loading_zones = [(0, 0, 2, 2), (0, 8, 2, 2), (24, 0, 2, 2), (24, 8, 2, 2)]
        for x, y, w, h in loading_zones:
            zone = Rectangle((x, y), w, h,
                           facecolor='lightblue',
                           edgecolor='blue',
                           alpha=0.7)
            self.ax.add_patch(zone)

    def stop_monitoring(self):
        """停止实时监控"""
        if self.fig:
            plt.ioff()  # 关闭交互模式
            plt.close(self.fig)
            self.fig = None
            print("✓ 实时监控已停止")
