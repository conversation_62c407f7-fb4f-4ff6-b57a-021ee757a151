"""
指标收集器
收集训练过程中的各种性能指标和注意力权重
"""

import time
import json
import sqlite3
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
import torch
from collections import defaultdict, deque


@dataclass
class TrainingMetrics:
    """训练指标数据结构"""
    timestamp: float
    episode: int
    step: int
    
    # 基础训练指标
    episode_reward: float
    episode_length: int
    policy_loss: float
    value_loss: float
    entropy_loss: float
    total_loss: float
    
    # 学习率和梯度信息
    learning_rate: float
    grad_norm: float
    
    # 环境指标
    task_completion_rate: float
    collision_count: int
    avg_task_time: float
    
    # 多智能体指标
    num_active_agvs: int
    coordination_efficiency: float


@dataclass
class AttentionMetrics:
    """注意力机制指标数据结构"""
    timestamp: float
    episode: int
    step: int
    
    # 任务分配注意力指标
    task_attention_entropy: float
    task_attention_max_weight: float
    task_attention_sparsity: float
    
    # 协作感知注意力指标
    collaboration_attention_entropy: float
    collaboration_attention_max_weight: float
    collaboration_attention_sparsity: float
    
    # 注意力权重分布
    task_attention_weights: List[List[float]]  # [num_agvs, num_tasks]
    collaboration_attention_weights: List[List[float]]  # [num_agvs, num_agvs]
    
    # 温度参数
    adaptive_temperatures: List[float]


@dataclass
class SystemMetrics:
    """系统性能指标"""
    timestamp: float
    
    # 计算性能
    cpu_usage: float
    memory_usage: float
    gpu_usage: float
    gpu_memory: float
    
    # 训练性能
    fps: float  # 帧率
    samples_per_second: float
    training_time_per_step: float


class MetricsCollector:
    """
    指标收集器
    负责收集、存储和管理训练过程中的各种指标
    """
    
    def __init__(self, 
                 save_dir: str = "./monitoring_data",
                 db_name: str = "training_metrics.db",
                 buffer_size: int = 1000,
                 auto_save_interval: int = 100):
        """
        初始化指标收集器
        
        Args:
            save_dir: 保存目录
            db_name: 数据库文件名
            buffer_size: 缓冲区大小
            auto_save_interval: 自动保存间隔
        """
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        self.db_path = self.save_dir / db_name
        self.buffer_size = buffer_size
        self.auto_save_interval = auto_save_interval
        
        # 初始化数据库
        self._init_database()
        
        # 内存缓冲区
        self.training_buffer = deque(maxlen=buffer_size)
        self.attention_buffer = deque(maxlen=buffer_size)
        self.system_buffer = deque(maxlen=buffer_size)
        
        # 统计信息
        self.total_episodes = 0
        self.total_steps = 0
        self.start_time = time.time()
        
        # 实时统计
        self.recent_rewards = deque(maxlen=100)
        self.recent_losses = deque(maxlen=100)
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 自动保存计数器
        self.save_counter = 0
        
        print(f"✓ 指标收集器初始化完成")
        print(f"  保存目录: {self.save_dir}")
        print(f"  数据库: {self.db_path}")
        print(f"  缓冲区大小: {buffer_size}")
    
    def _init_database(self):
        """初始化SQLite数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 训练指标表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS training_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    episode INTEGER,
                    step INTEGER,
                    episode_reward REAL,
                    episode_length INTEGER,
                    policy_loss REAL,
                    value_loss REAL,
                    entropy_loss REAL,
                    total_loss REAL,
                    learning_rate REAL,
                    grad_norm REAL,
                    task_completion_rate REAL,
                    collision_count INTEGER,
                    avg_task_time REAL,
                    num_active_agvs INTEGER,
                    coordination_efficiency REAL
                )
            """)
            
            # 注意力指标表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS attention_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    episode INTEGER,
                    step INTEGER,
                    task_attention_entropy REAL,
                    task_attention_max_weight REAL,
                    task_attention_sparsity REAL,
                    collaboration_attention_entropy REAL,
                    collaboration_attention_max_weight REAL,
                    collaboration_attention_sparsity REAL,
                    task_attention_weights TEXT,
                    collaboration_attention_weights TEXT,
                    adaptive_temperatures TEXT
                )
            """)
            
            # 系统指标表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL,
                    cpu_usage REAL,
                    memory_usage REAL,
                    gpu_usage REAL,
                    gpu_memory REAL,
                    fps REAL,
                    samples_per_second REAL,
                    training_time_per_step REAL
                )
            """)
            
            conn.commit()
    
    def collect_training_metrics(self, metrics: TrainingMetrics):
        """
        收集训练指标
        
        Args:
            metrics: 训练指标对象
        """
        with self.lock:
            self.training_buffer.append(metrics)
            self.recent_rewards.append(metrics.episode_reward)
            self.recent_losses.append(metrics.total_loss)
            
            self.total_episodes = max(self.total_episodes, metrics.episode)
            self.total_steps = max(self.total_steps, metrics.step)
            
            self._auto_save()
    
    def collect_attention_metrics(self, metrics: AttentionMetrics):
        """
        收集注意力指标
        
        Args:
            metrics: 注意力指标对象
        """
        with self.lock:
            self.attention_buffer.append(metrics)
            self._auto_save()
    
    def collect_system_metrics(self, metrics: SystemMetrics):
        """
        收集系统指标
        
        Args:
            metrics: 系统指标对象
        """
        with self.lock:
            self.system_buffer.append(metrics)
            self._auto_save()
    
    def _auto_save(self):
        """自动保存到数据库"""
        self.save_counter += 1
        if self.save_counter >= self.auto_save_interval:
            self.save_to_database()
            self.save_counter = 0
    
    def save_to_database(self):
        """保存缓冲区数据到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 保存训练指标
                if self.training_buffer:
                    training_data = []
                    while self.training_buffer:
                        metrics = self.training_buffer.popleft()
                        training_data.append((
                            metrics.timestamp, metrics.episode, metrics.step,
                            metrics.episode_reward, metrics.episode_length,
                            metrics.policy_loss, metrics.value_loss, metrics.entropy_loss, metrics.total_loss,
                            metrics.learning_rate, metrics.grad_norm,
                            metrics.task_completion_rate, metrics.collision_count, metrics.avg_task_time,
                            metrics.num_active_agvs, metrics.coordination_efficiency
                        ))
                    
                    conn.executemany("""
                        INSERT INTO training_metrics (
                            timestamp, episode, step, episode_reward, episode_length,
                            policy_loss, value_loss, entropy_loss, total_loss,
                            learning_rate, grad_norm, task_completion_rate, collision_count,
                            avg_task_time, num_active_agvs, coordination_efficiency
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, training_data)
                
                # 保存注意力指标
                if self.attention_buffer:
                    attention_data = []
                    while self.attention_buffer:
                        metrics = self.attention_buffer.popleft()
                        attention_data.append((
                            metrics.timestamp, metrics.episode, metrics.step,
                            metrics.task_attention_entropy, metrics.task_attention_max_weight, metrics.task_attention_sparsity,
                            metrics.collaboration_attention_entropy, metrics.collaboration_attention_max_weight, metrics.collaboration_attention_sparsity,
                            json.dumps(metrics.task_attention_weights),
                            json.dumps(metrics.collaboration_attention_weights),
                            json.dumps(metrics.adaptive_temperatures)
                        ))
                    
                    conn.executemany("""
                        INSERT INTO attention_metrics (
                            timestamp, episode, step,
                            task_attention_entropy, task_attention_max_weight, task_attention_sparsity,
                            collaboration_attention_entropy, collaboration_attention_max_weight, collaboration_attention_sparsity,
                            task_attention_weights, collaboration_attention_weights, adaptive_temperatures
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, attention_data)
                
                # 保存系统指标
                if self.system_buffer:
                    system_data = []
                    while self.system_buffer:
                        metrics = self.system_buffer.popleft()
                        system_data.append((
                            metrics.timestamp, metrics.cpu_usage, metrics.memory_usage,
                            metrics.gpu_usage, metrics.gpu_memory, metrics.fps,
                            metrics.samples_per_second, metrics.training_time_per_step
                        ))
                    
                    conn.executemany("""
                        INSERT INTO system_metrics (
                            timestamp, cpu_usage, memory_usage, gpu_usage, gpu_memory,
                            fps, samples_per_second, training_time_per_step
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, system_data)
                
                conn.commit()
                
        except Exception as e:
            print(f"❌ 保存数据到数据库时出错: {e}")
    
    def get_recent_stats(self) -> Dict[str, Any]:
        """
        获取最近的统计信息
        
        Returns:
            stats: 统计信息字典
        """
        with self.lock:
            stats = {
                'total_episodes': self.total_episodes,
                'total_steps': self.total_steps,
                'runtime_hours': (time.time() - self.start_time) / 3600,
                'recent_avg_reward': np.mean(self.recent_rewards) if self.recent_rewards else 0.0,
                'recent_avg_loss': np.mean(self.recent_losses) if self.recent_losses else 0.0,
                'buffer_sizes': {
                    'training': len(self.training_buffer),
                    'attention': len(self.attention_buffer),
                    'system': len(self.system_buffer)
                }
            }
        
        return stats
    
    def export_to_json(self, filename: Optional[str] = None) -> str:
        """
        导出数据到JSON文件
        
        Args:
            filename: 文件名（可选）
            
        Returns:
            filepath: 导出文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"training_data_{timestamp}.json"
        
        filepath = self.save_dir / filename
        
        # 从数据库读取所有数据
        data = {
            'training_metrics': [],
            'attention_metrics': [],
            'system_metrics': [],
            'export_time': datetime.now().isoformat(),
            'stats': self.get_recent_stats()
        }
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 读取训练指标
                cursor = conn.execute("SELECT * FROM training_metrics ORDER BY timestamp")
                columns = [description[0] for description in cursor.description]
                for row in cursor.fetchall():
                    data['training_metrics'].append(dict(zip(columns, row)))
                
                # 读取注意力指标
                cursor = conn.execute("SELECT * FROM attention_metrics ORDER BY timestamp")
                columns = [description[0] for description in cursor.description]
                for row in cursor.fetchall():
                    row_dict = dict(zip(columns, row))
                    # 解析JSON字段
                    if row_dict['task_attention_weights']:
                        row_dict['task_attention_weights'] = json.loads(row_dict['task_attention_weights'])
                    if row_dict['collaboration_attention_weights']:
                        row_dict['collaboration_attention_weights'] = json.loads(row_dict['collaboration_attention_weights'])
                    if row_dict['adaptive_temperatures']:
                        row_dict['adaptive_temperatures'] = json.loads(row_dict['adaptive_temperatures'])
                    data['attention_metrics'].append(row_dict)
                
                # 读取系统指标
                cursor = conn.execute("SELECT * FROM system_metrics ORDER BY timestamp")
                columns = [description[0] for description in cursor.description]
                for row in cursor.fetchall():
                    data['system_metrics'].append(dict(zip(columns, row)))
            
            # 保存到JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✓ 数据已导出到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            print(f"❌ 导出数据时出错: {e}")
            return ""
    
    def close(self):
        """关闭收集器，保存剩余数据"""
        print("正在关闭指标收集器...")
        self.save_to_database()
        print("✓ 指标收集器已关闭")
