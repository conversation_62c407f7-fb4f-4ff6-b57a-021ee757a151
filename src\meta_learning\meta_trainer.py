"""
元训练器
整合MAML框架、任务分布生成器和快速适应管理器的完整元学习训练系统
"""

import torch
import numpy as np
import time
import os
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import json
from datetime import datetime

from .maml_framework import MAMLFramework, MAMLConfig
from .task_distribution import TaskDistributionGenerator, TaskVariationConfig, EnvironmentVariation
from .fast_adaptation import FastAdaptationManager, AdaptationConfig, AdaptationStrategy
# from .environment_detector import EnvironmentChangeDetector, ChangeType


@dataclass
class MetaTrainingConfig:
    """元训练配置"""
    
    # 训练参数
    num_meta_iterations: int = 1000
    meta_batch_size: int = 16
    evaluation_interval: int = 50
    save_interval: int = 100
    
    # 任务分布参数
    num_task_variations: int = 5
    task_complexity_progression: bool = True
    
    # 验证参数
    num_validation_tasks: int = 20
    validation_adaptation_steps: int = 3
    
    # 早停参数
    patience: int = 100
    min_improvement: float = 0.001
    
    # 输出配置
    output_dir: str = "./meta_learning_results"
    experiment_name: str = "agv_meta_learning"
    save_checkpoints: bool = True
    
    # 日志配置
    log_interval: int = 10
    detailed_logging: bool = True


class MetaTrainer:
    """
    元训练器
    管理完整的元学习训练流程
    """
    
    def __init__(self, 
                 maml_config: MAMLConfig,
                 adaptation_config: AdaptationConfig,
                 task_variation_config: TaskVariationConfig,
                 training_config: MetaTrainingConfig,
                 obs_space,
                 action_space):
        """
        初始化元训练器
        
        Args:
            maml_config: MAML配置
            adaptation_config: 适应配置
            task_variation_config: 任务变化配置
            training_config: 训练配置
            obs_space: 观察空间
            action_space: 动作空间
        """
        self.training_config = training_config
        
        # 创建核心组件
        self.maml_framework = MAMLFramework(maml_config, obs_space, action_space)
        self.task_generator = TaskDistributionGenerator(task_variation_config)
        self.adaptation_manager = FastAdaptationManager(self.maml_framework, adaptation_config)
        # self.change_detector = EnvironmentChangeDetector()
        
        # 训练状态
        self.current_iteration = 0
        self.best_meta_loss = float('inf')
        self.patience_counter = 0
        
        # 训练历史
        self.training_history = {
            'meta_losses': [],
            'adaptation_performances': [],
            'validation_scores': [],
            'training_times': []
        }
        
        # 创建输出目录
        self.output_dir = os.path.join(
            training_config.output_dir,
            f"{training_config.experiment_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 保存配置
        self._save_configs()
        
    def train(self) -> Dict[str, Any]:
        """
        执行元训练
        
        Returns:
            training_results: 训练结果
        """
        print(f"开始元学习训练 - 实验: {self.training_config.experiment_name}")
        print(f"输出目录: {self.output_dir}")
        print(f"目标迭代次数: {self.training_config.num_meta_iterations}")
        print("=" * 80)
        
        start_time = time.time()
        
        try:
            for iteration in range(self.training_config.num_meta_iterations):
                self.current_iteration = iteration
                
                # 执行一次元训练迭代
                iteration_start = time.time()
                iteration_results = self._meta_training_iteration()
                iteration_time = time.time() - iteration_start
                
                # 记录训练历史
                self._update_training_history(iteration_results, iteration_time)
                
                # 日志输出
                if iteration % self.training_config.log_interval == 0:
                    self._log_training_progress(iteration, iteration_results, iteration_time)
                
                # 验证和评估
                if iteration % self.training_config.evaluation_interval == 0:
                    validation_results = self._evaluate_meta_model()
                    self._log_validation_results(iteration, validation_results)
                    
                    # 早停检查
                    if self._check_early_stopping(validation_results):
                        print(f"早停触发 - 迭代 {iteration}")
                        break
                
                # 保存检查点
                if (iteration % self.training_config.save_interval == 0 and 
                    self.training_config.save_checkpoints):
                    self._save_checkpoint(iteration)
            
            # 训练完成
            total_time = time.time() - start_time
            final_results = self._finalize_training(total_time)
            
            print("=" * 80)
            print("✅ 元学习训练完成!")
            print(f"总训练时间: {total_time:.2f}秒")
            print(f"最佳元损失: {self.best_meta_loss:.6f}")
            print(f"最终检查点: {final_results['final_checkpoint']}")
            
            return final_results
            
        except Exception as e:
            print(f"❌ 训练过程中出现错误: {e}")
            # 保存错误状态
            self._save_error_state(e)
            raise
    
    def _meta_training_iteration(self) -> Dict[str, Any]:
        """
        执行一次元训练迭代
        
        Returns:
            iteration_results: 迭代结果
        """
        # 生成任务批次
        variation_types = self._select_variation_types()
        task_batch = self.task_generator.generate_task_batch(
            batch_size=self.training_config.meta_batch_size,
            variation_types=variation_types
        )
        
        # 执行元更新
        meta_results = self.maml_framework.meta_update(task_batch)
        
        # 测试快速适应能力
        adaptation_results = self._test_adaptation_capability(task_batch[:3])  # 测试前3个任务
        
        return {
            'meta_loss': meta_results['meta_loss'],
            'adaptation_performance': meta_results['adaptation_performance'],
            'meta_iteration': meta_results['meta_iteration'],
            'patience_counter': meta_results['patience_counter'],
            'adaptation_success_rate': adaptation_results['success_rate'],
            'adaptation_time': adaptation_results['avg_adaptation_time'],
            'task_complexity': self._calculate_batch_complexity(task_batch)
        }
    
    def _select_variation_types(self) -> List[EnvironmentVariation]:
        """选择变化类型（课程学习）"""
        progress = self.current_iteration / self.training_config.num_meta_iterations
        
        if progress < 0.3:
            # 早期：简单变化
            return [EnvironmentVariation.AGV_COUNT_CHANGE, EnvironmentVariation.TASK_DISTRIBUTION_CHANGE]
        elif progress < 0.7:
            # 中期：中等复杂度变化
            return [
                EnvironmentVariation.AGV_COUNT_CHANGE,
                EnvironmentVariation.TASK_DISTRIBUTION_CHANGE,
                EnvironmentVariation.LAYOUT_CHANGE
            ]
        else:
            # 后期：所有变化类型
            return list(EnvironmentVariation)
    
    def _test_adaptation_capability(self, test_tasks: List[Dict[str, Any]]) -> Dict[str, float]:
        """测试快速适应能力"""
        adaptation_times = []
        success_count = 0
        
        for task in test_tasks:
            # 模拟新环境数据
            new_env_data = task['support']
            validation_data = task['query']
            
            # 执行快速适应
            adaptation_result = self.adaptation_manager.adapt_to_new_environment(
                new_env_data, validation_data
            )
            
            adaptation_times.append(adaptation_result['adaptation_time'])
            if adaptation_result['success']:
                success_count += 1
        
        return {
            'success_rate': success_count / len(test_tasks),
            'avg_adaptation_time': np.mean(adaptation_times)
        }
    
    def _calculate_batch_complexity(self, task_batch: List[Dict[str, Any]]) -> float:
        """计算任务批次复杂度"""
        complexity_scores = []
        
        for task in task_batch:
            config = task['config']
            variations = task['variations']
            
            # 基于配置计算复杂度
            base_complexity = {
                'low': 1.0,
                'medium': 2.0,
                'high': 3.0
            }.get(config.get('complexity', 'medium'), 2.0)
            
            # 基于变化数量调整
            variation_complexity = len(variations) * 0.5
            
            complexity_scores.append(base_complexity + variation_complexity)
        
        return np.mean(complexity_scores)
    
    def _evaluate_meta_model(self) -> Dict[str, float]:
        """评估元模型"""
        # 生成验证任务
        validation_tasks = self.task_generator.generate_task_batch(
            batch_size=self.training_config.num_validation_tasks,
            variation_types=list(EnvironmentVariation)
        )
        
        adaptation_performances = []
        adaptation_times = []
        success_count = 0
        
        for task in validation_tasks:
            # 测试快速适应
            adaptation_result = self.adaptation_manager.adapt_to_new_environment(
                task['support'], task['query']
            )
            
            adaptation_performances.append(
                adaptation_result['performance_metrics'].get('performance_improvement', 0)
            )
            adaptation_times.append(adaptation_result['adaptation_time'])
            
            if adaptation_result['success']:
                success_count += 1
        
        return {
            'avg_adaptation_performance': np.mean(adaptation_performances),
            'avg_adaptation_time': np.mean(adaptation_times),
            'adaptation_success_rate': success_count / len(validation_tasks),
            'performance_std': np.std(adaptation_performances)
        }
    
    def _update_training_history(self, iteration_results: Dict[str, Any], iteration_time: float):
        """更新训练历史"""
        self.training_history['meta_losses'].append(iteration_results['meta_loss'])
        self.training_history['adaptation_performances'].append(iteration_results['adaptation_performance'])
        self.training_history['training_times'].append(iteration_time)
    
    def _log_training_progress(self, iteration: int, results: Dict[str, Any], iteration_time: float):
        """记录训练进度"""
        print(f"Iteration {iteration:4d} | "
              f"Meta Loss: {results['meta_loss']:.6f} | "
              f"Adapt Perf: {results['adaptation_performance']:.4f} | "
              f"Success Rate: {results['adaptation_success_rate']:.3f} | "
              f"Time: {iteration_time:.2f}s")
    
    def _log_validation_results(self, iteration: int, validation_results: Dict[str, float]):
        """记录验证结果"""
        print(f"Validation {iteration:4d} | "
              f"Adapt Perf: {validation_results['avg_adaptation_performance']:.4f} | "
              f"Success Rate: {validation_results['adaptation_success_rate']:.3f} | "
              f"Adapt Time: {validation_results['avg_adaptation_time']:.2f}s")
        
        # 保存验证结果
        self.training_history['validation_scores'].append(validation_results)
    
    def _check_early_stopping(self, validation_results: Dict[str, float]) -> bool:
        """检查早停条件"""
        current_performance = validation_results['avg_adaptation_performance']
        
        if current_performance > self.best_meta_loss + self.training_config.min_improvement:
            self.best_meta_loss = current_performance
            self.patience_counter = 0
            return False
        else:
            self.patience_counter += 1
            return self.patience_counter >= self.training_config.patience
    
    def _save_checkpoint(self, iteration: int):
        """保存检查点"""
        checkpoint_path = os.path.join(self.output_dir, f"checkpoint_iter_{iteration}.pt")
        
        self.maml_framework.save_meta_model(checkpoint_path)
        
        # 保存训练历史
        history_path = os.path.join(self.output_dir, f"training_history_iter_{iteration}.json")
        with open(history_path, 'w') as f:
            # 转换numpy数组为列表以便JSON序列化
            serializable_history = {}
            for key, value in self.training_history.items():
                if isinstance(value, list) and value and isinstance(value[0], (np.ndarray, np.float64, np.int64)):
                    serializable_history[key] = [float(x) if isinstance(x, (np.ndarray, np.float64, np.int64)) else x for x in value]
                else:
                    serializable_history[key] = value
            
            json.dump(serializable_history, f, indent=2)
    
    def _save_configs(self):
        """保存配置"""
        configs = {
            'maml_config': self.maml_framework.config.__dict__,
            'adaptation_config': self.adaptation_manager.config.__dict__,
            'task_variation_config': self.task_generator.config.__dict__,
            'training_config': self.training_config.__dict__
        }
        
        config_path = os.path.join(self.output_dir, "configs.json")
        with open(config_path, 'w') as f:
            json.dump(configs, f, indent=2, default=str)
    
    def _finalize_training(self, total_time: float) -> Dict[str, Any]:
        """完成训练并返回最终结果"""
        # 保存最终模型
        final_checkpoint = os.path.join(self.output_dir, "final_model.pt")
        self.maml_framework.save_meta_model(final_checkpoint)
        
        # 保存最终训练历史
        final_history_path = os.path.join(self.output_dir, "final_training_history.json")
        with open(final_history_path, 'w') as f:
            serializable_history = {}
            for key, value in self.training_history.items():
                if isinstance(value, list) and value:
                    if isinstance(value[0], dict):
                        serializable_history[key] = value
                    else:
                        serializable_history[key] = [float(x) if isinstance(x, (np.ndarray, np.float64, np.int64)) else x for x in value]
                else:
                    serializable_history[key] = value
            
            json.dump(serializable_history, f, indent=2)
        
        # 生成训练报告
        report = self._generate_training_report(total_time)
        report_path = os.path.join(self.output_dir, "training_report.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        return {
            'final_checkpoint': final_checkpoint,
            'training_report': report,
            'output_directory': self.output_dir,
            'total_training_time': total_time,
            'final_meta_loss': self.training_history['meta_losses'][-1] if self.training_history['meta_losses'] else None
        }
    
    def _generate_training_report(self, total_time: float) -> Dict[str, Any]:
        """生成训练报告"""
        meta_stats = self.maml_framework.get_meta_statistics()
        adaptation_stats = self.adaptation_manager.get_adaptation_statistics()
        task_stats = self.task_generator.get_variation_statistics()
        
        return {
            'experiment_info': {
                'name': self.training_config.experiment_name,
                'total_iterations': self.current_iteration + 1,
                'total_time': total_time,
                'output_directory': self.output_dir
            },
            'meta_learning_stats': meta_stats,
            'adaptation_stats': adaptation_stats,
            'task_generation_stats': task_stats,
            'training_summary': {
                'best_meta_loss': self.best_meta_loss,
                'final_meta_loss': self.training_history['meta_losses'][-1] if self.training_history['meta_losses'] else None,
                'avg_iteration_time': np.mean(self.training_history['training_times']) if self.training_history['training_times'] else 0,
                'convergence_status': meta_stats.get('convergence_status', 'unknown')
            }
        }
    
    def _save_error_state(self, error: Exception):
        """保存错误状态"""
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'current_iteration': self.current_iteration,
            'training_history': self.training_history
        }
        
        error_path = os.path.join(self.output_dir, "error_state.json")
        with open(error_path, 'w') as f:
            json.dump(error_info, f, indent=2, default=str)
