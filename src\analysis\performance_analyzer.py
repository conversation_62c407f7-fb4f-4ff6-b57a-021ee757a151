"""
性能分析器
提供深度性能分析功能，包括模型对比、注意力机制分析、协作效率评估等
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import json
import os
from pathlib import Path

from ..monitoring.metrics_collector import MetricsCollector
from ..visualization.trajectory_collector import TrajectoryCollector
from ..visualization.attention_extractor import AttentionDataExtractor


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    episode_id: int
    timestamp: float
    
    # 基础性能指标
    episode_reward: float
    episode_length: int
    success_rate: float
    task_completion_rate: float
    
    # 协作效率指标
    collision_count: int
    deadlock_count: int
    avg_waiting_time: float
    coordination_efficiency: float
    
    # 注意力机制指标
    attention_entropy: float
    attention_sparsity: float
    attention_stability: float
    
    # 计算效率指标
    inference_time: float
    memory_usage: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class ModelComparison:
    """模型对比结果"""
    model_names: List[str]
    metrics: Dict[str, List[float]]
    statistical_tests: Dict[str, Dict[str, float]]
    recommendations: List[str]


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, 
                 metrics_collector: Optional[MetricsCollector] = None,
                 trajectory_collector: Optional[TrajectoryCollector] = None,
                 attention_extractor: Optional[AttentionDataExtractor] = None):
        """
        初始化性能分析器
        
        Args:
            metrics_collector: 指标收集器
            trajectory_collector: 轨迹收集器
            attention_extractor: 注意力数据提取器
        """
        self.metrics_collector = metrics_collector
        self.trajectory_collector = trajectory_collector
        self.attention_extractor = attention_extractor
        
        # 分析结果存储
        self.performance_history: List[PerformanceMetrics] = []
        self.analysis_cache: Dict[str, Any] = {}
        
        print("✓ 性能分析器初始化完成")
    
    def analyze_episode_performance(self, episode_id: int, 
                                  episode_data: Optional[Dict] = None) -> PerformanceMetrics:
        """
        分析单个回合的性能
        
        Args:
            episode_id: 回合ID
            episode_data: 回合数据（可选）
            
        Returns:
            performance_metrics: 性能指标
        """
        if episode_data is None and self.trajectory_collector:
            episode_data = self.trajectory_collector.load_episode_data(episode_id)
        
        if not episode_data:
            raise ValueError(f"无法获取回合 {episode_id} 的数据")
        
        # 基础性能指标
        episode_reward = self._calculate_episode_reward(episode_data)
        episode_length = episode_data['metadata']['total_steps']
        success_rate = self._calculate_success_rate(episode_data)
        task_completion_rate = self._calculate_task_completion_rate(episode_data)
        
        # 协作效率指标
        collision_count = len(episode_data.get('collision_events', []))
        deadlock_count = self._count_deadlocks(episode_data)
        avg_waiting_time = self._calculate_avg_waiting_time(episode_data)
        coordination_efficiency = self._calculate_coordination_efficiency(episode_data)
        
        # 注意力机制指标
        attention_metrics = self._analyze_attention_mechanisms(episode_data)
        
        # 计算效率指标
        inference_time = self._calculate_inference_time(episode_data)
        memory_usage = self._estimate_memory_usage(episode_data)
        
        # 创建性能指标对象
        metrics = PerformanceMetrics(
            episode_id=episode_id,
            timestamp=datetime.now().timestamp(),
            episode_reward=episode_reward,
            episode_length=episode_length,
            success_rate=success_rate,
            task_completion_rate=task_completion_rate,
            collision_count=collision_count,
            deadlock_count=deadlock_count,
            avg_waiting_time=avg_waiting_time,
            coordination_efficiency=coordination_efficiency,
            attention_entropy=attention_metrics['entropy'],
            attention_sparsity=attention_metrics['sparsity'],
            attention_stability=attention_metrics['stability'],
            inference_time=inference_time,
            memory_usage=memory_usage
        )
        
        # 添加到历史记录
        self.performance_history.append(metrics)
        
        return metrics
    
    def _calculate_episode_reward(self, episode_data: Dict) -> float:
        """计算回合总奖励"""
        total_reward = 0.0
        trajectories = episode_data.get('trajectories', {})
        
        for agv_id, trajectory in trajectories.items():
            for point in trajectory:
                total_reward += point.get('reward', 0.0)
        
        return total_reward
    
    def _calculate_success_rate(self, episode_data: Dict) -> float:
        """计算成功率"""
        task_events = episode_data.get('task_events', [])
        completed_tasks = [e for e in task_events if e['event_type'] == 'delivery_completed']
        total_tasks = len(set(e['task_id'] for e in task_events))
        
        return len(completed_tasks) / max(total_tasks, 1)
    
    def _calculate_task_completion_rate(self, episode_data: Dict) -> float:
        """计算任务完成率"""
        return self._calculate_success_rate(episode_data)  # 简化实现
    
    def _count_deadlocks(self, episode_data: Dict) -> int:
        """统计死锁次数"""
        collision_events = episode_data.get('collision_events', [])
        return len([e for e in collision_events if e.get('collision_type') == 'deadlock'])
    
    def _calculate_avg_waiting_time(self, episode_data: Dict) -> float:
        """计算平均等待时间"""
        trajectories = episode_data.get('trajectories', {})
        waiting_times = []
        
        for agv_id, trajectory in trajectories.items():
            waiting_count = 0
            for point in trajectory:
                if point.get('status') == 'WAITING' or point.get('action') == 'stay':
                    waiting_count += 1
            waiting_times.append(waiting_count)
        
        return np.mean(waiting_times) if waiting_times else 0.0
    
    def _calculate_coordination_efficiency(self, episode_data: Dict) -> float:
        """计算协作效率"""
        # 基于碰撞次数、等待时间和任务完成率的综合指标
        collision_penalty = len(episode_data.get('collision_events', [])) * 0.1
        waiting_penalty = self._calculate_avg_waiting_time(episode_data) * 0.05
        success_bonus = self._calculate_success_rate(episode_data)
        
        efficiency = max(0.0, success_bonus - collision_penalty - waiting_penalty)
        return min(1.0, efficiency)
    
    def _analyze_attention_mechanisms(self, episode_data: Dict) -> Dict[str, float]:
        """分析注意力机制指标"""
        if not self.attention_extractor:
            return {'entropy': 0.0, 'sparsity': 0.0, 'stability': 0.0}
        
        # 提取注意力权重（模拟实现）
        attention_weights = self._extract_attention_weights(episode_data)
        
        # 计算熵（多样性）
        entropy = self._calculate_attention_entropy(attention_weights)
        
        # 计算稀疏性
        sparsity = self._calculate_attention_sparsity(attention_weights)
        
        # 计算稳定性
        stability = self._calculate_attention_stability(attention_weights)
        
        return {
            'entropy': entropy,
            'sparsity': sparsity,
            'stability': stability
        }
    
    def _extract_attention_weights(self, episode_data: Dict) -> np.ndarray:
        """提取注意力权重（模拟实现）"""
        # 这里应该从实际的注意力数据中提取
        # 目前使用模拟数据
        num_steps = episode_data['metadata']['total_steps']
        num_agvs = episode_data['metadata']['num_agvs']
        
        # 生成模拟的注意力权重
        weights = np.random.dirichlet(np.ones(num_agvs), size=num_steps)
        return weights
    
    def _calculate_attention_entropy(self, weights: np.ndarray) -> float:
        """计算注意力熵"""
        if weights.size == 0:
            return 0.0
        
        # 计算每个时间步的熵，然后取平均
        entropies = []
        for step_weights in weights:
            # 避免log(0)
            step_weights = step_weights + 1e-8
            entropy = -np.sum(step_weights * np.log(step_weights))
            entropies.append(entropy)
        
        return np.mean(entropies)
    
    def _calculate_attention_sparsity(self, weights: np.ndarray) -> float:
        """计算注意力稀疏性"""
        if weights.size == 0:
            return 0.0
        
        # 使用Gini系数衡量稀疏性
        sparsities = []
        for step_weights in weights:
            sorted_weights = np.sort(step_weights)
            n = len(sorted_weights)
            index = np.arange(1, n + 1)
            gini = (2 * np.sum(index * sorted_weights)) / (n * np.sum(sorted_weights)) - (n + 1) / n
            sparsities.append(gini)
        
        return np.mean(sparsities)
    
    def _calculate_attention_stability(self, weights: np.ndarray) -> float:
        """计算注意力稳定性"""
        if weights.shape[0] < 2:
            return 1.0
        
        # 计算相邻时间步之间的相似性
        similarities = []
        for i in range(len(weights) - 1):
            # 使用余弦相似度
            w1, w2 = weights[i], weights[i + 1]
            similarity = np.dot(w1, w2) / (np.linalg.norm(w1) * np.linalg.norm(w2) + 1e-8)
            similarities.append(similarity)
        
        return np.mean(similarities)
    
    def _calculate_inference_time(self, episode_data: Dict) -> float:
        """计算推理时间（模拟）"""
        # 基于步数和AGV数量估算
        num_steps = episode_data['metadata']['total_steps']
        num_agvs = episode_data['metadata']['num_agvs']
        
        # 模拟推理时间（毫秒）
        base_time = 10.0  # 基础时间
        complexity_factor = num_agvs * 0.5  # 复杂度因子
        
        return base_time + complexity_factor
    
    def _estimate_memory_usage(self, episode_data: Dict) -> float:
        """估算内存使用量（MB）"""
        # 基于数据量估算
        trajectories = episode_data.get('trajectories', {})
        total_points = sum(len(traj) for traj in trajectories.values())
        
        # 估算每个轨迹点的内存使用（字节）
        bytes_per_point = 200  # 估算值
        total_bytes = total_points * bytes_per_point
        
        return total_bytes / (1024 * 1024)  # 转换为MB
    
    def compare_models(self, model_results: Dict[str, List[PerformanceMetrics]]) -> ModelComparison:
        """
        比较多个模型的性能
        
        Args:
            model_results: 模型结果字典 {model_name: [metrics_list]}
            
        Returns:
            comparison: 模型对比结果
        """
        model_names = list(model_results.keys())
        metrics_dict = {}
        
        # 提取各项指标
        metric_names = [
            'episode_reward', 'success_rate', 'task_completion_rate',
            'coordination_efficiency', 'attention_entropy', 'inference_time'
        ]
        
        for metric_name in metric_names:
            metrics_dict[metric_name] = []
            for model_name in model_names:
                model_metrics = model_results[model_name]
                values = [getattr(m, metric_name) for m in model_metrics]
                metrics_dict[metric_name].append(np.mean(values))
        
        # 统计检验
        statistical_tests = self._perform_statistical_tests(model_results, metric_names)
        
        # 生成建议
        recommendations = self._generate_model_recommendations(metrics_dict, model_names)
        
        return ModelComparison(
            model_names=model_names,
            metrics=metrics_dict,
            statistical_tests=statistical_tests,
            recommendations=recommendations
        )
    
    def _perform_statistical_tests(self, model_results: Dict[str, List[PerformanceMetrics]], 
                                 metric_names: List[str]) -> Dict[str, Dict[str, float]]:
        """执行统计检验"""
        from scipy import stats
        
        tests = {}
        model_names = list(model_results.keys())
        
        if len(model_names) < 2:
            return tests
        
        for metric_name in metric_names:
            tests[metric_name] = {}
            
            # 提取数据
            data_groups = []
            for model_name in model_names:
                values = [getattr(m, metric_name) for m in model_results[model_name]]
                data_groups.append(values)
            
            # 执行ANOVA检验
            if len(data_groups) > 2:
                try:
                    f_stat, p_value = stats.f_oneway(*data_groups)
                    tests[metric_name]['anova_f'] = f_stat
                    tests[metric_name]['anova_p'] = p_value
                except:
                    tests[metric_name]['anova_f'] = 0.0
                    tests[metric_name]['anova_p'] = 1.0
            
            # 执行t检验（前两个模型）
            if len(data_groups) >= 2:
                try:
                    t_stat, p_value = stats.ttest_ind(data_groups[0], data_groups[1])
                    tests[metric_name]['ttest_t'] = t_stat
                    tests[metric_name]['ttest_p'] = p_value
                except:
                    tests[metric_name]['ttest_t'] = 0.0
                    tests[metric_name]['ttest_p'] = 1.0
        
        return tests
    
    def _generate_model_recommendations(self, metrics_dict: Dict[str, List[float]], 
                                      model_names: List[str]) -> List[str]:
        """生成模型建议"""
        recommendations = []
        
        # 找出各项指标的最佳模型
        best_models = {}
        for metric_name, values in metrics_dict.items():
            if metric_name == 'inference_time':  # 越小越好
                best_idx = np.argmin(values)
            else:  # 越大越好
                best_idx = np.argmax(values)
            best_models[metric_name] = model_names[best_idx]
        
        # 生成建议
        if len(set(best_models.values())) == 1:
            best_model = list(best_models.values())[0]
            recommendations.append(f"模型 {best_model} 在所有指标上都表现最佳，强烈推荐使用")
        else:
            recommendations.append("不同模型在不同指标上各有优势：")
            for metric, model in best_models.items():
                recommendations.append(f"- {metric}: {model} 表现最佳")
        
        return recommendations
