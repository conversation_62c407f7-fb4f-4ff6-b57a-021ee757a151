"""
注意力机制预训练模块
实现双层注意力机制的预训练系统
"""

from .data_generator import (
    AttentionPretrainingDataGenerator,
    PretrainingBatch,
    PretrainingScenarioType
)

from .loss_functions import (
    AttentionPretrainingLoss,
    PretrainingLossOutput,
    CurriculumLossScheduler
)

from .trainer import (
    AttentionPretrainingTrainer,
    AttentionPretrainingModel,
    PretrainingConfig,
    PretrainingDataset
)

__all__ = [
    # 数据生成
    'AttentionPretrainingDataGenerator',
    'PretrainingBatch',
    'PretrainingScenarioType',
    
    # 损失函数
    'AttentionPretrainingLoss',
    'PretrainingLossOutput',
    'CurriculumLossScheduler',
    
    # 训练器
    'AttentionPretrainingTrainer',
    'AttentionPretrainingModel',
    'PretrainingConfig',
    'PretrainingDataset'
]
