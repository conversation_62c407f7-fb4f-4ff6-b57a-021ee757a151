"""
简化的AGV环境
用于MAPPO框架测试的简化版本
"""

import gymnasium as gym
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from gymnasium import spaces
import random


class SimpleAGVEntity:
    """简化的AGV实体"""
    
    def __init__(self, agv_id: int, position: Tuple[int, int], capacity: int = 25):
        self.id = agv_id
        self.position = position
        self.capacity = capacity
        self.current_load = 0
        self.status = "idle"  # idle, moving, loading, unloading
        self.target_task = None
        
    def is_idle(self) -> bool:
        return self.status == "idle"
    
    def assign_task(self, task_id: int):
        self.target_task = task_id
        self.status = "moving"


class SimpleTask:
    """简化的任务"""
    
    def __init__(self, task_id: int, pickup_pos: Tuple[int, int], delivery_pos: Tuple[int, int]):
        self.id = task_id
        self.pickup_position = pickup_pos
        self.delivery_position = delivery_pos
        self.assigned_agv = None
        self.status = "pending"  # pending, assigned, completed


class SimpleTaskManager:
    """简化的任务管理器"""
    
    def __init__(self, num_tasks: int = 5):
        self.tasks = []
        self.completed_tasks = []
        
        # 创建随机任务
        for i in range(num_tasks):
            pickup_pos = (random.randint(1, 24), random.randint(1, 8))
            delivery_pos = (random.randint(1, 24), random.randint(1, 8))
            task = SimpleTask(i, pickup_pos, delivery_pos)
            self.tasks.append(task)
    
    def get_pending_tasks(self) -> List[SimpleTask]:
        return [task for task in self.tasks if task.status == "pending"]
    
    def get_completed_task_count(self) -> int:
        return len(self.completed_tasks)
    
    def update(self):
        """更新任务状态"""
        pass


class SimpleAGVEnvironment:
    """简化的AGV环境"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.num_agvs = config.get('num_agvs', 3)
        self.num_tasks = config.get('num_tasks', 5)
        self.max_episode_steps = config.get('max_episode_steps', 200)
        
        # 创建AGV
        self.agvs = []
        for i in range(self.num_agvs):
            pos = (random.randint(1, 24), random.randint(1, 8))
            agv = SimpleAGVEntity(i, pos)
            self.agvs.append(agv)
        
        # 创建任务管理器
        self.task_manager = SimpleTaskManager(self.num_tasks)
        
        # 环境状态
        self.current_step = 0
        self.episode_rewards = []
    
    def reset(self):
        """重置环境"""
        # 重置AGV位置
        for i, agv in enumerate(self.agvs):
            agv.position = (random.randint(1, 24), random.randint(1, 8))
            agv.current_load = 0
            agv.status = "idle"
            agv.target_task = None

        # 重置任务
        self.task_manager = SimpleTaskManager(self.num_tasks)

        # 重置环境状态
        self.current_step = 0
        self.episode_rewards = []

        observation = self._get_observation()
        info = {
            'episode_step': self.current_step,
            'active_tasks': len(self.task_manager.tasks)
        }

        return observation, info
    
    def step(self, actions: Dict[int, str]) -> Dict[str, Any]:
        """执行一步"""
        self.current_step += 1
        
        # 执行动作（简化版本）
        rewards = {}
        for agv_id, action in actions.items():
            if agv_id < len(self.agvs):
                agv = self.agvs[agv_id]
                reward = self._execute_action(agv, action)
                rewards[agv_id] = reward
        
        # 更新任务状态
        self.task_manager.update()
        
        return {
            'rewards': rewards,
            'stats': {
                'completed_tasks': self.task_manager.get_completed_task_count(),
                'current_step': self.current_step
            }
        }
    
    def _execute_action(self, agv: SimpleAGVEntity, action: str) -> float:
        """执行单个AGV的动作"""
        reward = 0.0
        
        # 简化的动作执行
        if action in ['up', 'down', 'left', 'right']:
            # 移动动作
            x, y = agv.position
            if action == 'up' and y > 0:
                agv.position = (x, y - 1)
            elif action == 'down' and y < 9:
                agv.position = (x, y + 1)
            elif action == 'left' and x > 0:
                agv.position = (x - 1, y)
            elif action == 'right' and x < 25:
                agv.position = (x + 1, y)
            
            reward = -0.01  # 移动成本
            
        elif action == 'load':
            # 装载动作
            if agv.current_load < agv.capacity:
                agv.current_load += 1
                reward = 0.1
                
        elif action == 'unload':
            # 卸载动作
            if agv.current_load > 0:
                agv.current_load -= 1
                reward = 0.5  # 卸载奖励
        
        return reward
    
    def _get_observation(self) -> Dict[str, Any]:
        """获取观察（简化版本）"""
        return {
            'agvs': self.agvs,
            'tasks': self.task_manager.get_pending_tasks(),
            'global_state': {
                'step': self.current_step,
                'completed_tasks': self.task_manager.get_completed_task_count()
            }
        }
    
    def is_episode_complete(self) -> bool:
        """检查回合是否完成"""
        return (self.current_step >= self.max_episode_steps or 
                len(self.task_manager.get_pending_tasks()) == 0)
    
    def render(self, mode='human'):
        """渲染环境"""
        if mode == 'human':
            print(f"Step: {self.current_step}")
            print(f"AGVs: {[(agv.id, agv.position, agv.current_load) for agv in self.agvs]}")
            print(f"Pending tasks: {len(self.task_manager.get_pending_tasks())}")
            print("-" * 50)
    
    def close(self):
        """关闭环境"""
        pass
    
    def seed(self, seed: int):
        """设置随机种子"""
        random.seed(seed)
        np.random.seed(seed)
        return [seed]
