"""
注意力增强的MAPPO训练器
集成双层注意力机制的高级MAPPO训练实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, NamedTuple
from dataclasses import dataclass
import logging
from collections import defaultdict

from .attention_enhanced_networks import (
    AttentionEnhancedPolicyNetwork,
    AttentionEnhancedValueNetwork,
    AttentionEnhancedOutput
)


@dataclass
class TrainingBatch:
    """训练批次数据"""
    agv_features: torch.Tensor
    task_features: torch.Tensor
    agv_positions: torch.Tensor
    actions: torch.Tensor
    rewards: torch.Tensor
    values: torch.Tensor
    advantages: torch.Tensor
    action_masks: Optional[torch.Tensor] = None
    global_states: Optional[torch.Tensor] = None


@dataclass
class TrainingMetrics:
    """训练指标"""
    policy_loss: float
    value_loss: float
    attention_loss: float
    total_loss: float
    policy_entropy: float
    attention_entropy: float
    kl_divergence: float
    explained_variance: float
    gradient_norm: float


class AttentionEnhancedMAPPOTrainer:
    """注意力增强的MAPPO训练器"""
    
    def __init__(self,
                 feature_dim: int = 64,
                 hidden_dim: int = 256,
                 action_dim: int = 7,
                 num_heads: int = 8,
                 learning_rate: float = 3e-4,
                 clip_param: float = 0.2,
                 value_loss_coeff: float = 0.5,
                 entropy_coeff: float = 0.01,
                 attention_loss_coeff: float = 0.1,
                 max_grad_norm: float = 0.5,
                 device: str = 'cpu'):
        
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.action_dim = action_dim
        self.num_heads = num_heads
        self.learning_rate = learning_rate
        self.clip_param = clip_param
        self.value_loss_coeff = value_loss_coeff
        self.entropy_coeff = entropy_coeff
        self.attention_loss_coeff = attention_loss_coeff
        self.max_grad_norm = max_grad_norm
        self.device = device
        
        # 创建网络
        self.policy_network = AttentionEnhancedPolicyNetwork(
            feature_dim=feature_dim,
            hidden_dim=hidden_dim,
            action_dim=action_dim,
            num_heads=num_heads
        ).to(device)
        
        self.value_network = AttentionEnhancedValueNetwork(
            feature_dim=feature_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads
        ).to(device)
        
        # 创建优化器
        self.policy_optimizer = optim.Adam(
            self.policy_network.parameters(),
            lr=learning_rate
        )
        
        self.value_optimizer = optim.Adam(
            self.value_network.parameters(),
            lr=learning_rate
        )
        
        # 训练统计
        self.training_stats = defaultdict(list)
        self.logger = logging.getLogger(__name__)
        
    def compute_gae_advantages(self,
                              rewards: torch.Tensor,
                              values: torch.Tensor,
                              dones: torch.Tensor,
                              gamma: float = 0.99,
                              lambda_: float = 0.95) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算GAE优势估计
        
        Args:
            rewards: 奖励 [batch_size, seq_len]
            values: 价值估计 [batch_size, seq_len]
            dones: 完成标志 [batch_size, seq_len]
            gamma: 折扣因子
            lambda_: GAE参数
            
        Returns:
            advantages: 优势估计
            returns: 回报
        """
        batch_size, seq_len = rewards.shape
        advantages = torch.zeros_like(rewards)
        returns = torch.zeros_like(rewards)
        
        # 计算GAE
        gae = 0
        for t in reversed(range(seq_len)):
            if t == seq_len - 1:
                next_value = 0
                next_done = 1
            else:
                next_value = values[:, t + 1]
                next_done = dones[:, t + 1]
            
            delta = rewards[:, t] + gamma * next_value * (1 - next_done) - values[:, t]
            gae = delta + gamma * lambda_ * (1 - next_done) * gae
            advantages[:, t] = gae
            returns[:, t] = advantages[:, t] + values[:, t]
        
        return advantages, returns
    
    def compute_policy_loss(self,
                           policy_output: AttentionEnhancedOutput,
                           actions: torch.Tensor,
                           advantages: torch.Tensor,
                           old_log_probs: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算策略损失
        
        Args:
            policy_output: 策略网络输出
            actions: 动作
            advantages: 优势估计
            old_log_probs: 旧的对数概率
            
        Returns:
            policy_loss: 策略损失
            metrics: 损失指标
        """
        # 计算动作概率分布
        action_dist = F.softmax(policy_output.policy_logits, dim=-1)
        log_probs = F.log_softmax(policy_output.policy_logits, dim=-1)
        
        # 获取选择动作的对数概率
        action_log_probs = log_probs.gather(-1, actions.unsqueeze(-1)).squeeze(-1)
        
        # 计算重要性采样比率
        ratio = torch.exp(action_log_probs - old_log_probs)
        
        # 计算裁剪的策略损失
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.clip_param, 1 + self.clip_param) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 计算策略熵
        policy_entropy = -(action_dist * log_probs).sum(dim=-1).mean()
        
        # 计算KL散度
        kl_div = (torch.exp(old_log_probs) * (old_log_probs - action_log_probs)).mean()
        
        metrics = {
            'policy_entropy': policy_entropy.item(),
            'kl_divergence': kl_div.item(),
            'ratio_mean': ratio.mean().item(),
            'ratio_std': ratio.std().item()
        }
        
        return policy_loss, metrics
    
    def compute_value_loss(self,
                          value_estimates: torch.Tensor,
                          returns: torch.Tensor,
                          old_values: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算价值损失
        
        Args:
            value_estimates: 价值估计
            returns: 回报
            old_values: 旧的价值估计
            
        Returns:
            value_loss: 价值损失
            metrics: 损失指标
        """
        # 裁剪的价值损失
        value_pred_clipped = old_values + torch.clamp(
            value_estimates - old_values,
            -self.clip_param,
            self.clip_param
        )
        
        value_loss1 = F.mse_loss(value_estimates, returns)
        value_loss2 = F.mse_loss(value_pred_clipped, returns)
        value_loss = torch.max(value_loss1, value_loss2)
        
        # 计算解释方差
        explained_variance = 1 - torch.var(returns - value_estimates) / torch.var(returns)
        
        metrics = {
            'explained_variance': explained_variance.item(),
            'value_mean': value_estimates.mean().item(),
            'return_mean': returns.mean().item()
        }
        
        return value_loss, metrics
    
    def compute_attention_loss(self, policy_output: AttentionEnhancedOutput) -> torch.Tensor:
        """
        计算注意力正则化损失
        
        Args:
            policy_output: 策略网络输出
            
        Returns:
            attention_loss: 注意力损失
        """
        # 注意力熵正则化（鼓励多样性）
        entropy_loss = -policy_output.attention_entropy
        
        # 特征重要性正则化（鼓励稀疏性）
        importance_loss = torch.mean(torch.abs(policy_output.feature_importance))
        
        # 注意力权重平滑性正则化
        task_attention_smoothness = torch.mean(
            torch.abs(policy_output.task_attention_weights[:, 1:] - 
                     policy_output.task_attention_weights[:, :-1])
        )
        
        collab_attention_smoothness = torch.mean(
            torch.abs(policy_output.collaboration_attention_weights[:, 1:] - 
                     policy_output.collaboration_attention_weights[:, :-1])
        )
        
        attention_loss = (entropy_loss + 
                         0.1 * importance_loss + 
                         0.05 * task_attention_smoothness + 
                         0.05 * collab_attention_smoothness)
        
        return attention_loss
    
    def train_step(self, batch: TrainingBatch, num_epochs: int = 10) -> TrainingMetrics:
        """
        执行一步训练
        
        Args:
            batch: 训练批次
            num_epochs: 训练轮数
            
        Returns:
            metrics: 训练指标
        """
        # 计算旧的策略输出（用于重要性采样）
        with torch.no_grad():
            old_policy_output = self.policy_network(
                batch.agv_features,
                batch.task_features,
                batch.agv_positions,
                batch.action_masks
            )
            old_log_probs = F.log_softmax(old_policy_output.policy_logits, dim=-1)
            old_action_log_probs = old_log_probs.gather(-1, batch.actions.unsqueeze(-1)).squeeze(-1)
            
            old_values = self.value_network(
                batch.agv_features,
                batch.task_features,
                batch.agv_positions,
                batch.global_states
            )
        
        # 计算GAE优势
        advantages, returns = self.compute_gae_advantages(
            batch.rewards,
            batch.values,
            torch.zeros_like(batch.rewards)  # 简化：假设没有episode结束
        )
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        total_metrics = defaultdict(list)
        
        # 多轮训练
        for epoch in range(num_epochs):
            # 策略网络前向传播
            policy_output = self.policy_network(
                batch.agv_features,
                batch.task_features,
                batch.agv_positions,
                batch.action_masks
            )
            
            # 价值网络前向传播
            value_estimates = self.value_network(
                batch.agv_features,
                batch.task_features,
                batch.agv_positions,
                batch.global_states
            )
            
            # 计算损失
            policy_loss, policy_metrics = self.compute_policy_loss(
                policy_output, batch.actions, advantages, old_action_log_probs
            )
            
            value_loss, value_metrics = self.compute_value_loss(
                value_estimates, returns, old_values
            )
            
            attention_loss = self.compute_attention_loss(policy_output)
            
            # 总损失
            total_loss = (policy_loss + 
                         self.value_loss_coeff * value_loss + 
                         self.entropy_coeff * (-policy_metrics['policy_entropy']) +
                         self.attention_loss_coeff * attention_loss)
            
            # 策略网络优化
            self.policy_optimizer.zero_grad()
            policy_loss.backward(retain_graph=True)
            policy_grad_norm = torch.nn.utils.clip_grad_norm_(
                self.policy_network.parameters(), self.max_grad_norm
            )
            self.policy_optimizer.step()
            
            # 价值网络优化
            self.value_optimizer.zero_grad()
            value_loss.backward()
            value_grad_norm = torch.nn.utils.clip_grad_norm_(
                self.value_network.parameters(), self.max_grad_norm
            )
            self.value_optimizer.step()
            
            # 记录指标
            total_metrics['policy_loss'].append(policy_loss.item())
            total_metrics['value_loss'].append(value_loss.item())
            total_metrics['attention_loss'].append(attention_loss.item())
            total_metrics['total_loss'].append(total_loss.item())
            total_metrics['gradient_norm'].append(max(policy_grad_norm, value_grad_norm))
            
            for key, value in policy_metrics.items():
                total_metrics[key].append(value)
            for key, value in value_metrics.items():
                total_metrics[key].append(value)
        
        # 计算平均指标
        avg_metrics = TrainingMetrics(
            policy_loss=np.mean(total_metrics['policy_loss']),
            value_loss=np.mean(total_metrics['value_loss']),
            attention_loss=np.mean(total_metrics['attention_loss']),
            total_loss=np.mean(total_metrics['total_loss']),
            policy_entropy=np.mean(total_metrics['policy_entropy']),
            attention_entropy=policy_output.attention_entropy.item(),
            kl_divergence=np.mean(total_metrics['kl_divergence']),
            explained_variance=np.mean(total_metrics['explained_variance']),
            gradient_norm=np.mean(total_metrics['gradient_norm'])
        )
        
        return avg_metrics
    
    def save_checkpoint(self, filepath: str):
        """保存检查点"""
        checkpoint = {
            'policy_network': self.policy_network.state_dict(),
            'value_network': self.value_network.state_dict(),
            'policy_optimizer': self.policy_optimizer.state_dict(),
            'value_optimizer': self.value_optimizer.state_dict(),
            'training_stats': dict(self.training_stats)
        }
        torch.save(checkpoint, filepath)
        
    def load_checkpoint(self, filepath: str):
        """加载检查点"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.policy_network.load_state_dict(checkpoint['policy_network'])
        self.value_network.load_state_dict(checkpoint['value_network'])
        self.policy_optimizer.load_state_dict(checkpoint['policy_optimizer'])
        self.value_optimizer.load_state_dict(checkpoint['value_optimizer'])
        self.training_stats = defaultdict(list, checkpoint.get('training_stats', {}))
    
    def get_action(self, 
                   agv_features: torch.Tensor,
                   task_features: torch.Tensor,
                   agv_positions: torch.Tensor,
                   action_mask: Optional[torch.Tensor] = None,
                   deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        获取动作
        
        Args:
            agv_features: AGV特征
            task_features: 任务特征
            agv_positions: AGV位置
            action_mask: 动作掩码
            deterministic: 是否确定性选择
            
        Returns:
            actions: 选择的动作
            log_probs: 对数概率
        """
        with torch.no_grad():
            policy_output = self.policy_network(
                agv_features, task_features, agv_positions, action_mask
            )
            
            action_dist = F.softmax(policy_output.policy_logits, dim=-1)
            
            if deterministic:
                actions = torch.argmax(action_dist, dim=-1)
            else:
                actions = torch.multinomial(action_dist, 1).squeeze(-1)
            
            log_probs = F.log_softmax(policy_output.policy_logits, dim=-1)
            action_log_probs = log_probs.gather(-1, actions.unsqueeze(-1)).squeeze(-1)
            
            return actions, action_log_probs
