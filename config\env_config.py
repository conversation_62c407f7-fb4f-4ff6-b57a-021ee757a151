"""
环境配置文件
定义仓储环境的各种参数和设置
"""

from dataclasses import dataclass
from typing import Tuple, List


@dataclass
class EnvironmentConfig:
    """环境配置类"""

    # 地图配置
    map_width: int = 26                    # 地图宽度（网格数）
    map_height: int = 10                   # 地图高度（网格数）

    # 货架配置
    shelf_count: int = 15                  # 货架数量
    shelf_width: int = 4                   # 货架宽度
    shelf_height: int = 2                  # 货架高度
    shelf_rows: int = 3                    # 货架行数
    shelf_cols: int = 5                    # 货架列数

    # AGV配置
    num_agvs: int = 4                      # AGV数量
    agv_capacity: int = 25                 # AGV载重能力
    agv_speed: float = 1.0                 # AGV移动速度

    # 任务配置
    num_tasks: int = 16                    # 任务数量
    task_weights: List[int] = None         # 任务重量列表（只有5和10两种重量）

    # 时间配置
    max_episode_steps: int = 1000          # 最大回合步数
    time_limit: int = 500                  # 时间限制

    # 奖励配置
    completion_reward: float = 10.0        # 任务完成奖励
    movement_penalty: float = -0.1         # 移动惩罚
    waiting_penalty: float = -0.05         # 等待惩罚
    collision_penalty: float = -5.0        # 碰撞惩罚
    distance_reward: float = 0.1           # 距离奖励
    system_reward: float = 0.2             # 系统效率奖励
    idle_penalty: float = -0.1             # 空闲惩罚

    # 观察空间配置
    observation_radius: int = 5            # 观察半径
    include_global_info: bool = True       # 是否包含全局信息

    # 动作空间配置
    action_space_size: int = 5             # 动作空间大小
    enable_action_masking: bool = True     # 是否启用动作掩码

    def __post_init__(self):
        """初始化后处理"""
        if self.task_weights is None:
            # 只有5和10两种重量，随机分配
            self.task_weights = [5, 10] * (self.num_tasks // 2)
            if self.num_tasks % 2 == 1:
                self.task_weights.append(5)

    def get_map_size(self) -> Tuple[int, int]:
        """获取地图尺寸"""
        return (self.map_width, self.map_height)

    def get_shelf_layout(self) -> List[Tuple[int, int, int, int]]:
        """获取货架布局 (x, y, width, height)"""
        shelves = []
        shelf_spacing_x = self.map_width // self.shelf_cols
        shelf_spacing_y = self.map_height // self.shelf_rows

        for row in range(self.shelf_rows):
            for col in range(self.shelf_cols):
                x = col * shelf_spacing_x + 1
                y = row * shelf_spacing_y + 1
                shelves.append((x, y, self.shelf_width, self.shelf_height))

        return shelves[:self.shelf_count]

    def validate(self) -> bool:
        """验证配置的有效性"""
        # 检查地图尺寸
        if self.map_width <= 0 or self.map_height <= 0:
            return False

        # 检查AGV数量
        if self.num_agvs <= 0 or self.num_agvs > 10:
            return False

        # 检查任务数量
        if self.num_tasks <= 0 or self.num_tasks > 50:
            return False

        # 检查货架配置
        total_shelf_area = self.shelf_count * self.shelf_width * self.shelf_height
        map_area = self.map_width * self.map_height
        if total_shelf_area >= map_area * 0.8:  # 货架不能占用超过80%的地图
            return False

        return True


# 默认环境配置实例
DEFAULT_ENV_CONFIG = EnvironmentConfig()

# 课程学习阶段配置
CURRICULUM_CONFIGS = {
    "stage1": EnvironmentConfig(
        num_agvs=2,
        num_tasks=4,
        max_episode_steps=300
    ),
    "stage2": EnvironmentConfig(
        num_agvs=3,
        num_tasks=8,
        max_episode_steps=500
    ),
    "stage3": EnvironmentConfig(
        num_agvs=4,
        num_tasks=16,
        max_episode_steps=1000
    )
}