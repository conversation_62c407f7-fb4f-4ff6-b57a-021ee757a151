"""
注意力机制可视化模块
实现双层注意力机制的全面可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import networkx as nx
import pandas as pd
import torch
from typing import Dict, List, Tuple, Any, Optional, Union
import os
from datetime import datetime
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置绘图风格
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')


class AttentionVisualizer:
    """
    注意力机制可视化器
    提供双层注意力机制的全面可视化功能
    """
    
    def __init__(self, save_dir: str = "./visualization_results"):
        """
        初始化可视化器
        
        Args:
            save_dir: 保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 颜色配置
        self.colors = {
            'task_attention': '#FF6B6B',      # 任务注意力 - 红色
            'collaboration': '#4ECDC4',       # 协作注意力 - 青色
            'agv': '#45B7D1',                 # AGV - 蓝色
            'task': '#96CEB4',                # 任务 - 绿色
            'warehouse': '#FFEAA7',           # 仓库 - 黄色
            'path': '#DDA0DD',                # 路径 - 紫色
            'attention_high': '#FF4757',      # 高注意力 - 深红
            'attention_medium': '#FFA502',    # 中注意力 - 橙色
            'attention_low': '#70A1FF'        # 低注意力 - 浅蓝
        }
        
        # 图表配置
        self.figure_size = (12, 8)
        self.dpi = 300
        
        print(f"✓ 注意力可视化器初始化完成，保存目录: {save_dir}")
    
    def visualize_task_attention_heatmap(self, 
                                       attention_weights: torch.Tensor,
                                       agv_ids: List[str],
                                       task_ids: List[str],
                                       title: str = "任务分配注意力热图",
                                       save_name: Optional[str] = None) -> str:
        """
        可视化任务分配注意力热图
        
        Args:
            attention_weights: 注意力权重矩阵 [num_agvs, num_tasks]
            agv_ids: AGV ID列表
            task_ids: 任务ID列表
            title: 图表标题
            save_name: 保存文件名
            
        Returns:
            save_path: 保存路径
        """
        # 转换为numpy数组
        if isinstance(attention_weights, torch.Tensor):
            weights = attention_weights.detach().cpu().numpy()
        else:
            weights = np.array(attention_weights)
        
        # 创建图表
        plt.figure(figsize=self.figure_size, dpi=self.dpi)
        
        # 绘制热图
        sns.heatmap(
            weights,
            xticklabels=task_ids,
            yticklabels=agv_ids,
            annot=True,
            fmt='.3f',
            cmap='YlOrRd',
            cbar_kws={'label': '注意力权重'},
            square=True
        )
        
        plt.title(title, fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('任务ID', fontsize=12)
        plt.ylabel('AGV ID', fontsize=12)
        plt.tight_layout()
        
        # 保存图表
        if save_name is None:
            save_name = f"task_attention_heatmap_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 任务注意力热图已保存: {save_path}")
        return save_path
    
    def visualize_collaboration_network(self,
                                      collaboration_weights: torch.Tensor,
                                      agv_positions: List[Tuple[int, int]],
                                      agv_ids: List[str],
                                      threshold: float = 0.1,
                                      title: str = "AGV协作关系网络图",
                                      save_name: Optional[str] = None) -> str:
        """
        可视化AGV协作关系网络图
        
        Args:
            collaboration_weights: 协作权重矩阵 [num_agvs, num_agvs]
            agv_positions: AGV位置列表
            agv_ids: AGV ID列表
            threshold: 显示协作关系的阈值
            title: 图表标题
            save_name: 保存文件名
            
        Returns:
            save_path: 保存路径
        """
        # 转换为numpy数组
        if isinstance(collaboration_weights, torch.Tensor):
            weights = collaboration_weights.detach().cpu().numpy()
        else:
            weights = np.array(collaboration_weights)
        
        # 创建网络图
        G = nx.DiGraph()
        
        # 添加节点
        for i, (agv_id, pos) in enumerate(zip(agv_ids, agv_positions)):
            G.add_node(agv_id, pos=pos)
        
        # 添加边（协作关系）
        for i in range(len(agv_ids)):
            for j in range(len(agv_ids)):
                if i != j and weights[i, j] > threshold:
                    G.add_edge(agv_ids[i], agv_ids[j], weight=weights[i, j])
        
        # 创建图表
        plt.figure(figsize=self.figure_size, dpi=self.dpi)
        
        # 设置节点位置
        pos = nx.get_node_attributes(G, 'pos')
        
        # 绘制节点
        nx.draw_networkx_nodes(
            G, pos,
            node_color=self.colors['agv'],
            node_size=800,
            alpha=0.8
        )
        
        # 绘制边（协作关系）
        edges = G.edges()
        edge_weights = [G[u][v]['weight'] for u, v in edges]
        
        if edge_weights:
            # 根据权重设置边的宽度和颜色
            edge_widths = [w * 5 for w in edge_weights]  # 放大显示
            edge_colors = [plt.cm.Reds(w) for w in edge_weights]
            
            nx.draw_networkx_edges(
                G, pos,
                width=edge_widths,
                edge_color=edge_colors,
                alpha=0.7,
                arrows=True,
                arrowsize=20,
                arrowstyle='->'
            )
        
        # 绘制标签
        nx.draw_networkx_labels(G, pos, font_size=10, font_weight='bold')
        
        plt.title(title, fontsize=16, fontweight='bold', pad=20)
        plt.axis('off')
        plt.tight_layout()
        
        # 保存图表
        if save_name is None:
            save_name = f"collaboration_network_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 协作网络图已保存: {save_path}")
        return save_path
    
    def visualize_attention_distribution(self,
                                       task_attention: torch.Tensor,
                                       collaboration_attention: torch.Tensor,
                                       agv_ids: List[str],
                                       title: str = "注意力分布对比",
                                       save_name: Optional[str] = None) -> str:
        """
        可视化注意力分布对比
        
        Args:
            task_attention: 任务注意力权重
            collaboration_attention: 协作注意力权重
            agv_ids: AGV ID列表
            title: 图表标题
            save_name: 保存文件名
            
        Returns:
            save_path: 保存路径
        """
        # 转换为numpy数组
        if isinstance(task_attention, torch.Tensor):
            task_weights = task_attention.detach().cpu().numpy()
        else:
            task_weights = np.array(task_attention)
            
        if isinstance(collaboration_attention, torch.Tensor):
            collab_weights = collaboration_attention.detach().cpu().numpy()
        else:
            collab_weights = np.array(collaboration_attention)
        
        # 计算每个AGV的注意力统计
        task_attention_stats = []
        collab_attention_stats = []
        
        for i in range(len(agv_ids)):
            # 任务注意力统计
            task_mean = np.mean(task_weights[i]) if task_weights.ndim > 1 else task_weights[i]
            task_max = np.max(task_weights[i]) if task_weights.ndim > 1 else task_weights[i]
            task_attention_stats.append({'AGV': agv_ids[i], 'Mean': task_mean, 'Max': task_max, 'Type': '任务注意力'})
            
            # 协作注意力统计
            collab_mean = np.mean(collab_weights[i]) if collab_weights.ndim > 1 else collab_weights[i]
            collab_max = np.max(collab_weights[i]) if collab_weights.ndim > 1 else collab_weights[i]
            collab_attention_stats.append({'AGV': agv_ids[i], 'Mean': collab_mean, 'Max': collab_max, 'Type': '协作注意力'})
        
        # 创建DataFrame
        df = pd.DataFrame(task_attention_stats + collab_attention_stats)
        
        # 创建子图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6), dpi=self.dpi)
        
        # 绘制平均注意力对比
        sns.barplot(data=df, x='AGV', y='Mean', hue='Type', ax=ax1, palette=[self.colors['task_attention'], self.colors['collaboration']])
        ax1.set_title('平均注意力对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('平均注意力权重')
        ax1.legend()
        
        # 绘制最大注意力对比
        sns.barplot(data=df, x='AGV', y='Max', hue='Type', ax=ax2, palette=[self.colors['task_attention'], self.colors['collaboration']])
        ax2.set_title('最大注意力对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('最大注意力权重')
        ax2.legend()
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存图表
        if save_name is None:
            save_name = f"attention_distribution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 注意力分布图已保存: {save_path}")
        return save_path

    def visualize_attention_timeline(self,
                                   attention_history: List[Dict[str, torch.Tensor]],
                                   agv_ids: List[str],
                                   title: str = "注意力权重时序变化",
                                   save_name: Optional[str] = None) -> str:
        """
        可视化注意力权重的时序变化

        Args:
            attention_history: 注意力历史数据列表
            agv_ids: AGV ID列表
            title: 图表标题
            save_name: 保存文件名

        Returns:
            save_path: 保存路径
        """
        # 提取时序数据
        timesteps = list(range(len(attention_history)))

        # 为每个AGV创建时序数据
        fig, axes = plt.subplots(len(agv_ids), 1, figsize=(12, 3*len(agv_ids)), dpi=self.dpi)
        if len(agv_ids) == 1:
            axes = [axes]

        for agv_idx, agv_id in enumerate(agv_ids):
            ax = axes[agv_idx]

            # 提取该AGV的任务注意力和协作注意力
            task_attention_values = []
            collab_attention_values = []

            for step_data in attention_history:
                if 'task_attention' in step_data and agv_idx < step_data['task_attention'].shape[0]:
                    task_att = step_data['task_attention'][agv_idx].mean().item()
                    task_attention_values.append(task_att)
                else:
                    task_attention_values.append(0)

                if 'collaboration_attention' in step_data and agv_idx < step_data['collaboration_attention'].shape[0]:
                    collab_att = step_data['collaboration_attention'][agv_idx].mean().item()
                    collab_attention_values.append(collab_att)
                else:
                    collab_attention_values.append(0)

            # 绘制时序图
            ax.plot(timesteps, task_attention_values,
                   color=self.colors['task_attention'],
                   label='任务注意力', linewidth=2, marker='o', markersize=4)
            ax.plot(timesteps, collab_attention_values,
                   color=self.colors['collaboration'],
                   label='协作注意力', linewidth=2, marker='s', markersize=4)

            ax.set_title(f'{agv_id} 注意力变化', fontsize=12, fontweight='bold')
            ax.set_xlabel('时间步')
            ax.set_ylabel('注意力权重')
            ax.legend()
            ax.grid(True, alpha=0.3)

        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()

        # 保存图表
        if save_name is None:
            save_name = f"attention_timeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

        save_path = os.path.join(self.save_dir, save_name)
        plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        plt.close()

        print(f"✓ 注意力时序图已保存: {save_path}")
        return save_path

    def create_interactive_attention_dashboard(self,
                                             attention_data: Dict[str, Any],
                                             save_name: Optional[str] = None) -> str:
        """
        创建交互式注意力分析仪表板

        Args:
            attention_data: 注意力数据字典
            save_name: 保存文件名

        Returns:
            save_path: 保存路径
        """
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('任务分配注意力热图', 'AGV协作网络', '注意力分布统计', '注意力权重分析'),
            specs=[[{"type": "heatmap"}, {"type": "scatter"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )

        # 1. 任务分配注意力热图
        if 'task_attention' in attention_data:
            task_weights = attention_data['task_attention']
            if isinstance(task_weights, torch.Tensor):
                task_weights = task_weights.detach().cpu().numpy()

            fig.add_trace(
                go.Heatmap(
                    z=task_weights,
                    colorscale='YlOrRd',
                    showscale=True,
                    name="任务注意力"
                ),
                row=1, col=1
            )

        # 2. AGV协作网络（简化为散点图）
        if 'collaboration_attention' in attention_data and 'agv_positions' in attention_data:
            positions = attention_data['agv_positions']
            collab_weights = attention_data['collaboration_attention']

            if isinstance(collab_weights, torch.Tensor):
                collab_weights = collab_weights.detach().cpu().numpy()

            # 计算每个AGV的协作强度
            collab_strength = np.mean(collab_weights, axis=1)

            fig.add_trace(
                go.Scatter(
                    x=[pos[0] for pos in positions],
                    y=[pos[1] for pos in positions],
                    mode='markers+text',
                    marker=dict(
                        size=[s*50 for s in collab_strength],
                        color=collab_strength,
                        colorscale='Viridis',
                        showscale=True
                    ),
                    text=[f'AGV{i}' for i in range(len(positions))],
                    textposition="middle center",
                    name="AGV协作强度"
                ),
                row=1, col=2
            )

        # 3. 注意力分布统计
        if 'attention_stats' in attention_data:
            stats = attention_data['attention_stats']

            fig.add_trace(
                go.Bar(
                    x=list(stats.keys()),
                    y=list(stats.values()),
                    marker_color=self.colors['task_attention'],
                    name="注意力统计"
                ),
                row=2, col=1
            )

        # 4. 注意力权重分析
        if 'attention_analysis' in attention_data:
            analysis = attention_data['attention_analysis']

            fig.add_trace(
                go.Scatter(
                    x=analysis.get('x', []),
                    y=analysis.get('y', []),
                    mode='markers',
                    marker=dict(
                        size=10,
                        color=self.colors['collaboration']
                    ),
                    name="注意力分析"
                ),
                row=2, col=2
            )

        # 更新布局
        fig.update_layout(
            title_text="双层注意力机制分析仪表板",
            title_x=0.5,
            height=800,
            showlegend=True
        )

        # 保存交互式图表
        if save_name is None:
            save_name = f"attention_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

        save_path = os.path.join(self.save_dir, save_name)
        fig.write_html(save_path)

        print(f"✓ 交互式仪表板已保存: {save_path}")
        return save_path
