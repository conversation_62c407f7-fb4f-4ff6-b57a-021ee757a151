# 性能分析与报告工具使用指南

## 概述

性能分析与报告工具是一个综合性的分析系统，提供深度性能分析、模型对比、协作效率评估和自动报告生成功能。该工具专为多AGV强化学习系统设计，能够全面评估算法性能和系统表现。

## 系统架构

### 核心组件

1. **PerformanceAnalyzer** - 性能分析器
2. **ReportGenerator** - 报告生成器
3. **CollaborationAnalyzer** - 协作效率分析器

### 分析维度

- **基础性能**: 回合奖励、成功率、任务完成率
- **协作效率**: 空间利用、时间同步、冲突处理
- **注意力机制**: 熵值、稀疏性、稳定性
- **计算效率**: 推理时间、内存使用

## 快速开始

### 1. 导入模块

```python
from src.analysis import (
    PerformanceAnalyzer, 
    ReportGenerator, 
    CollaborationAnalyzer
)
from src.visualization import TrajectoryCollector
```

### 2. 基本使用流程

#### 性能分析

```python
# 创建性能分析器
analyzer = PerformanceAnalyzer(
    metrics_collector=metrics_collector,
    trajectory_collector=trajectory_collector,
    attention_extractor=attention_extractor
)

# 分析单个回合
episode_data = trajectory_collector.load_episode_data(episode_id)
metrics = analyzer.analyze_episode_performance(episode_id, episode_data)

print(f"回合奖励: {metrics.episode_reward}")
print(f"成功率: {metrics.success_rate}")
print(f"协作效率: {metrics.coordination_efficiency}")
```

#### 模型对比

```python
# 准备多个模型的结果
model_results = {
    'Model_A': [metrics_list_a],
    'Model_B': [metrics_list_b],
    'Model_C': [metrics_list_c]
}

# 执行模型对比
comparison = analyzer.compare_models(model_results)

# 查看对比结果
for metric_name, values in comparison.metrics.items():
    print(f"{metric_name}: {values}")

# 查看建议
for recommendation in comparison.recommendations:
    print(f"建议: {recommendation}")
```

#### 协作效率分析

```python
# 创建协作分析器
collab_analyzer = CollaborationAnalyzer(trajectory_collector)

# 分析单个回合的协作效率
collab_metrics = collab_analyzer.analyze_collaboration(episode_data)

print(f"空间效率: {collab_metrics.spatial_efficiency}")
print(f"团队表现: {collab_metrics.team_performance_score}")
print(f"协调开销: {collab_metrics.coordination_overhead}")

# 生成协作网络图
graph = collab_analyzer.generate_collaboration_network(
    episode_data, 
    save_path="collaboration_network.png"
)
```

#### 自动报告生成

```python
# 创建报告生成器
report_generator = ReportGenerator(output_dir="./reports")

# 生成性能分析报告
performance_report = report_generator.generate_performance_report(
    analyzer=analyzer,
    report_name="training_performance",
    include_charts=True,
    include_statistics=True
)

# 生成模型对比报告
comparison_report = report_generator.generate_model_comparison_report(
    comparison=comparison,
    report_name="model_comparison"
)

print(f"性能报告: {performance_report}")
print(f"对比报告: {comparison_report}")
```

## 高级功能

### 1. 自定义性能指标

```python
# 扩展性能分析器
class CustomPerformanceAnalyzer(PerformanceAnalyzer):
    def _calculate_custom_metric(self, episode_data):
        # 实现自定义指标计算
        return custom_value
    
    def analyze_episode_performance(self, episode_id, episode_data):
        # 调用父类方法
        metrics = super().analyze_episode_performance(episode_id, episode_data)
        
        # 添加自定义指标
        custom_metric = self._calculate_custom_metric(episode_data)
        
        return metrics
```

### 2. 批量分析

```python
def batch_analysis(analyzer, episode_ids):
    """批量分析多个回合"""
    results = []
    
    for episode_id in episode_ids:
        episode_data = trajectory_collector.load_episode_data(episode_id)
        if episode_data:
            metrics = analyzer.analyze_episode_performance(episode_id, episode_data)
            results.append(metrics)
    
    return results

# 使用示例
episode_ids = range(100, 200)  # 分析第100-199回合
batch_results = batch_analysis(analyzer, episode_ids)
```

### 3. 实时性能监控

```python
class RealTimeAnalyzer:
    def __init__(self, analyzer, report_generator):
        self.analyzer = analyzer
        self.report_generator = report_generator
        self.analysis_interval = 10  # 每10个回合分析一次
    
    def on_episode_complete(self, episode_id, episode_data):
        """回合完成时的回调"""
        # 分析当前回合
        metrics = self.analyzer.analyze_episode_performance(episode_id, episode_data)
        
        # 定期生成报告
        if episode_id % self.analysis_interval == 0:
            self._generate_interim_report(episode_id)
    
    def _generate_interim_report(self, episode_id):
        """生成中期报告"""
        report_path = self.report_generator.generate_performance_report(
            analyzer=self.analyzer,
            report_name=f"interim_report_ep{episode_id}",
            include_charts=True,
            include_statistics=True
        )
        print(f"中期报告已生成: {report_path}")
```

## 性能指标详解

### 基础性能指标

- **episode_reward**: 回合总奖励
- **episode_length**: 回合长度（步数）
- **success_rate**: 任务成功率
- **task_completion_rate**: 任务完成率

### 协作效率指标

- **spatial_efficiency**: 空间利用效率
- **path_overlap_ratio**: 路径重叠比例
- **synchronization_score**: 同步性得分
- **coordination_efficiency**: 协作效率

### 注意力机制指标

- **attention_entropy**: 注意力熵（多样性）
- **attention_sparsity**: 注意力稀疏性
- **attention_stability**: 注意力稳定性

### 计算效率指标

- **inference_time**: 推理时间
- **memory_usage**: 内存使用量

## 报告类型

### 1. 性能分析报告

包含内容：
- 执行摘要和关键指标
- 性能趋势图表
- 统计分析结果
- 结论与改进建议

生成文件：
- HTML报告文件
- 性能图表（PNG）
- 原始数据（CSV）

### 2. 模型对比报告

包含内容：
- 多模型性能对比
- 雷达图和柱状图
- 统计检验结果
- 模型选择建议

### 3. 协作效率报告

包含内容：
- 协作网络图
- 效率指标分析
- 协作模式识别
- 优化建议

## 集成示例

### 与训练循环集成

```python
def train_with_analysis():
    # 初始化组件
    analyzer = PerformanceAnalyzer()
    collab_analyzer = CollaborationAnalyzer()
    report_generator = ReportGenerator()
    
    for episode in range(num_episodes):
        # 执行训练
        episode_data = run_training_episode(episode)
        
        # 性能分析
        metrics = analyzer.analyze_episode_performance(episode, episode_data)
        collab_metrics = collab_analyzer.analyze_collaboration(episode_data)
        
        # 记录关键指标
        logger.info(f"Episode {episode}: Reward={metrics.episode_reward:.2f}, "
                   f"Success={metrics.success_rate:.2f}")
        
        # 定期生成报告
        if episode % 50 == 0 and episode > 0:
            report_path = report_generator.generate_performance_report(
                analyzer=analyzer,
                report_name=f"training_progress_ep{episode}"
            )
            print(f"训练进度报告: {report_path}")
    
    # 最终报告
    final_report = report_generator.generate_performance_report(
        analyzer=analyzer,
        report_name="final_training_report"
    )
    print(f"最终训练报告: {final_report}")
```

### 与评估流程集成

```python
def evaluate_models(model_configs):
    """评估多个模型配置"""
    analyzer = PerformanceAnalyzer()
    model_results = {}
    
    for config_name, config in model_configs.items():
        print(f"评估模型: {config_name}")
        
        # 加载模型并评估
        model = load_model(config)
        episode_metrics = []
        
        for eval_episode in range(eval_episodes):
            episode_data = evaluate_model(model, eval_episode)
            metrics = analyzer.analyze_episode_performance(eval_episode, episode_data)
            episode_metrics.append(metrics)
        
        model_results[config_name] = episode_metrics
    
    # 模型对比
    comparison = analyzer.compare_models(model_results)
    
    # 生成对比报告
    report_generator = ReportGenerator()
    comparison_report = report_generator.generate_model_comparison_report(
        comparison=comparison,
        report_name="model_evaluation_comparison"
    )
    
    return comparison, comparison_report
```

## 最佳实践

### 1. 数据质量保证

```python
def validate_episode_data(episode_data):
    """验证回合数据质量"""
    required_keys = ['metadata', 'trajectories', 'task_events']
    
    for key in required_keys:
        if key not in episode_data:
            raise ValueError(f"缺少必要数据: {key}")
    
    # 验证轨迹数据完整性
    trajectories = episode_data['trajectories']
    if not trajectories:
        raise ValueError("轨迹数据为空")
    
    for agv_id, trajectory in trajectories.items():
        if not trajectory:
            print(f"警告: AGV {agv_id} 轨迹为空")
```

### 2. 性能优化

```python
# 使用缓存避免重复计算
from functools import lru_cache

class OptimizedPerformanceAnalyzer(PerformanceAnalyzer):
    @lru_cache(maxsize=100)
    def _cached_analysis(self, episode_id, data_hash):
        """缓存分析结果"""
        return self._perform_analysis(episode_id)
    
    def analyze_episode_performance(self, episode_id, episode_data):
        # 计算数据哈希
        data_hash = hash(str(episode_data))
        
        # 使用缓存
        return self._cached_analysis(episode_id, data_hash)
```

### 3. 错误处理

```python
def robust_analysis(analyzer, episode_data):
    """健壮的分析流程"""
    try:
        # 数据验证
        validate_episode_data(episode_data)
        
        # 执行分析
        metrics = analyzer.analyze_episode_performance(
            episode_data['metadata']['episode_id'], 
            episode_data
        )
        
        return metrics
        
    except ValueError as e:
        print(f"数据错误: {e}")
        return None
    except Exception as e:
        print(f"分析错误: {e}")
        return None
```

## 故障排除

### 常见问题

1. **内存不足**
   - 减少批量分析的回合数
   - 使用数据流式处理
   - 清理不必要的缓存

2. **图表生成失败**
   - 检查matplotlib配置
   - 确保输出目录存在
   - 验证数据格式正确性

3. **统计检验错误**
   - 确保样本数量充足
   - 检查数据分布
   - 处理异常值

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 数据检查
def debug_episode_data(episode_data):
    print(f"回合ID: {episode_data['metadata']['episode_id']}")
    print(f"AGV数量: {len(episode_data['trajectories'])}")
    print(f"总步数: {episode_data['metadata']['total_steps']}")
    
    for agv_id, trajectory in episode_data['trajectories'].items():
        print(f"AGV {agv_id}: {len(trajectory)} 个轨迹点")
```

## 总结

性能分析与报告工具提供了全面的分析能力，支持：

- ✅ **多维度性能分析**: 基础性能、协作效率、注意力机制
- ✅ **统计学模型对比**: 科学的模型评估和选择
- ✅ **自动化报告生成**: 专业的HTML报告和可视化图表
- ✅ **实时监控集成**: 与训练流程无缝集成
- ✅ **协作网络分析**: AGV协作关系可视化
- ✅ **趋势模式识别**: 长期性能趋势分析

该工具为多AGV强化学习系统的性能评估和优化提供了强大的支持，是研究和开发过程中不可或缺的分析利器。
