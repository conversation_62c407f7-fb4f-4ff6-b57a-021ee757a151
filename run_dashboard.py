#!/usr/bin/env python3
"""
AGV训练监控仪表板启动脚本
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.dashboard.web_dashboard import create_dashboard


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AGV训练监控仪表板")
    
    parser.add_argument("--host", type=str, default="localhost",
                       help="服务器主机地址 (默认: localhost)")
    parser.add_argument("--port", type=int, default=8000,
                       help="服务器端口 (默认: 8000)")
    parser.add_argument("--debug", action="store_true",
                       help="启用调试模式")
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🚀 AGV训练监控仪表板")
    print("=" * 80)
    print(f"📡 主机地址: {args.host}")
    print(f"🔌 端口: {args.port}")
    print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
    print("=" * 80)
    
    try:
        # 创建并运行仪表板
        dashboard = create_dashboard(host=args.host, port=args.port)
        dashboard.run(debug=args.debug)
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在关闭仪表板...")
    except Exception as e:
        print(f"❌ 启动仪表板时出错: {e}")
        sys.exit(1)
    
    print("✅ 仪表板已关闭")


if __name__ == "__main__":
    main()
