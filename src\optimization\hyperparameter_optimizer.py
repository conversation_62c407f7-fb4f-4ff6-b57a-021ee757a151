"""
超参数优化器
实现系统性的超参数优化，包括注意力机制参数、训练参数和网络架构参数的调优
"""

import os
import json
import time
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed
import optuna
from optuna.samplers import TPESampler
from optuna.pruners import MedianPruner

from ..utils.logger import Logger
from ..utils.metrics import MetricsCalculator
from config.mappo_config import MAPPOConfig


@dataclass
class HyperparameterSpace:
    """超参数搜索空间定义"""
    
    # 注意力机制参数
    attention_feature_dim: List[int] = None
    attention_num_heads: List[int] = None
    attention_dropout: List[float] = None
    attention_pos_dim: List[int] = None
    
    # 网络架构参数
    hidden_dim: List[int] = None
    num_layers: List[int] = None
    activation: List[str] = None
    
    # 训练参数
    learning_rate: List[float] = None
    batch_size: List[int] = None
    num_epochs: List[int] = None
    clip_param: List[float] = None
    entropy_coeff: List[float] = None
    value_loss_coeff: List[float] = None
    
    # 注意力特定参数
    near_threshold: List[float] = None
    far_threshold: List[float] = None
    temperature_scale: List[float] = None
    
    def __post_init__(self):
        """设置默认搜索空间"""
        if self.attention_feature_dim is None:
            self.attention_feature_dim = [32, 64, 128]
        if self.attention_num_heads is None:
            self.attention_num_heads = [4, 8, 16]
        if self.attention_dropout is None:
            self.attention_dropout = [0.0, 0.1, 0.2]
        if self.attention_pos_dim is None:
            self.attention_pos_dim = [16, 32, 64]
            
        if self.hidden_dim is None:
            self.hidden_dim = [128, 256, 512]
        if self.num_layers is None:
            self.num_layers = [2, 3, 4]
        if self.activation is None:
            self.activation = ['relu', 'tanh', 'gelu']
            
        if self.learning_rate is None:
            self.learning_rate = [1e-4, 3e-4, 5e-4, 1e-3]
        if self.batch_size is None:
            self.batch_size = [64, 128, 256, 512]
        if self.num_epochs is None:
            self.num_epochs = [5, 10, 15, 20]
        if self.clip_param is None:
            self.clip_param = [0.1, 0.2, 0.3]
        if self.entropy_coeff is None:
            self.entropy_coeff = [0.001, 0.01, 0.1]
        if self.value_loss_coeff is None:
            self.value_loss_coeff = [0.25, 0.5, 1.0]
            
        if self.near_threshold is None:
            self.near_threshold = [2.0, 3.0, 5.0]
        if self.far_threshold is None:
            self.far_threshold = [8.0, 10.0, 15.0]
        if self.temperature_scale is None:
            self.temperature_scale = [1.0, 2.0, 3.0]


@dataclass
class OptimizationResult:
    """优化结果"""
    best_params: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    total_trials: int
    optimization_time: float
    convergence_info: Dict[str, Any]


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, 
                 search_space: HyperparameterSpace,
                 optimization_method: str = "optuna",
                 n_trials: int = 100,
                 n_jobs: int = 1,
                 random_seed: int = 42):
        """
        初始化超参数优化器
        
        Args:
            search_space: 超参数搜索空间
            optimization_method: 优化方法 ("optuna", "grid", "random")
            n_trials: 试验次数
            n_jobs: 并行作业数
            random_seed: 随机种子
        """
        self.search_space = search_space
        self.optimization_method = optimization_method
        self.n_trials = n_trials
        self.n_jobs = n_jobs
        self.random_seed = random_seed
        
        # 设置随机种子
        np.random.seed(random_seed)
        torch.manual_seed(random_seed)
        
        # 初始化日志和指标
        self.logger = Logger("HyperparameterOptimizer")
        self.metrics_calculator = MetricsCalculator()
        
        # 优化历史
        self.optimization_history = []
        self.best_params = None
        self.best_score = float('-inf')
        
        self.logger.info(f"超参数优化器初始化完成，方法: {optimization_method}, 试验次数: {n_trials}")
    
    def optimize(self, 
                 objective_function: callable,
                 direction: str = "maximize",
                 timeout: Optional[float] = None) -> OptimizationResult:
        """
        执行超参数优化
        
        Args:
            objective_function: 目标函数，接收超参数字典，返回评分
            direction: 优化方向 ("maximize" 或 "minimize")
            timeout: 超时时间（秒）
            
        Returns:
            optimization_result: 优化结果
        """
        self.logger.info(f"开始超参数优化，方法: {self.optimization_method}")
        start_time = time.time()
        
        if self.optimization_method == "optuna":
            result = self._optimize_with_optuna(objective_function, direction, timeout)
        elif self.optimization_method == "grid":
            result = self._optimize_with_grid_search(objective_function, direction)
        elif self.optimization_method == "random":
            result = self._optimize_with_random_search(objective_function, direction)
        else:
            raise ValueError(f"不支持的优化方法: {self.optimization_method}")
        
        optimization_time = time.time() - start_time
        
        # 构建优化结果
        optimization_result = OptimizationResult(
            best_params=self.best_params,
            best_score=self.best_score,
            optimization_history=self.optimization_history,
            total_trials=len(self.optimization_history),
            optimization_time=optimization_time,
            convergence_info=self._analyze_convergence()
        )
        
        self.logger.info(f"超参数优化完成，最佳得分: {self.best_score:.4f}, 用时: {optimization_time:.2f}s")
        
        return optimization_result
    
    def _optimize_with_optuna(self, 
                             objective_function: callable,
                             direction: str,
                             timeout: Optional[float]) -> Dict[str, Any]:
        """使用Optuna进行贝叶斯优化"""
        
        def optuna_objective(trial):
            # 从搜索空间中采样超参数
            params = self._sample_params_optuna(trial)
            
            # 评估目标函数
            score = objective_function(params)
            
            # 记录历史
            self.optimization_history.append({
                'trial': trial.number,
                'params': params,
                'score': score,
                'timestamp': datetime.now().isoformat()
            })
            
            # 更新最佳结果
            if (direction == "maximize" and score > self.best_score) or \
               (direction == "minimize" and score < self.best_score):
                self.best_score = score
                self.best_params = params.copy()
            
            return score
        
        # 创建Optuna研究
        study = optuna.create_study(
            direction=direction,
            sampler=TPESampler(seed=self.random_seed),
            pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
        
        # 执行优化
        study.optimize(
            optuna_objective,
            n_trials=self.n_trials,
            timeout=timeout,
            n_jobs=self.n_jobs
        )
        
        return {"study": study}
    
    def _optimize_with_grid_search(self, 
                                  objective_function: callable,
                                  direction: str) -> Dict[str, Any]:
        """使用网格搜索进行优化"""
        
        # 生成所有参数组合
        param_combinations = self._generate_grid_combinations()
        
        self.logger.info(f"网格搜索: 总共 {len(param_combinations)} 个参数组合")
        
        # 评估所有组合
        for i, params in enumerate(param_combinations):
            if i >= self.n_trials:
                break
                
            score = objective_function(params)
            
            # 记录历史
            self.optimization_history.append({
                'trial': i,
                'params': params,
                'score': score,
                'timestamp': datetime.now().isoformat()
            })
            
            # 更新最佳结果
            if (direction == "maximize" and score > self.best_score) or \
               (direction == "minimize" and score < self.best_score):
                self.best_score = score
                self.best_params = params.copy()
            
            if (i + 1) % 10 == 0:
                self.logger.info(f"网格搜索进度: {i+1}/{min(self.n_trials, len(param_combinations))}")
        
        return {"total_combinations": len(param_combinations)}
    
    def _optimize_with_random_search(self, 
                                    objective_function: callable,
                                    direction: str) -> Dict[str, Any]:
        """使用随机搜索进行优化"""
        
        for i in range(self.n_trials):
            # 随机采样参数
            params = self._sample_params_random()
            
            # 评估目标函数
            score = objective_function(params)
            
            # 记录历史
            self.optimization_history.append({
                'trial': i,
                'params': params,
                'score': score,
                'timestamp': datetime.now().isoformat()
            })
            
            # 更新最佳结果
            if (direction == "maximize" and score > self.best_score) or \
               (direction == "minimize" and score < self.best_score):
                self.best_score = score
                self.best_params = params.copy()
            
            if (i + 1) % 10 == 0:
                self.logger.info(f"随机搜索进度: {i+1}/{self.n_trials}")
        
        return {}
    
    def _sample_params_optuna(self, trial) -> Dict[str, Any]:
        """使用Optuna采样参数"""
        params = {}
        
        # 注意力机制参数
        params['attention_feature_dim'] = trial.suggest_categorical(
            'attention_feature_dim', self.search_space.attention_feature_dim)
        params['attention_num_heads'] = trial.suggest_categorical(
            'attention_num_heads', self.search_space.attention_num_heads)
        params['attention_dropout'] = trial.suggest_categorical(
            'attention_dropout', self.search_space.attention_dropout)
        params['attention_pos_dim'] = trial.suggest_categorical(
            'attention_pos_dim', self.search_space.attention_pos_dim)
        
        # 网络架构参数
        params['hidden_dim'] = trial.suggest_categorical(
            'hidden_dim', self.search_space.hidden_dim)
        params['num_layers'] = trial.suggest_categorical(
            'num_layers', self.search_space.num_layers)
        params['activation'] = trial.suggest_categorical(
            'activation', self.search_space.activation)
        
        # 训练参数
        params['learning_rate'] = trial.suggest_categorical(
            'learning_rate', self.search_space.learning_rate)
        params['batch_size'] = trial.suggest_categorical(
            'batch_size', self.search_space.batch_size)
        params['num_epochs'] = trial.suggest_categorical(
            'num_epochs', self.search_space.num_epochs)
        params['clip_param'] = trial.suggest_categorical(
            'clip_param', self.search_space.clip_param)
        params['entropy_coeff'] = trial.suggest_categorical(
            'entropy_coeff', self.search_space.entropy_coeff)
        params['value_loss_coeff'] = trial.suggest_categorical(
            'value_loss_coeff', self.search_space.value_loss_coeff)
        
        # 注意力特定参数
        params['near_threshold'] = trial.suggest_categorical(
            'near_threshold', self.search_space.near_threshold)
        params['far_threshold'] = trial.suggest_categorical(
            'far_threshold', self.search_space.far_threshold)
        params['temperature_scale'] = trial.suggest_categorical(
            'temperature_scale', self.search_space.temperature_scale)
        
        return params
    
    def _sample_params_random(self) -> Dict[str, Any]:
        """随机采样参数"""
        params = {}
        
        # 注意力机制参数
        params['attention_feature_dim'] = np.random.choice(self.search_space.attention_feature_dim)
        params['attention_num_heads'] = np.random.choice(self.search_space.attention_num_heads)
        params['attention_dropout'] = np.random.choice(self.search_space.attention_dropout)
        params['attention_pos_dim'] = np.random.choice(self.search_space.attention_pos_dim)
        
        # 网络架构参数
        params['hidden_dim'] = np.random.choice(self.search_space.hidden_dim)
        params['num_layers'] = np.random.choice(self.search_space.num_layers)
        params['activation'] = np.random.choice(self.search_space.activation)
        
        # 训练参数
        params['learning_rate'] = np.random.choice(self.search_space.learning_rate)
        params['batch_size'] = np.random.choice(self.search_space.batch_size)
        params['num_epochs'] = np.random.choice(self.search_space.num_epochs)
        params['clip_param'] = np.random.choice(self.search_space.clip_param)
        params['entropy_coeff'] = np.random.choice(self.search_space.entropy_coeff)
        params['value_loss_coeff'] = np.random.choice(self.search_space.value_loss_coeff)
        
        # 注意力特定参数
        params['near_threshold'] = np.random.choice(self.search_space.near_threshold)
        params['far_threshold'] = np.random.choice(self.search_space.far_threshold)
        params['temperature_scale'] = np.random.choice(self.search_space.temperature_scale)
        
        return params
    
    def _generate_grid_combinations(self) -> List[Dict[str, Any]]:
        """生成网格搜索的所有参数组合"""
        param_names = []
        param_values = []
        
        # 收集所有参数
        for field_name, field_value in asdict(self.search_space).items():
            if field_value is not None:
                param_names.append(field_name)
                param_values.append(field_value)
        
        # 生成所有组合
        combinations = []
        for combination in itertools.product(*param_values):
            params = dict(zip(param_names, combination))
            combinations.append(params)
        
        return combinations
    
    def _analyze_convergence(self) -> Dict[str, Any]:
        """分析收敛性"""
        if not self.optimization_history:
            return {}
        
        scores = [trial['score'] for trial in self.optimization_history]
        
        # 计算收敛指标
        convergence_info = {
            'total_trials': len(scores),
            'best_score': self.best_score,
            'score_std': np.std(scores),
            'score_range': [min(scores), max(scores)],
            'improvement_rate': self._calculate_improvement_rate(scores),
            'convergence_trial': self._find_convergence_trial(scores)
        }
        
        return convergence_info
    
    def _calculate_improvement_rate(self, scores: List[float]) -> float:
        """计算改进率"""
        if len(scores) < 2:
            return 0.0
        
        improvements = 0
        for i in range(1, len(scores)):
            if scores[i] > max(scores[:i]):
                improvements += 1
        
        return improvements / (len(scores) - 1)
    
    def _find_convergence_trial(self, scores: List[float]) -> int:
        """找到收敛的试验编号"""
        if len(scores) < 10:
            return len(scores)
        
        # 寻找最后一次显著改进的位置
        best_score = max(scores)
        for i in range(len(scores) - 1, -1, -1):
            if scores[i] == best_score:
                return i
        
        return len(scores)
    
    def save_results(self, result: OptimizationResult, save_path: str):
        """保存优化结果"""
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 准备保存数据
        save_data = {
            'best_params': result.best_params,
            'best_score': result.best_score,
            'optimization_history': result.optimization_history,
            'total_trials': result.total_trials,
            'optimization_time': result.optimization_time,
            'convergence_info': result.convergence_info,
            'search_space': asdict(self.search_space),
            'optimization_config': {
                'method': self.optimization_method,
                'n_trials': self.n_trials,
                'n_jobs': self.n_jobs,
                'random_seed': self.random_seed
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存到文件
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"优化结果已保存到: {save_path}")
    
    def load_results(self, load_path: str) -> OptimizationResult:
        """加载优化结果"""
        with open(load_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        result = OptimizationResult(
            best_params=data['best_params'],
            best_score=data['best_score'],
            optimization_history=data['optimization_history'],
            total_trials=data['total_trials'],
            optimization_time=data['optimization_time'],
            convergence_info=data['convergence_info']
        )
        
        self.logger.info(f"优化结果已从 {load_path} 加载")
        
        return result
