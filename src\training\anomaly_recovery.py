"""
异常检测与恢复系统
检测训练过程中的异常并自动恢复
"""

import torch
import torch.nn as nn
import numpy as np
import time
import logging
import pickle
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass
from pathlib import Path
from collections import deque
import copy


@dataclass
class CheckpointInfo:
    """检查点信息"""
    step: int
    timestamp: float
    model_state: Dict[str, Any]
    optimizer_state: Dict[str, Any]
    metrics: Dict[str, float]
    file_path: str


@dataclass
class AnomalyEvent:
    """异常事件"""
    timestamp: float
    step: int
    anomaly_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    metrics: Dict[str, float]
    recovery_action: Optional[str] = None


class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, 
                 checkpoint_dir: str = "./checkpoints",
                 max_checkpoints: int = 10,
                 save_frequency: int = 100):
        """
        初始化检查点管理器
        
        Args:
            checkpoint_dir: 检查点目录
            max_checkpoints: 最大检查点数量
            save_frequency: 保存频率
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_checkpoints = max_checkpoints
        self.save_frequency = save_frequency
        
        # 检查点历史
        self.checkpoints = deque(maxlen=max_checkpoints)
        self.last_save_step = 0
        
        # 最佳检查点
        self.best_checkpoint = None
        self.best_metric_value = float('-inf')
        
    def should_save(self, step: int) -> bool:
        """判断是否应该保存检查点"""
        return step - self.last_save_step >= self.save_frequency
    
    def save_checkpoint(self, 
                       step: int,
                       model: nn.Module,
                       optimizer: torch.optim.Optimizer,
                       metrics: Dict[str, float],
                       is_best: bool = False) -> str:
        """
        保存检查点
        
        Args:
            step: 训练步数
            model: 模型
            optimizer: 优化器
            metrics: 指标
            is_best: 是否为最佳检查点
            
        Returns:
            checkpoint_path: 检查点路径
        """
        timestamp = time.time()
        
        # 创建检查点文件名
        if is_best:
            filename = f"best_checkpoint_step_{step}.pt"
        else:
            filename = f"checkpoint_step_{step}.pt"
        
        checkpoint_path = self.checkpoint_dir / filename
        
        # 保存检查点
        checkpoint_data = {
            'step': step,
            'timestamp': timestamp,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'metrics': metrics
        }
        
        torch.save(checkpoint_data, checkpoint_path)
        
        # 创建检查点信息
        checkpoint_info = CheckpointInfo(
            step=step,
            timestamp=timestamp,
            model_state=copy.deepcopy(model.state_dict()),
            optimizer_state=copy.deepcopy(optimizer.state_dict()),
            metrics=metrics.copy(),
            file_path=str(checkpoint_path)
        )
        
        # 添加到历史
        self.checkpoints.append(checkpoint_info)
        self.last_save_step = step
        
        # 更新最佳检查点
        current_metric = metrics.get('episode_reward', float('-inf'))
        if is_best or current_metric > self.best_metric_value:
            self.best_checkpoint = checkpoint_info
            self.best_metric_value = current_metric
        
        # 清理旧检查点
        self._cleanup_old_checkpoints()
        
        logging.info(f"Checkpoint saved: {checkpoint_path}")
        return str(checkpoint_path)
    
    def load_checkpoint(self, 
                       checkpoint_path: str,
                       model: nn.Module,
                       optimizer: torch.optim.Optimizer) -> Dict[str, Any]:
        """
        加载检查点
        
        Args:
            checkpoint_path: 检查点路径
            model: 模型
            optimizer: 优化器
            
        Returns:
            checkpoint_data: 检查点数据
        """
        checkpoint_data = torch.load(checkpoint_path, map_location='cpu')
        
        # 加载模型状态
        model.load_state_dict(checkpoint_data['model_state_dict'])
        
        # 加载优化器状态
        optimizer.load_state_dict(checkpoint_data['optimizer_state_dict'])
        
        logging.info(f"Checkpoint loaded: {checkpoint_path}")
        return checkpoint_data
    
    def get_latest_checkpoint(self) -> Optional[CheckpointInfo]:
        """获取最新检查点"""
        return self.checkpoints[-1] if self.checkpoints else None
    
    def get_best_checkpoint(self) -> Optional[CheckpointInfo]:
        """获取最佳检查点"""
        return self.best_checkpoint
    
    def _cleanup_old_checkpoints(self):
        """清理旧检查点文件"""
        # 获取所有检查点文件
        checkpoint_files = list(self.checkpoint_dir.glob("checkpoint_step_*.pt"))
        
        # 按修改时间排序
        checkpoint_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除超出限制的文件
        for file_path in checkpoint_files[self.max_checkpoints:]:
            try:
                file_path.unlink()
                logging.info(f"Deleted old checkpoint: {file_path}")
            except Exception as e:
                logging.warning(f"Failed to delete checkpoint {file_path}: {e}")


class AnomalyDetector:
    """高级异常检测器"""
    
    def __init__(self, 
                 loss_spike_threshold: float = 2.0,
                 grad_explosion_threshold: float = 100.0,
                 grad_vanishing_threshold: float = 1e-6,
                 performance_drop_threshold: float = 0.5,
                 window_size: int = 50):
        """
        初始化异常检测器
        
        Args:
            loss_spike_threshold: 损失突增阈值
            grad_explosion_threshold: 梯度爆炸阈值
            grad_vanishing_threshold: 梯度消失阈值
            performance_drop_threshold: 性能下降阈值
            window_size: 统计窗口大小
        """
        self.loss_spike_threshold = loss_spike_threshold
        self.grad_explosion_threshold = grad_explosion_threshold
        self.grad_vanishing_threshold = grad_vanishing_threshold
        self.performance_drop_threshold = performance_drop_threshold
        self.window_size = window_size
        
        # 历史数据
        self.loss_history = deque(maxlen=window_size)
        self.grad_history = deque(maxlen=window_size)
        self.reward_history = deque(maxlen=window_size)
        
        # 异常事件历史
        self.anomaly_events = []
        
        # 基线统计
        self.baseline_loss = None
        self.baseline_reward = None
        
    def detect_anomalies(self, 
                        step: int,
                        loss: float,
                        grad_norm: float,
                        reward: float,
                        model: nn.Module) -> List[AnomalyEvent]:
        """
        检测异常
        
        Args:
            step: 训练步数
            loss: 损失值
            grad_norm: 梯度范数
            reward: 奖励
            model: 模型
            
        Returns:
            anomalies: 检测到的异常列表
        """
        anomalies = []
        timestamp = time.time()
        
        # 更新历史
        self.loss_history.append(loss)
        self.grad_history.append(grad_norm)
        self.reward_history.append(reward)
        
        # 更新基线
        if len(self.loss_history) >= 20:
            self.baseline_loss = np.median(list(self.loss_history)[:20])
            self.baseline_reward = np.median(list(self.reward_history)[:20])
        
        # 检测NaN/Inf
        nan_inf_anomaly = self._detect_nan_inf(step, timestamp, loss, grad_norm, reward, model)
        if nan_inf_anomaly:
            anomalies.append(nan_inf_anomaly)
        
        # 检测损失异常
        loss_anomaly = self._detect_loss_anomaly(step, timestamp, loss)
        if loss_anomaly:
            anomalies.append(loss_anomaly)
        
        # 检测梯度异常
        grad_anomaly = self._detect_gradient_anomaly(step, timestamp, grad_norm)
        if grad_anomaly:
            anomalies.append(grad_anomaly)
        
        # 检测性能下降
        performance_anomaly = self._detect_performance_drop(step, timestamp, reward)
        if performance_anomaly:
            anomalies.append(performance_anomaly)
        
        # 记录异常事件
        for anomaly in anomalies:
            self.anomaly_events.append(anomaly)
        
        return anomalies
    
    def _detect_nan_inf(self, step: int, timestamp: float, loss: float, 
                       grad_norm: float, reward: float, model: nn.Module) -> Optional[AnomalyEvent]:
        """检测NaN/Inf异常"""
        nan_inf_values = []
        
        # 检查标量值
        if np.isnan(loss) or np.isinf(loss):
            nan_inf_values.append(f"loss: {loss}")
        if np.isnan(grad_norm) or np.isinf(grad_norm):
            nan_inf_values.append(f"grad_norm: {grad_norm}")
        if np.isnan(reward) or np.isinf(reward):
            nan_inf_values.append(f"reward: {reward}")
        
        # 检查模型参数
        for name, param in model.named_parameters():
            if torch.isnan(param).any() or torch.isinf(param).any():
                nan_inf_values.append(f"param {name}")
        
        if nan_inf_values:
            return AnomalyEvent(
                timestamp=timestamp,
                step=step,
                anomaly_type="nan_inf",
                severity="critical",
                description=f"NaN/Inf detected in: {', '.join(nan_inf_values)}",
                metrics={"loss": loss, "grad_norm": grad_norm, "reward": reward}
            )
        
        return None
    
    def _detect_loss_anomaly(self, step: int, timestamp: float, loss: float) -> Optional[AnomalyEvent]:
        """检测损失异常"""
        if len(self.loss_history) < 10:
            return None
        
        recent_losses = list(self.loss_history)[-10:]
        older_losses = list(self.loss_history)[:-10] if len(self.loss_history) > 10 else recent_losses
        
        recent_mean = np.mean(recent_losses)
        older_mean = np.mean(older_losses)
        
        # 损失突增
        if recent_mean > older_mean * self.loss_spike_threshold:
            severity = "high" if recent_mean > older_mean * 5 else "medium"
            return AnomalyEvent(
                timestamp=timestamp,
                step=step,
                anomaly_type="loss_spike",
                severity=severity,
                description=f"Loss spike: {older_mean:.4f} -> {recent_mean:.4f}",
                metrics={"old_loss": older_mean, "new_loss": recent_mean, "ratio": recent_mean/older_mean}
            )
        
        return None
    
    def _detect_gradient_anomaly(self, step: int, timestamp: float, grad_norm: float) -> Optional[AnomalyEvent]:
        """检测梯度异常"""
        # 梯度爆炸
        if grad_norm > self.grad_explosion_threshold:
            return AnomalyEvent(
                timestamp=timestamp,
                step=step,
                anomaly_type="gradient_explosion",
                severity="high",
                description=f"Gradient explosion: {grad_norm:.4f}",
                metrics={"grad_norm": grad_norm}
            )
        
        # 梯度消失
        if len(self.grad_history) >= 10:
            recent_grads = list(self.grad_history)[-10:]
            if all(g < self.grad_vanishing_threshold for g in recent_grads):
                return AnomalyEvent(
                    timestamp=timestamp,
                    step=step,
                    anomaly_type="gradient_vanishing",
                    severity="medium",
                    description=f"Gradient vanishing: {np.mean(recent_grads):.8f}",
                    metrics={"avg_grad_norm": np.mean(recent_grads)}
                )
        
        return None
    
    def _detect_performance_drop(self, step: int, timestamp: float, reward: float) -> Optional[AnomalyEvent]:
        """检测性能下降"""
        if len(self.reward_history) < 20 or self.baseline_reward is None:
            return None
        
        recent_rewards = list(self.reward_history)[-10:]
        recent_mean = np.mean(recent_rewards)
        
        # 性能显著下降
        if recent_mean < self.baseline_reward * self.performance_drop_threshold:
            return AnomalyEvent(
                timestamp=timestamp,
                step=step,
                anomaly_type="performance_drop",
                severity="medium",
                description=f"Performance drop: {self.baseline_reward:.2f} -> {recent_mean:.2f}",
                metrics={"baseline_reward": self.baseline_reward, "current_reward": recent_mean}
            )
        
        return None


class RecoveryManager:
    """恢复管理器"""
    
    def __init__(self, 
                 checkpoint_manager: CheckpointManager,
                 recovery_strategies: Optional[Dict[str, Callable]] = None):
        """
        初始化恢复管理器
        
        Args:
            checkpoint_manager: 检查点管理器
            recovery_strategies: 恢复策略字典
        """
        self.checkpoint_manager = checkpoint_manager
        self.recovery_strategies = recovery_strategies or self._default_recovery_strategies()
        
        # 恢复历史
        self.recovery_history = []
        
    def _default_recovery_strategies(self) -> Dict[str, Callable]:
        """默认恢复策略"""
        return {
            "nan_inf": self._recover_from_nan_inf,
            "gradient_explosion": self._recover_from_gradient_explosion,
            "loss_spike": self._recover_from_loss_spike,
            "performance_drop": self._recover_from_performance_drop
        }
    
    def handle_anomaly(self, 
                      anomaly: AnomalyEvent,
                      model: nn.Module,
                      optimizer: torch.optim.Optimizer) -> bool:
        """
        处理异常
        
        Args:
            anomaly: 异常事件
            model: 模型
            optimizer: 优化器
            
        Returns:
            success: 是否成功恢复
        """
        recovery_strategy = self.recovery_strategies.get(anomaly.anomaly_type)
        
        if recovery_strategy is None:
            logging.warning(f"No recovery strategy for anomaly type: {anomaly.anomaly_type}")
            return False
        
        try:
            success = recovery_strategy(anomaly, model, optimizer)
            
            # 记录恢复历史
            recovery_record = {
                'timestamp': time.time(),
                'anomaly': anomaly,
                'success': success,
                'strategy': anomaly.anomaly_type
            }
            self.recovery_history.append(recovery_record)
            
            if success:
                logging.info(f"Successfully recovered from {anomaly.anomaly_type}")
            else:
                logging.error(f"Failed to recover from {anomaly.anomaly_type}")
            
            return success
            
        except Exception as e:
            logging.error(f"Recovery strategy failed: {e}")
            return False
    
    def _recover_from_nan_inf(self, anomaly: AnomalyEvent, model: nn.Module, optimizer: torch.optim.Optimizer) -> bool:
        """从NaN/Inf异常恢复"""
        # 尝试加载最近的检查点
        latest_checkpoint = self.checkpoint_manager.get_latest_checkpoint()
        
        if latest_checkpoint:
            try:
                self.checkpoint_manager.load_checkpoint(
                    latest_checkpoint.file_path, model, optimizer
                )
                logging.info(f"Recovered from NaN/Inf by loading checkpoint from step {latest_checkpoint.step}")
                return True
            except Exception as e:
                logging.error(f"Failed to load checkpoint: {e}")
        
        # 如果没有检查点，重新初始化模型
        logging.warning("No checkpoint available, reinitializing model")
        self._reinitialize_model(model)
        return True
    
    def _recover_from_gradient_explosion(self, anomaly: AnomalyEvent, model: nn.Module, optimizer: torch.optim.Optimizer) -> bool:
        """从梯度爆炸恢复"""
        # 降低学习率
        for param_group in optimizer.param_groups:
            param_group['lr'] *= 0.5
        
        logging.info(f"Reduced learning rate to {optimizer.param_groups[0]['lr']}")
        return True
    
    def _recover_from_loss_spike(self, anomaly: AnomalyEvent, model: nn.Module, optimizer: torch.optim.Optimizer) -> bool:
        """从损失突增恢复"""
        # 加载最佳检查点
        best_checkpoint = self.checkpoint_manager.get_best_checkpoint()
        
        if best_checkpoint:
            try:
                self.checkpoint_manager.load_checkpoint(
                    best_checkpoint.file_path, model, optimizer
                )
                
                # 降低学习率
                for param_group in optimizer.param_groups:
                    param_group['lr'] *= 0.8
                
                logging.info(f"Recovered from loss spike by loading best checkpoint and reducing LR")
                return True
            except Exception as e:
                logging.error(f"Failed to load best checkpoint: {e}")
        
        return False
    
    def _recover_from_performance_drop(self, anomaly: AnomalyEvent, model: nn.Module, optimizer: torch.optim.Optimizer) -> bool:
        """从性能下降恢复"""
        # 轻微降低学习率
        for param_group in optimizer.param_groups:
            param_group['lr'] *= 0.9
        
        logging.info("Reduced learning rate due to performance drop")
        return True
    
    def _reinitialize_model(self, model: nn.Module):
        """重新初始化模型"""
        for module in model.modules():
            if hasattr(module, 'reset_parameters'):
                module.reset_parameters()
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """获取恢复统计信息"""
        if not self.recovery_history:
            return {}
        
        total_recoveries = len(self.recovery_history)
        successful_recoveries = sum(1 for r in self.recovery_history if r['success'])
        
        recovery_types = {}
        for record in self.recovery_history:
            anomaly_type = record['anomaly'].anomaly_type
            recovery_types[anomaly_type] = recovery_types.get(anomaly_type, 0) + 1
        
        return {
            'total_recoveries': total_recoveries,
            'successful_recoveries': successful_recoveries,
            'success_rate': successful_recoveries / total_recoveries,
            'recovery_types': recovery_types
        }
