"""
训练配置文件
定义训练过程的各种参数和策略
"""

from dataclasses import dataclass
from typing import Dict, List, Any, Optional


@dataclass
class CurriculumConfig:
    """课程学习配置"""

    # 是否启用课程学习
    enable_curriculum: bool = True

    # 阶段配置
    stages: List[str] = None
    stage_episodes: Dict[str, int] = None
    stage_success_threshold: Dict[str, float] = None

    # 阶段切换条件
    min_episodes_per_stage: int = 100      # 每阶段最少训练回合数
    success_window: int = 50               # 成功率计算窗口
    stage_switch_patience: int = 20        # 阶段切换耐心值

    def __post_init__(self):
        """初始化后处理"""
        if self.stages is None:
            self.stages = ["stage1", "stage2", "stage3"]

        if self.stage_episodes is None:
            self.stage_episodes = {
                "stage1": 500,
                "stage2": 1000,
                "stage3": 2000
            }

        if self.stage_success_threshold is None:
            self.stage_success_threshold = {
                "stage1": 0.8,
                "stage2": 0.75,
                "stage3": 0.7
            }


@dataclass
class PretrainConfig:
    """预训练配置"""

    # 注意力预训练
    enable_attention_pretrain: bool = True
    pretrain_episodes: int = 200           # 预训练回合数
    pretrain_lr: float = 1e-3              # 预训练学习率

    # 任务分配预训练
    task_pretrain_data_size: int = 10000   # 任务分配预训练数据量
    task_pretrain_epochs: int = 50         # 任务分配预训练轮数

    # 协作感知预训练
    collab_pretrain_data_size: int = 5000  # 协作预训练数据量
    collab_pretrain_epochs: int = 30       # 协作预训练轮数

    # 预训练数据生成
    use_expert_data: bool = True           # 是否使用专家数据
    expert_data_ratio: float = 0.3         # 专家数据比例
    random_data_ratio: float = 0.7         # 随机数据比例


@dataclass
class ExperienceReplayConfig:
    """经验回放配置"""

    # 基础配置
    enable_prioritized_replay: bool = True # 是否启用优先级回放
    buffer_size: int = 100000              # 缓冲区大小
    alpha: float = 0.6                     # 优先级指数
    beta_start: float = 0.4                # 重要性采样起始值
    beta_end: float = 1.0                  # 重要性采样结束值
    beta_steps: int = 100000               # beta线性增长步数

    # 注意力感知筛选
    attention_weight_threshold: float = 0.1  # 注意力权重变化阈值
    experience_value_weight: float = 0.7   # 经验价值权重
    attention_change_weight: float = 0.3   # 注意力变化权重

    # 采样配置
    min_replay_size: int = 1000            # 最小回放大小
    sample_batch_size: int = 256           # 采样批次大小


@dataclass
class MetaLearningConfig:
    """元学习配置"""

    # MAML配置
    enable_meta_learning: bool = True      # 是否启用元学习
    meta_lr: float = 1e-3                  # 元学习率
    inner_lr: float = 1e-2                 # 内循环学习率
    inner_steps: int = 5                   # 内循环步数

    # 任务配置
    num_meta_tasks: int = 10               # 元任务数量
    task_batch_size: int = 4               # 任务批次大小

    # 快速适应
    adaptation_steps: int = 10             # 适应步数
    adaptation_lr: float = 1e-2            # 适应学习率

    # 环境变化检测
    performance_threshold: float = 0.1     # 性能下降阈值
    detection_window: int = 100            # 检测窗口大小


@dataclass
class TrainingConfig:
    """完整训练配置"""

    # 基础训练参数
    total_episodes: int = 5000             # 总训练回合数
    max_steps_per_episode: int = 1000      # 每回合最大步数
    eval_interval: int = 100               # 评估间隔
    eval_episodes: int = 10                # 评估回合数

    # 日志和监控
    log_interval: int = 10                 # 日志记录间隔
    tensorboard_log: bool = True           # 是否使用TensorBoard
    wandb_log: bool = False                # 是否使用WandB
    save_video: bool = True                # 是否保存视频
    video_interval: int = 500              # 视频保存间隔

    # 早停配置
    early_stopping: bool = True            # 是否启用早停
    patience: int = 200                    # 早停耐心值
    min_improvement: float = 0.01          # 最小改进阈值

    # 检查点配置
    save_checkpoint: bool = True           # 是否保存检查点
    checkpoint_interval: int = 500         # 检查点保存间隔
    keep_best_checkpoints: int = 3         # 保留最佳检查点数量

    # 子配置
    curriculum: CurriculumConfig = None
    pretrain: PretrainConfig = None
    experience_replay: ExperienceReplayConfig = None
    meta_learning: MetaLearningConfig = None

    # 随机种子
    seed: int = 42                         # 随机种子
    deterministic: bool = True             # 是否使用确定性算法

    def __post_init__(self):
        """初始化后处理"""
        if self.curriculum is None:
            self.curriculum = CurriculumConfig()

        if self.pretrain is None:
            self.pretrain = PretrainConfig()

        if self.experience_replay is None:
            self.experience_replay = ExperienceReplayConfig()

        if self.meta_learning is None:
            self.meta_learning = MetaLearningConfig()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_episodes": self.total_episodes,
            "max_steps_per_episode": self.max_steps_per_episode,
            "eval_interval": self.eval_interval,
            "eval_episodes": self.eval_episodes,
            "log_interval": self.log_interval,
            "tensorboard_log": self.tensorboard_log,
            "wandb_log": self.wandb_log,
            "save_video": self.save_video,
            "video_interval": self.video_interval,
            "early_stopping": self.early_stopping,
            "patience": self.patience,
            "min_improvement": self.min_improvement,
            "save_checkpoint": self.save_checkpoint,
            "checkpoint_interval": self.checkpoint_interval,
            "keep_best_checkpoints": self.keep_best_checkpoints,
            "curriculum": self.curriculum.__dict__,
            "pretrain": self.pretrain.__dict__,
            "experience_replay": self.experience_replay.__dict__,
            "meta_learning": self.meta_learning.__dict__,
            "seed": self.seed,
            "deterministic": self.deterministic
        }


# 默认训练配置实例
DEFAULT_TRAINING_CONFIG = TrainingConfig()

# 快速测试配置
QUICK_TEST_CONFIG = TrainingConfig(
    total_episodes=100,
    eval_interval=20,
    eval_episodes=3,
    log_interval=5,
    save_video=False,
    curriculum=CurriculumConfig(
        enable_curriculum=False
    ),
    pretrain=PretrainConfig(
        enable_attention_pretrain=False
    )
)