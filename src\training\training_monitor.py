"""
训练监控系统
实时监控训练过程中的各种指标和状态
"""

import torch
import numpy as np
import time
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import matplotlib.pyplot as plt
from pathlib import Path
import warnings


@dataclass
class TrainingMetrics:
    """训练指标"""
    step: int
    epoch: int
    timestamp: float
    
    # 损失指标
    total_loss: float
    policy_loss: float
    value_loss: float
    entropy_loss: float
    
    # 正则化损失
    regularization_loss: float
    attention_penalty: float
    
    # 梯度指标
    grad_norm: float
    grad_clipped: bool
    learning_rate: float
    
    # 性能指标
    episode_reward: float
    episode_length: int
    success_rate: float
    
    # 注意力指标
    task_attention_entropy: float
    collaboration_attention_entropy: float
    attention_diversity: float


class MetricsBuffer:
    """指标缓冲区"""
    
    def __init__(self, maxlen: int = 1000):
        """
        初始化指标缓冲区
        
        Args:
            maxlen: 最大长度
        """
        self.maxlen = maxlen
        self.metrics = deque(maxlen=maxlen)
        self.step_to_metrics = {}
        
    def add(self, metrics: TrainingMetrics):
        """添加指标"""
        self.metrics.append(metrics)
        self.step_to_metrics[metrics.step] = metrics
        
        # 清理旧的映射
        if len(self.step_to_metrics) > self.maxlen:
            oldest_step = min(self.step_to_metrics.keys())
            del self.step_to_metrics[oldest_step]
    
    def get_recent(self, n: int = 100) -> List[TrainingMetrics]:
        """获取最近n个指标"""
        return list(self.metrics)[-n:]
    
    def get_by_step_range(self, start_step: int, end_step: int) -> List[TrainingMetrics]:
        """根据步数范围获取指标"""
        return [m for m in self.metrics if start_step <= m.step <= end_step]
    
    def compute_statistics(self, field: str, window: int = 100) -> Dict[str, float]:
        """计算指定字段的统计信息"""
        recent_metrics = self.get_recent(window)
        if not recent_metrics:
            return {}
        
        values = [getattr(m, field) for m in recent_metrics if hasattr(m, field)]
        if not values:
            return {}
        
        return {
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values)
        }


class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self, 
                 loss_threshold: float = 10.0,
                 grad_threshold: float = 100.0,
                 nan_check: bool = True):
        """
        初始化异常检测器
        
        Args:
            loss_threshold: 损失异常阈值
            grad_threshold: 梯度异常阈值
            nan_check: 是否检查NaN
        """
        self.loss_threshold = loss_threshold
        self.grad_threshold = grad_threshold
        self.nan_check = nan_check
        
        # 历史统计
        self.loss_history = deque(maxlen=100)
        self.grad_history = deque(maxlen=100)
        
        # 异常计数
        self.anomaly_counts = defaultdict(int)
        
    def detect_anomalies(self, metrics: TrainingMetrics) -> List[str]:
        """
        检测异常
        
        Args:
            metrics: 训练指标
            
        Returns:
            anomalies: 异常列表
        """
        anomalies = []
        
        # 检查NaN
        if self.nan_check:
            for field_name, field_value in asdict(metrics).items():
                if isinstance(field_value, float) and (np.isnan(field_value) or np.isinf(field_value)):
                    anomalies.append(f"NaN/Inf detected in {field_name}: {field_value}")
                    self.anomaly_counts['nan_inf'] += 1
        
        # 检查损失异常
        if metrics.total_loss > self.loss_threshold:
            anomalies.append(f"High loss detected: {metrics.total_loss:.4f}")
            self.anomaly_counts['high_loss'] += 1
        
        # 检查梯度异常
        if metrics.grad_norm > self.grad_threshold:
            anomalies.append(f"High gradient norm: {metrics.grad_norm:.4f}")
            self.anomaly_counts['high_grad'] += 1
        
        # 检查损失突增
        self.loss_history.append(metrics.total_loss)
        if len(self.loss_history) >= 10:
            recent_mean = np.mean(list(self.loss_history)[-10:])
            older_mean = np.mean(list(self.loss_history)[:-10]) if len(self.loss_history) > 10 else recent_mean
            
            if recent_mean > older_mean * 2:  # 损失翻倍
                anomalies.append(f"Loss spike detected: {older_mean:.4f} -> {recent_mean:.4f}")
                self.anomaly_counts['loss_spike'] += 1
        
        # 检查梯度消失
        self.grad_history.append(metrics.grad_norm)
        if len(self.grad_history) >= 10:
            recent_grad_mean = np.mean(list(self.grad_history)[-10:])
            if recent_grad_mean < 1e-6:
                anomalies.append(f"Gradient vanishing detected: {recent_grad_mean:.8f}")
                self.anomaly_counts['grad_vanishing'] += 1
        
        return anomalies
    
    def get_anomaly_summary(self) -> Dict[str, int]:
        """获取异常统计摘要"""
        return dict(self.anomaly_counts)


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self):
        """初始化性能跟踪器"""
        self.episode_rewards = deque(maxlen=1000)
        self.episode_lengths = deque(maxlen=1000)
        self.success_rates = deque(maxlen=100)
        
        # 最佳性能记录
        self.best_reward = float('-inf')
        self.best_success_rate = 0.0
        
        # 性能趋势
        self.reward_trend = []
        self.success_trend = []
        
    def update(self, episode_reward: float, episode_length: int, success_rate: float):
        """
        更新性能指标
        
        Args:
            episode_reward: 回合奖励
            episode_length: 回合长度
            success_rate: 成功率
        """
        self.episode_rewards.append(episode_reward)
        self.episode_lengths.append(episode_length)
        self.success_rates.append(success_rate)
        
        # 更新最佳记录
        if episode_reward > self.best_reward:
            self.best_reward = episode_reward
        
        if success_rate > self.best_success_rate:
            self.best_success_rate = success_rate
        
        # 计算趋势（每100步计算一次）
        if len(self.episode_rewards) % 100 == 0:
            recent_reward = np.mean(list(self.episode_rewards)[-100:])
            recent_success = np.mean(list(self.success_rates)[-100:])
            
            self.reward_trend.append(recent_reward)
            self.success_trend.append(recent_success)
    
    def get_performance_summary(self) -> Dict[str, float]:
        """获取性能摘要"""
        if not self.episode_rewards:
            return {}
        
        return {
            'mean_reward': np.mean(self.episode_rewards),
            'std_reward': np.std(self.episode_rewards),
            'best_reward': self.best_reward,
            'mean_episode_length': np.mean(self.episode_lengths),
            'mean_success_rate': np.mean(self.success_rates) if self.success_rates else 0.0,
            'best_success_rate': self.best_success_rate,
            'reward_trend_slope': self._compute_trend_slope(self.reward_trend),
            'success_trend_slope': self._compute_trend_slope(self.success_trend)
        }
    
    def _compute_trend_slope(self, trend_data: List[float]) -> float:
        """计算趋势斜率"""
        if len(trend_data) < 2:
            return 0.0
        
        x = np.arange(len(trend_data))
        y = np.array(trend_data)
        
        # 线性回归计算斜率
        slope = np.polyfit(x, y, 1)[0]
        return slope


class TrainingMonitor:
    """训练监控器"""
    
    def __init__(self, 
                 log_dir: str = "./logs",
                 save_frequency: int = 100,
                 plot_frequency: int = 500):
        """
        初始化训练监控器
        
        Args:
            log_dir: 日志目录
            save_frequency: 保存频率
            plot_frequency: 绘图频率
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.save_frequency = save_frequency
        self.plot_frequency = plot_frequency
        
        # 初始化组件
        self.metrics_buffer = MetricsBuffer()
        self.anomaly_detector = AnomalyDetector()
        self.performance_tracker = PerformanceTracker()
        
        # 设置日志
        self._setup_logging()
        
        # 监控状态
        self.start_time = time.time()
        self.last_save_step = 0
        self.last_plot_step = 0
        
    def _setup_logging(self):
        """设置日志"""
        log_file = self.log_dir / "training.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def log_metrics(self, metrics: TrainingMetrics):
        """
        记录指标
        
        Args:
            metrics: 训练指标
        """
        # 添加到缓冲区
        self.metrics_buffer.add(metrics)
        
        # 更新性能跟踪器
        self.performance_tracker.update(
            metrics.episode_reward,
            metrics.episode_length,
            metrics.success_rate
        )
        
        # 异常检测
        anomalies = self.anomaly_detector.detect_anomalies(metrics)
        if anomalies:
            for anomaly in anomalies:
                self.logger.warning(f"Anomaly detected: {anomaly}")
        
        # 定期保存和绘图
        if metrics.step - self.last_save_step >= self.save_frequency:
            self._save_metrics()
            self.last_save_step = metrics.step
        
        if metrics.step - self.last_plot_step >= self.plot_frequency:
            self._create_plots()
            self.last_plot_step = metrics.step
        
        # 定期日志输出
        if metrics.step % 100 == 0:
            self._log_summary(metrics)
    
    def _log_summary(self, metrics: TrainingMetrics):
        """记录摘要信息"""
        elapsed_time = time.time() - self.start_time
        
        # 计算最近性能
        loss_stats = self.metrics_buffer.compute_statistics('total_loss', 100)
        reward_stats = self.metrics_buffer.compute_statistics('episode_reward', 100)
        
        summary = (
            f"Step {metrics.step} | "
            f"Loss: {metrics.total_loss:.4f} (avg: {loss_stats.get('mean', 0):.4f}) | "
            f"Reward: {metrics.episode_reward:.2f} (avg: {reward_stats.get('mean', 0):.2f}) | "
            f"Success: {metrics.success_rate:.3f} | "
            f"LR: {metrics.learning_rate:.6f} | "
            f"Time: {elapsed_time:.1f}s"
        )
        
        self.logger.info(summary)
    
    def _save_metrics(self):
        """保存指标到文件"""
        try:
            # 保存最近的指标
            recent_metrics = self.metrics_buffer.get_recent(1000)
            metrics_data = [asdict(m) for m in recent_metrics]
            
            metrics_file = self.log_dir / "metrics.json"
            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2)
            
            # 保存性能摘要
            performance_summary = self.performance_tracker.get_performance_summary()
            anomaly_summary = self.anomaly_detector.get_anomaly_summary()
            
            summary_data = {
                'performance': performance_summary,
                'anomalies': anomaly_summary,
                'timestamp': time.time()
            }
            
            summary_file = self.log_dir / "summary.json"
            with open(summary_file, 'w') as f:
                json.dump(summary_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to save metrics: {e}")
    
    def _create_plots(self):
        """创建监控图表"""
        try:
            recent_metrics = self.metrics_buffer.get_recent(500)
            if len(recent_metrics) < 10:
                return
            
            # 提取数据
            steps = [m.step for m in recent_metrics]
            losses = [m.total_loss for m in recent_metrics]
            rewards = [m.episode_reward for m in recent_metrics]
            grad_norms = [m.grad_norm for m in recent_metrics]
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(12, 8))
            
            # 损失曲线
            axes[0, 0].plot(steps, losses)
            axes[0, 0].set_title('Training Loss')
            axes[0, 0].set_xlabel('Step')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].grid(True)
            
            # 奖励曲线
            axes[0, 1].plot(steps, rewards)
            axes[0, 1].set_title('Episode Reward')
            axes[0, 1].set_xlabel('Step')
            axes[0, 1].set_ylabel('Reward')
            axes[0, 1].grid(True)
            
            # 梯度范数
            axes[1, 0].plot(steps, grad_norms)
            axes[1, 0].set_title('Gradient Norm')
            axes[1, 0].set_xlabel('Step')
            axes[1, 0].set_ylabel('Norm')
            axes[1, 0].grid(True)
            
            # 成功率
            success_rates = [m.success_rate for m in recent_metrics]
            axes[1, 1].plot(steps, success_rates)
            axes[1, 1].set_title('Success Rate')
            axes[1, 1].set_xlabel('Step')
            axes[1, 1].set_ylabel('Success Rate')
            axes[1, 1].grid(True)
            
            plt.tight_layout()
            
            # 保存图表
            plot_file = self.log_dir / f"training_plots_step_{steps[-1]}.png"
            plt.savefig(plot_file, dpi=150, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            self.logger.error(f"Failed to create plots: {e}")
            # 忽略matplotlib警告
            warnings.filterwarnings("ignore", category=UserWarning)
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        return {
            'total_steps': len(self.metrics_buffer.metrics),
            'elapsed_time': time.time() - self.start_time,
            'performance_summary': self.performance_tracker.get_performance_summary(),
            'anomaly_summary': self.anomaly_detector.get_anomaly_summary(),
            'recent_loss_stats': self.metrics_buffer.compute_statistics('total_loss', 100),
            'recent_reward_stats': self.metrics_buffer.compute_statistics('episode_reward', 100)
        }
    
    def close(self):
        """关闭监控器"""
        # 最终保存
        self._save_metrics()
        self._create_plots()
        
        # 记录最终摘要
        final_summary = self.get_monitoring_summary()
        self.logger.info(f"Training monitoring completed. Final summary: {final_summary}")
        
        # 关闭日志处理器
        for handler in self.logger.handlers:
            handler.close()
