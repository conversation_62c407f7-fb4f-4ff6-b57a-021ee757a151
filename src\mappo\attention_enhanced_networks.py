"""
注意力增强的策略价值网络
深度集成双层注意力机制的高级策略和价值网络实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, NamedTuple
from dataclasses import dataclass

from ..attention.task_allocation_attention import (
    MultiHeadTaskAllocationAttention,
    AttentionOutput as TaskAllocationOutput
)
from ..attention.collaboration_attention import (
    HierarchicalCollaborationAttention,
    CollaborationOutput
)


@dataclass
class AttentionEnhancedOutput:
    """注意力增强网络输出"""
    policy_logits: torch.Tensor
    value_estimate: torch.Tensor
    task_attention_weights: torch.Tensor
    collaboration_attention_weights: torch.Tensor
    attention_entropy: torch.Tensor
    feature_importance: torch.Tensor


class AttentionGatedFusion(nn.Module):
    """注意力门控融合模块"""
    
    def __init__(self, feature_dim: int, num_attention_types: int = 2):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_attention_types = num_attention_types
        
        # 门控网络
        self.gate_net = nn.Sequential(
            nn.Linear(feature_dim * num_attention_types, feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, num_attention_types),
            nn.Softmax(dim=-1)
        )
        
        # 特征变换网络
        self.transform_nets = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, feature_dim),
                nn.ReLU(),
                nn.LayerNorm(feature_dim)
            ) for _ in range(num_attention_types)
        ])
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.ReLU(),
            nn.LayerNorm(feature_dim)
        )
    
    def forward(self, attention_features: List[torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            attention_features: 不同类型的注意力特征列表
            
        Returns:
            fused_features: 融合后的特征
            gate_weights: 门控权重
        """
        # 拼接所有注意力特征
        concat_features = torch.cat(attention_features, dim=-1)
        
        # 计算门控权重
        gate_weights = self.gate_net(concat_features)
        
        # 变换每种注意力特征
        transformed_features = []
        for i, features in enumerate(attention_features):
            transformed = self.transform_nets[i](features)
            transformed_features.append(transformed)
        
        # 加权融合
        fused_features = torch.zeros_like(transformed_features[0])
        for i, features in enumerate(transformed_features):
            fused_features += gate_weights[:, i:i+1] * features
        
        # 输出投影
        output_features = self.output_projection(fused_features)
        
        return output_features, gate_weights


class AttentionEnhancedPolicyNetwork(nn.Module):
    """注意力增强的策略网络"""
    
    def __init__(self, 
                 feature_dim: int = 64,
                 hidden_dim: int = 256,
                 action_dim: int = 7,
                 num_heads: int = 8,
                 dropout: float = 0.1):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.action_dim = action_dim
        self.num_heads = num_heads
        
        # 双层注意力机制
        self.task_attention = MultiHeadTaskAllocationAttention(
            feature_dim=feature_dim,
            num_heads=num_heads,
            dropout=dropout
        )
        
        self.collaboration_attention = HierarchicalCollaborationAttention(
            feature_dim=feature_dim,
            pos_dim=32,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 注意力门控融合
        self.attention_fusion = AttentionGatedFusion(
            feature_dim=feature_dim,
            num_attention_types=2
        )
        
        # 策略头网络
        self.policy_head = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, action_dim)
        )
        
        # 注意力正则化层
        self.attention_regularizer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # 动作掩码处理
        self.action_mask_processor = nn.Sequential(
            nn.Linear(feature_dim + action_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, action_dim),
            nn.Sigmoid()
        )
    
    def forward(self,
                agv_features: torch.Tensor,
                task_features: torch.Tensor,
                agv_positions: torch.Tensor,
                action_mask: Optional[torch.Tensor] = None) -> AttentionEnhancedOutput:
        """
        前向传播

        Args:
            agv_features: AGV特征 [batch_size, num_agvs, feature_dim]
            task_features: 任务特征 [batch_size, num_tasks, feature_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            action_mask: 动作掩码 [batch_size, num_agvs, action_dim]

        Returns:
            output: 注意力增强输出
        """
        batch_size, num_agvs, _ = agv_features.shape
        _, num_tasks, _ = task_features.shape

        # 创建任务分配注意力所需的额外参数
        # 计算AGV到任务的距离矩阵
        task_positions = torch.randn(batch_size, num_tasks, 2)  # 简化：随机任务位置
        agv_pos_expanded = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        task_pos_expanded = task_positions.unsqueeze(1)  # [batch_size, 1, num_tasks, 2]
        distances = torch.norm(agv_pos_expanded - task_pos_expanded, dim=-1)  # [batch_size, num_agvs, num_tasks]

        # 创建AGV载重信息（简化）
        agv_loads = torch.rand(batch_size, num_agvs) * 0.8  # 随机载重比例

        # 创建任务重量和优先级（简化）
        task_weights = torch.rand(batch_size, num_tasks) * 10 + 1  # 1-11的任务重量
        task_priorities = torch.rand(batch_size, num_tasks)  # 0-1的优先级

        # 计算任务分配注意力
        task_attention_output = self.task_attention(
            agv_features, task_features, distances, agv_loads, task_weights, task_priorities
        )

        # 计算协作感知注意力
        collaboration_output = self.collaboration_attention(
            agv_features, agv_positions, task_attention_output.attended_features
        )
        
        # 注意力特征融合
        # 确保两个特征具有相同的形状
        task_features_reshaped = task_attention_output.attended_features.view(-1, self.feature_dim)
        collab_features_reshaped = collaboration_output.collaborated_features.view(-1, self.feature_dim)

        attention_features = [
            task_features_reshaped,
            collab_features_reshaped
        ]

        fused_features, gate_weights = self.attention_fusion(attention_features)

        # 重新调整形状以匹配原始批次和AGV维度
        fused_features = fused_features.view(batch_size, num_agvs, self.feature_dim)
        
        # 生成策略logits
        policy_logits = self.policy_head(fused_features)
        
        # 应用动作掩码
        if action_mask is not None:
            # 计算掩码权重
            mask_input = torch.cat([fused_features, policy_logits], dim=-1)
            mask_weights = self.action_mask_processor(mask_input)
            
            # 应用掩码
            masked_logits = policy_logits * mask_weights
            masked_logits = masked_logits + (action_mask - 1) * 1e9  # 将无效动作设为负无穷
            policy_logits = masked_logits
        
        # 计算注意力熵（用于正则化）
        task_entropy = -torch.sum(
            task_attention_output.attention_weights * torch.log(task_attention_output.attention_weights + 1e-8),
            dim=-1
        ).mean()
        
        collab_entropy = -torch.sum(
            collaboration_output.collaboration_weights * torch.log(collaboration_output.collaboration_weights + 1e-8),
            dim=-1
        ).mean()
        
        attention_entropy = task_entropy + collab_entropy
        
        # 计算特征重要性
        feature_importance = self.attention_regularizer(fused_features)
        
        return AttentionEnhancedOutput(
            policy_logits=policy_logits,
            value_estimate=None,  # 将在价值网络中计算
            task_attention_weights=task_attention_output.attention_weights,
            collaboration_attention_weights=collaboration_output.collaboration_weights,
            attention_entropy=attention_entropy,
            feature_importance=feature_importance
        )


class AttentionEnhancedValueNetwork(nn.Module):
    """注意力增强的价值网络"""
    
    def __init__(self,
                 feature_dim: int = 64,
                 hidden_dim: int = 256,
                 num_heads: int = 8,
                 dropout: float = 0.1):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        
        # 双层注意力机制（与策略网络共享）
        self.task_attention = MultiHeadTaskAllocationAttention(
            feature_dim=feature_dim,
            num_heads=num_heads,
            dropout=dropout
        )
        
        self.collaboration_attention = HierarchicalCollaborationAttention(
            feature_dim=feature_dim,
            pos_dim=32,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 注意力门控融合
        self.attention_fusion = AttentionGatedFusion(
            feature_dim=feature_dim,
            num_attention_types=2
        )
        
        # 全局价值估计网络
        self.global_value_net = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # 个体价值估计网络
        self.individual_value_net = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # 价值融合网络
        self.value_fusion = nn.Sequential(
            nn.Linear(2, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # 注意力感知的价值调整
        self.attention_value_adjustment = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1),
            nn.Tanh()
        )
    
    def forward(self,
                agv_features: torch.Tensor,
                task_features: torch.Tensor,
                agv_positions: torch.Tensor,
                global_state: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            agv_features: AGV特征 [batch_size, num_agvs, feature_dim]
            task_features: 任务特征 [batch_size, num_tasks, feature_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            global_state: 全局状态 [batch_size, global_dim]

        Returns:
            value_estimates: 价值估计 [batch_size, num_agvs]
        """
        batch_size, num_agvs, _ = agv_features.shape
        _, num_tasks, _ = task_features.shape

        # 创建任务分配注意力所需的额外参数（与策略网络相同）
        task_positions = torch.randn(batch_size, num_tasks, 2)
        agv_pos_expanded = agv_positions.unsqueeze(2)
        task_pos_expanded = task_positions.unsqueeze(1)
        distances = torch.norm(agv_pos_expanded - task_pos_expanded, dim=-1)

        agv_loads = torch.rand(batch_size, num_agvs) * 0.8
        task_weights = torch.rand(batch_size, num_tasks) * 10 + 1
        task_priorities = torch.rand(batch_size, num_tasks)

        # 计算任务分配注意力
        task_attention_output = self.task_attention(
            agv_features, task_features, distances, agv_loads, task_weights, task_priorities
        )

        # 计算协作感知注意力
        collaboration_output = self.collaboration_attention(
            agv_features, agv_positions, task_attention_output.attended_features
        )
        
        # 注意力特征融合
        # 确保两个特征具有相同的形状
        task_features_reshaped = task_attention_output.attended_features.view(-1, self.feature_dim)
        collab_features_reshaped = collaboration_output.collaborated_features.view(-1, self.feature_dim)

        attention_features = [
            task_features_reshaped,
            collab_features_reshaped
        ]

        fused_features, _ = self.attention_fusion(attention_features)

        # 重新调整形状以匹配原始批次和AGV维度
        fused_features = fused_features.view(batch_size, num_agvs, self.feature_dim)
        
        # 计算全局价值
        global_features = fused_features.mean(dim=1)  # 聚合所有AGV的特征
        global_value = self.global_value_net(global_features)
        
        # 计算个体价值
        individual_values = self.individual_value_net(fused_features)
        
        # 注意力感知的价值调整
        attention_adjustment = self.attention_value_adjustment(fused_features)
        adjusted_individual_values = individual_values + attention_adjustment
        
        # 融合全局和个体价值
        global_value_expanded = global_value.unsqueeze(1).expand(-1, num_agvs, -1)
        value_input = torch.cat([global_value_expanded, adjusted_individual_values], dim=-1)
        final_values = self.value_fusion(value_input).squeeze(-1)
        
        return final_values
