"""
仪表板集成模块
将训练系统与Web仪表板集成，提供实时数据传输和控制功能
"""

import asyncio
import json
import threading
import time
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import queue

from .web_dashboard import WebSocketManager


class DashboardIntegration:
    """仪表板集成类"""
    
    def __init__(self, websocket_manager: Optional[WebSocketManager] = None):
        """
        初始化仪表板集成
        
        Args:
            websocket_manager: WebSocket管理器实例
        """
        self.websocket_manager = websocket_manager or WebSocketManager()
        self.is_running = False
        self.data_queue = queue.Queue()
        self.update_thread = None
        
        # 训练状态
        self.training_status = "stopped"
        self.current_episode = 0
        self.total_episodes = 0
        self.start_time = None
        
        # 数据缓存
        self.metrics_cache = {
            "episode_rewards": [],
            "losses": {"policy_loss": [], "value_loss": []},
            "success_rates": [],
            "attention_weights": {}
        }
        
        # 回调函数
        self.callbacks = {
            "on_training_start": [],
            "on_training_stop": [],
            "on_training_pause": [],
            "on_episode_end": [],
            "on_metrics_update": []
        }
    
    def start(self):
        """启动集成服务"""
        if self.is_running:
            return
        
        self.is_running = True
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        print("✅ 仪表板集成服务已启动")
    
    def stop(self):
        """停止集成服务"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        print("🛑 仪表板集成服务已停止")
    
    def _update_loop(self):
        """数据更新循环"""
        while self.is_running:
            try:
                # 处理队列中的数据
                while not self.data_queue.empty():
                    try:
                        data = self.data_queue.get_nowait()
                        asyncio.run(self._send_data_update(data))
                    except queue.Empty:
                        break
                    except Exception as e:
                        print(f"❌ 处理数据更新失败: {e}")
                
                time.sleep(1)  # 1秒更新间隔
                
            except Exception as e:
                print(f"❌ 数据更新循环错误: {e}")
                time.sleep(5)
    
    async def _send_data_update(self, data: Dict[str, Any]):
        """发送数据更新"""
        try:
            if data.get("type") == "metrics":
                await self.websocket_manager.send_metrics_update(data["data"])
            elif data.get("type") == "attention":
                await self.websocket_manager.send_attention_update(data["data"])
        except Exception as e:
            print(f"❌ 发送数据更新失败: {e}")
    
    def register_callback(self, event: str, callback: Callable):
        """
        注册回调函数
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self.callbacks:
            self.callbacks[event].append(callback)
        else:
            print(f"⚠️ 未知事件类型: {event}")
    
    def trigger_callback(self, event: str, *args, **kwargs):
        """触发回调函数"""
        if event in self.callbacks:
            for callback in self.callbacks[event]:
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    print(f"❌ 回调函数执行失败: {e}")
    
    # 训练控制方法
    def start_training(self, total_episodes: int = 1000):
        """开始训练"""
        self.training_status = "training"
        self.current_episode = 0
        self.total_episodes = total_episodes
        self.start_time = time.time()
        
        # 触发回调
        self.trigger_callback("on_training_start", total_episodes)
        
        # 发送状态更新
        self._queue_metrics_update()
        print(f"🚀 训练已开始，目标回合数: {total_episodes}")
    
    def stop_training(self):
        """停止训练"""
        self.training_status = "stopped"
        
        # 触发回调
        self.trigger_callback("on_training_stop")
        
        # 发送状态更新
        self._queue_metrics_update()
        print("🛑 训练已停止")
    
    def pause_training(self):
        """暂停训练"""
        self.training_status = "paused"
        
        # 触发回调
        self.trigger_callback("on_training_pause")
        
        # 发送状态更新
        self._queue_metrics_update()
        print("⏸️ 训练已暂停")
    
    def resume_training(self):
        """恢复训练"""
        self.training_status = "training"
        
        # 发送状态更新
        self._queue_metrics_update()
        print("▶️ 训练已恢复")
    
    # 数据更新方法
    def update_episode(self, episode: int, reward: float, success: bool = False):
        """
        更新回合信息
        
        Args:
            episode: 回合数
            reward: 回合奖励
            success: 是否成功
        """
        self.current_episode = episode
        
        # 更新缓存
        self.metrics_cache["episode_rewards"].append(reward)
        self.metrics_cache["success_rates"].append(1.0 if success else 0.0)
        
        # 限制缓存大小
        max_cache_size = 1000
        if len(self.metrics_cache["episode_rewards"]) > max_cache_size:
            self.metrics_cache["episode_rewards"] = self.metrics_cache["episode_rewards"][-max_cache_size:]
            self.metrics_cache["success_rates"] = self.metrics_cache["success_rates"][-max_cache_size:]
        
        # 触发回调
        self.trigger_callback("on_episode_end", episode, reward, success)
        
        # 发送更新
        self._queue_metrics_update()
    
    def update_losses(self, policy_loss: float, value_loss: float):
        """
        更新损失信息
        
        Args:
            policy_loss: 策略损失
            value_loss: 价值损失
        """
        self.metrics_cache["losses"]["policy_loss"].append(policy_loss)
        self.metrics_cache["losses"]["value_loss"].append(value_loss)
        
        # 限制缓存大小
        max_cache_size = 1000
        if len(self.metrics_cache["losses"]["policy_loss"]) > max_cache_size:
            self.metrics_cache["losses"]["policy_loss"] = self.metrics_cache["losses"]["policy_loss"][-max_cache_size:]
            self.metrics_cache["losses"]["value_loss"] = self.metrics_cache["losses"]["value_loss"][-max_cache_size:]
        
        # 发送更新
        self._queue_metrics_update()
    
    def update_attention_weights(self, task_attention: Any = None, collaboration_attention: Any = None):
        """
        更新注意力权重
        
        Args:
            task_attention: 任务分配注意力权重
            collaboration_attention: 协作感知注意力权重
        """
        attention_data = {}
        
        if task_attention is not None:
            # 转换为可序列化的格式
            if hasattr(task_attention, 'detach'):
                task_attention = task_attention.detach().cpu().numpy()
            attention_data["task_attention_weights"] = task_attention.tolist()
        
        if collaboration_attention is not None:
            # 转换为可序列化的格式
            if hasattr(collaboration_attention, 'detach'):
                collaboration_attention = collaboration_attention.detach().cpu().numpy()
            attention_data["collaboration_attention_weights"] = collaboration_attention.tolist()
        
        if attention_data:
            self.data_queue.put({
                "type": "attention",
                "data": attention_data
            })
    
    def _queue_metrics_update(self):
        """将指标更新加入队列"""
        metrics = self._get_current_metrics()
        self.data_queue.put({
            "type": "metrics",
            "data": metrics
        })
        
        # 触发回调
        self.trigger_callback("on_metrics_update", metrics)
    
    def _get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        
        # 计算平均奖励
        recent_rewards = self.metrics_cache["episode_rewards"][-100:]  # 最近100个回合
        average_reward = sum(recent_rewards) / len(recent_rewards) if recent_rewards else 0.0
        
        # 计算成功率
        recent_success = self.metrics_cache["success_rates"][-100:]  # 最近100个回合
        success_rate = sum(recent_success) / len(recent_success) if recent_success else 0.0
        
        return {
            "current_episode": self.current_episode,
            "total_episodes": self.total_episodes,
            "average_reward": average_reward,
            "success_rate": success_rate,
            "elapsed_time": elapsed_time,
            "training_status": self.training_status,
            "episode_rewards": self.metrics_cache["episode_rewards"],
            "losses": self.metrics_cache["losses"]
        }
    
    def get_training_status(self) -> Dict[str, Any]:
        """获取训练状态"""
        return {
            "status": self.training_status,
            "current_episode": self.current_episode,
            "total_episodes": self.total_episodes,
            "elapsed_time": time.time() - self.start_time if self.start_time else 0
        }
    
    def reset_metrics(self):
        """重置指标"""
        self.metrics_cache = {
            "episode_rewards": [],
            "losses": {"policy_loss": [], "value_loss": []},
            "success_rates": [],
            "attention_weights": {}
        }
        self.current_episode = 0
        self.start_time = time.time() if self.training_status == "training" else None
        
        # 发送更新
        self._queue_metrics_update()
        print("🔄 指标已重置")


# 全局集成实例
_dashboard_integration = None


def get_dashboard_integration() -> DashboardIntegration:
    """获取全局仪表板集成实例"""
    global _dashboard_integration
    if _dashboard_integration is None:
        _dashboard_integration = DashboardIntegration()
        _dashboard_integration.start()
    return _dashboard_integration


def init_dashboard_integration(websocket_manager: Optional[WebSocketManager] = None) -> DashboardIntegration:
    """初始化仪表板集成"""
    global _dashboard_integration
    _dashboard_integration = DashboardIntegration(websocket_manager)
    _dashboard_integration.start()
    return _dashboard_integration
