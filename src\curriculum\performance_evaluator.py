"""
课程学习性能评估系统
评估学习进度、技能掌握程度和阶段转换准备度
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from collections import defaultdict
import logging


@dataclass
class SkillAssessment:
    """技能评估结果"""
    skill_name: str
    mastery_level: float  # 0.0-1.0
    consistency: float  # 0.0-1.0
    improvement_rate: float  # -1.0 to 1.0
    confidence: float  # 0.0-1.0


@dataclass
class StageReadiness:
    """阶段准备度评估"""
    current_stage: str
    next_stage: str
    readiness_score: float  # 0.0-1.0
    skill_assessments: List[SkillAssessment]
    recommendation: str  # 'advance', 'continue', 'review'
    confidence: float  # 0.0-1.0


class CurriculumPerformanceEvaluator:
    """
    课程学习性能评估器
    评估学习进度和技能掌握情况
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化性能评估器
        
        Args:
            config: 评估配置
        """
        self.config = config
        self.logger = logging.getLogger('PerformanceEvaluator')
        
        # 评估参数
        self.evaluation_window = config.get('evaluation_window', 50)
        self.mastery_threshold = config.get('mastery_threshold', 0.8)
        self.consistency_threshold = config.get('consistency_threshold', 0.7)
        self.advancement_threshold = config.get('advancement_threshold', 0.75)
        
        # 技能定义
        self.skills = self._define_skills()
        
        # 性能历史
        self.performance_data = defaultdict(list)
        self.skill_history = defaultdict(list)
        self.evaluation_history = []
        
        self.logger.info("课程学习性能评估器初始化完成")
    
    def _define_skills(self) -> Dict[str, Dict[str, Any]]:
        """定义需要评估的技能"""
        skills = {
            # 基础技能
            'navigation': {
                'name': '导航能力',
                'description': 'AGV基础移动和路径规划能力',
                'metrics': ['path_efficiency', 'collision_avoidance', 'goal_reaching'],
                'weight': 1.0
            },
            
            'task_execution': {
                'name': '任务执行',
                'description': '单个任务的完成能力',
                'metrics': ['task_completion_rate', 'execution_time', 'accuracy'],
                'weight': 1.0
            },
            
            # 协作技能
            'coordination': {
                'name': '协调能力',
                'description': 'AGV间的基础协调能力',
                'metrics': ['conflict_resolution', 'resource_sharing', 'communication'],
                'weight': 1.2
            },
            
            'collaboration': {
                'name': '协作能力',
                'description': '复杂多AGV协作任务能力',
                'metrics': ['joint_task_completion', 'load_balancing', 'efficiency'],
                'weight': 1.5
            },
            
            # 高级技能
            'adaptation': {
                'name': '适应能力',
                'description': '环境变化和异常情况的适应能力',
                'metrics': ['robustness', 'recovery_time', 'flexibility'],
                'weight': 1.3
            },
            
            'optimization': {
                'name': '优化能力',
                'description': '系统整体性能优化能力',
                'metrics': ['throughput', 'resource_utilization', 'energy_efficiency'],
                'weight': 1.4
            }
        }
        
        return skills
    
    def update_performance_data(self, episode_data: Dict[str, Any]):
        """
        更新性能数据
        
        Args:
            episode_data: 回合性能数据
        """
        # 提取基础指标
        episode_reward = episode_data.get('total_reward', 0)
        episode_length = episode_data.get('episode_length', 0)
        success = episode_data.get('success', False)
        
        # 提取AGV特定指标
        agv_metrics = episode_data.get('agv_metrics', {})
        task_metrics = episode_data.get('task_metrics', {})
        collaboration_metrics = episode_data.get('collaboration_metrics', {})
        
        # 更新性能历史
        self.performance_data['rewards'].append(episode_reward)
        self.performance_data['lengths'].append(episode_length)
        self.performance_data['successes'].append(1.0 if success else 0.0)
        
        # 计算技能指标
        skill_scores = self._calculate_skill_scores(
            agv_metrics, task_metrics, collaboration_metrics
        )
        
        # 更新技能历史
        for skill_name, score in skill_scores.items():
            self.skill_history[skill_name].append(score)
    
    def _calculate_skill_scores(self, 
                               agv_metrics: Dict[str, Any],
                               task_metrics: Dict[str, Any], 
                               collaboration_metrics: Dict[str, Any]) -> Dict[str, float]:
        """
        计算技能得分
        
        Args:
            agv_metrics: AGV指标
            task_metrics: 任务指标
            collaboration_metrics: 协作指标
            
        Returns:
            skill_scores: 技能得分字典
        """
        skill_scores = {}
        
        # 导航能力
        path_efficiency = agv_metrics.get('path_efficiency', 0.5)
        collision_rate = agv_metrics.get('collision_rate', 0.5)
        goal_reaching_rate = agv_metrics.get('goal_reaching_rate', 0.5)
        
        navigation_score = (path_efficiency + (1 - collision_rate) + goal_reaching_rate) / 3
        skill_scores['navigation'] = navigation_score
        
        # 任务执行能力
        completion_rate = task_metrics.get('completion_rate', 0.5)
        avg_execution_time = task_metrics.get('avg_execution_time', 1.0)
        execution_accuracy = task_metrics.get('execution_accuracy', 0.5)
        
        # 执行时间归一化（越短越好）
        time_score = max(0, 1 - (avg_execution_time - 0.5) / 0.5)
        task_execution_score = (completion_rate + time_score + execution_accuracy) / 3
        skill_scores['task_execution'] = task_execution_score
        
        # 协调能力
        conflict_resolution = collaboration_metrics.get('conflict_resolution_rate', 0.5)
        resource_sharing = collaboration_metrics.get('resource_sharing_efficiency', 0.5)
        communication_quality = collaboration_metrics.get('communication_quality', 0.5)
        
        coordination_score = (conflict_resolution + resource_sharing + communication_quality) / 3
        skill_scores['coordination'] = coordination_score
        
        # 协作能力
        joint_completion = collaboration_metrics.get('joint_task_completion_rate', 0.5)
        load_balance = collaboration_metrics.get('load_balance_score', 0.5)
        collab_efficiency = collaboration_metrics.get('collaboration_efficiency', 0.5)
        
        collaboration_score = (joint_completion + load_balance + collab_efficiency) / 3
        skill_scores['collaboration'] = collaboration_score
        
        # 适应能力
        robustness = agv_metrics.get('robustness_score', 0.5)
        recovery_time = agv_metrics.get('recovery_time_score', 0.5)
        flexibility = agv_metrics.get('flexibility_score', 0.5)
        
        adaptation_score = (robustness + recovery_time + flexibility) / 3
        skill_scores['adaptation'] = adaptation_score
        
        # 优化能力
        throughput = task_metrics.get('throughput_score', 0.5)
        resource_util = agv_metrics.get('resource_utilization', 0.5)
        energy_efficiency = agv_metrics.get('energy_efficiency', 0.5)
        
        optimization_score = (throughput + resource_util + energy_efficiency) / 3
        skill_scores['optimization'] = optimization_score
        
        return skill_scores
    
    def assess_skills(self) -> List[SkillAssessment]:
        """
        评估所有技能
        
        Returns:
            assessments: 技能评估结果列表
        """
        assessments = []
        
        for skill_name, skill_config in self.skills.items():
            if skill_name not in self.skill_history or len(self.skill_history[skill_name]) < 5:
                # 数据不足，给出默认评估
                assessment = SkillAssessment(
                    skill_name=skill_name,
                    mastery_level=0.3,
                    consistency=0.3,
                    improvement_rate=0.0,
                    confidence=0.1
                )
            else:
                assessment = self._assess_single_skill(skill_name)
            
            assessments.append(assessment)
        
        return assessments
    
    def _assess_single_skill(self, skill_name: str) -> SkillAssessment:
        """
        评估单个技能
        
        Args:
            skill_name: 技能名称
            
        Returns:
            assessment: 技能评估结果
        """
        skill_scores = self.skill_history[skill_name]
        recent_scores = skill_scores[-self.evaluation_window:] if len(skill_scores) >= self.evaluation_window else skill_scores
        
        # 掌握程度（最近表现的平均值）
        mastery_level = np.mean(recent_scores)
        
        # 一致性（1 - 标准差）
        consistency = max(0, 1 - np.std(recent_scores))
        
        # 改进率（线性回归斜率）
        improvement_rate = self._calculate_improvement_rate(recent_scores)
        
        # 置信度（基于数据量和一致性）
        data_confidence = min(1.0, len(recent_scores) / self.evaluation_window)
        consistency_confidence = consistency
        confidence = (data_confidence + consistency_confidence) / 2
        
        return SkillAssessment(
            skill_name=skill_name,
            mastery_level=mastery_level,
            consistency=consistency,
            improvement_rate=improvement_rate,
            confidence=confidence
        )
    
    def _calculate_improvement_rate(self, scores: List[float]) -> float:
        """计算改进率"""
        if len(scores) < 3:
            return 0.0
        
        # 使用线性回归计算趋势
        x = np.arange(len(scores))
        y = np.array(scores)
        
        # 计算斜率
        slope = np.polyfit(x, y, 1)[0]
        
        # 归一化到 [-1, 1]
        return np.clip(slope * len(scores), -1.0, 1.0)
    
    def evaluate_stage_readiness(self, current_stage: str, next_stage: str) -> StageReadiness:
        """
        评估阶段转换准备度
        
        Args:
            current_stage: 当前阶段
            next_stage: 下一阶段
            
        Returns:
            readiness: 阶段准备度评估
        """
        # 评估所有技能
        skill_assessments = self.assess_skills()
        
        # 根据阶段确定关键技能
        key_skills = self._get_stage_key_skills(current_stage, next_stage)
        
        # 计算准备度得分
        readiness_score = self._calculate_readiness_score(skill_assessments, key_skills)
        
        # 生成建议
        recommendation = self._generate_recommendation(readiness_score, skill_assessments)
        
        # 计算整体置信度
        confidence = np.mean([assessment.confidence for assessment in skill_assessments])
        
        return StageReadiness(
            current_stage=current_stage,
            next_stage=next_stage,
            readiness_score=readiness_score,
            skill_assessments=skill_assessments,
            recommendation=recommendation,
            confidence=confidence
        )
    
    def _get_stage_key_skills(self, current_stage: str, next_stage: str) -> Dict[str, float]:
        """获取阶段关键技能及其权重"""
        stage_skills = {
            'stage1': {
                'navigation': 1.0,
                'task_execution': 1.0,
                'coordination': 0.3
            },
            'stage2': {
                'navigation': 0.8,
                'task_execution': 1.0,
                'coordination': 1.0,
                'collaboration': 0.7
            },
            'stage3': {
                'coordination': 1.0,
                'collaboration': 1.0,
                'adaptation': 1.0,
                'optimization': 1.0
            }
        }
        
        return stage_skills.get(next_stage, {})
    
    def _calculate_readiness_score(self, 
                                 assessments: List[SkillAssessment], 
                                 key_skills: Dict[str, float]) -> float:
        """计算准备度得分"""
        if not key_skills:
            return 0.5
        
        total_score = 0
        total_weight = 0
        
        for assessment in assessments:
            if assessment.skill_name in key_skills:
                weight = key_skills[assessment.skill_name]
                # 综合掌握程度和一致性
                skill_score = (assessment.mastery_level + assessment.consistency) / 2
                total_score += skill_score * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.5
    
    def _generate_recommendation(self, 
                               readiness_score: float, 
                               assessments: List[SkillAssessment]) -> str:
        """生成阶段转换建议"""
        if readiness_score >= self.advancement_threshold:
            return 'advance'
        elif readiness_score >= self.mastery_threshold * 0.7:
            return 'continue'
        else:
            # 找出最薄弱的技能
            weak_skills = [a for a in assessments if a.mastery_level < 0.6]
            if weak_skills:
                return f'review'
            else:
                return 'continue'
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        if not self.performance_data['rewards']:
            return {'status': 'insufficient_data'}
        
        recent_rewards = self.performance_data['rewards'][-20:]
        recent_successes = self.performance_data['successes'][-20:]
        
        return {
            'recent_performance': {
                'mean_reward': np.mean(recent_rewards),
                'success_rate': np.mean(recent_successes),
                'improvement_trend': self._calculate_improvement_rate(recent_rewards)
            },
            'skill_summary': {
                skill_name: {
                    'current_level': np.mean(scores[-10:]) if len(scores) >= 10 else np.mean(scores),
                    'trend': self._calculate_improvement_rate(scores[-20:]) if len(scores) >= 20 else 0
                }
                for skill_name, scores in self.skill_history.items()
                if scores
            },
            'data_points': len(self.performance_data['rewards']),
            'evaluation_count': len(self.evaluation_history)
        }
