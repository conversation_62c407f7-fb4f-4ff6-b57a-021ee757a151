"""
多维度奖励函数设计
实现多AGV协同调度的综合奖励机制
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from .agv_entity import AGVEntity, AGVStatus
from .task_manager import Task, TaskStatus
from .action_space import ActionType, MoveAction, TaskAction, CoordinationAction


class RewardComponent(Enum):
    """奖励组件枚举"""
    TASK_COMPLETION = "task_completion"
    EFFICIENCY = "efficiency"
    COLLABORATION = "collaboration"
    SAFETY = "safety"
    ENERGY = "energy"
    EXPLORATION = "exploration"


@dataclass
class RewardWeights:
    """奖励权重配置"""
    task_completion: float = 10.0      # 任务完成奖励权重
    efficiency: float = 5.0            # 效率奖励权重
    collaboration: float = 3.0         # 协作奖励权重
    safety: float = 2.0               # 安全奖励权重
    energy: float = 1.0               # 能耗奖励权重
    exploration: float = 0.5          # 探索奖励权重


@dataclass
class RewardInfo:
    """奖励信息"""
    total_reward: float
    component_rewards: Dict[str, float]
    bonus_rewards: Dict[str, float]
    penalty_rewards: Dict[str, float]
    normalized_reward: float


class MultiDimensionalRewardFunction:
    """
    多维度奖励函数
    实现综合的奖励机制，包括任务完成、效率、协作、安全等多个维度
    """
    
    def __init__(self, weights: Optional[RewardWeights] = None):
        """
        初始化多维度奖励函数
        
        Args:
            weights: 奖励权重配置
        """
        self.weights = weights or RewardWeights()
        self.reward_history = []
        self.step_count = 0
        
        # 奖励范围配置
        self.reward_ranges = {
            RewardComponent.TASK_COMPLETION: (-50.0, 100.0),
            RewardComponent.EFFICIENCY: (-10.0, 20.0),
            RewardComponent.COLLABORATION: (-5.0, 15.0),
            RewardComponent.SAFETY: (-20.0, 5.0),
            RewardComponent.ENERGY: (-5.0, 2.0),
            RewardComponent.EXPLORATION: (-1.0, 3.0)
        }
        
    def compute_reward(self,
                      agv: AGVEntity,
                      action: int,
                      prev_state: Dict,
                      current_state: Dict,
                      other_agvs: List[AGVEntity],
                      completed_tasks: List[Task],
                      failed_tasks: List[Task]) -> RewardInfo:
        """
        计算综合奖励
        
        Args:
            agv: 当前AGV
            action: 执行的动作
            prev_state: 前一状态
            current_state: 当前状态
            other_agvs: 其他AGV列表
            completed_tasks: 完成的任务列表
            failed_tasks: 失败的任务列表
            
        Returns:
            reward_info: 奖励信息
        """
        self.step_count += 1
        
        # 计算各维度奖励
        component_rewards = {}
        
        # 1. 任务完成奖励
        component_rewards[RewardComponent.TASK_COMPLETION.value] = self._compute_task_completion_reward(
            agv, action, completed_tasks, failed_tasks
        )
        
        # 2. 效率奖励
        component_rewards[RewardComponent.EFFICIENCY.value] = self._compute_efficiency_reward(
            agv, action, prev_state, current_state
        )
        
        # 3. 协作奖励
        component_rewards[RewardComponent.COLLABORATION.value] = self._compute_collaboration_reward(
            agv, action, other_agvs, current_state
        )
        
        # 4. 安全奖励
        component_rewards[RewardComponent.SAFETY.value] = self._compute_safety_reward(
            agv, action, other_agvs, current_state
        )
        
        # 5. 能耗奖励
        component_rewards[RewardComponent.ENERGY.value] = self._compute_energy_reward(
            agv, action
        )
        
        # 6. 探索奖励
        component_rewards[RewardComponent.EXPLORATION.value] = self._compute_exploration_reward(
            agv, action, current_state
        )
        
        # 计算奖励和惩罚
        bonus_rewards = self._compute_bonus_rewards(agv, action, current_state, other_agvs)
        penalty_rewards = self._compute_penalty_rewards(agv, action, current_state, other_agvs)
        
        # 加权求和
        total_reward = self._compute_weighted_reward(component_rewards, bonus_rewards, penalty_rewards)
        
        # 归一化奖励
        normalized_reward = self._normalize_reward(total_reward)
        
        # 创建奖励信息
        reward_info = RewardInfo(
            total_reward=total_reward,
            component_rewards=component_rewards,
            bonus_rewards=bonus_rewards,
            penalty_rewards=penalty_rewards,
            normalized_reward=normalized_reward
        )
        
        # 记录奖励历史
        self.reward_history.append(reward_info)
        
        return reward_info
    
    def _compute_task_completion_reward(self,
                                      agv: AGVEntity,
                                      action: int,
                                      completed_tasks: List[Task],
                                      failed_tasks: List[Task]) -> float:
        """计算任务完成奖励"""
        reward = 0.0
        
        # 任务完成奖励
        for task in completed_tasks:
            if task.assigned_agv == agv.agv_id:
                # 基础完成奖励
                reward += 50.0
                
                # 时间奖励（越快完成奖励越高）
                if hasattr(task, 'completion_time') and hasattr(task, 'creation_time'):
                    time_efficiency = max(0, 20 - (task.completion_time - task.creation_time))
                    reward += time_efficiency
                
                # 优先级奖励
                if hasattr(task, 'priority'):
                    reward += task.priority * 5.0
        
        # 任务失败惩罚
        for task in failed_tasks:
            if task.assigned_agv == agv.agv_id:
                reward -= 30.0
        
        # 任务进度奖励
        if action == TaskAction.PICKUP.value:
            reward += 10.0  # 拾取奖励
        elif action == TaskAction.DELIVER.value:
            reward += 20.0  # 交付奖励
        
        return reward
    
    def _compute_efficiency_reward(self,
                                 agv: AGVEntity,
                                 action: int,
                                 prev_state: Dict,
                                 current_state: Dict) -> float:
        """计算效率奖励"""
        reward = 0.0
        
        # 移动效率奖励
        if action in [MoveAction.UP.value, MoveAction.DOWN.value,
                     MoveAction.LEFT.value, MoveAction.RIGHT.value]:
            # 朝向目标移动的奖励
            if agv.current_target is not None:
                prev_distance = self._get_distance_to_target(agv, prev_state)
                current_distance = self._get_distance_to_target(agv, current_state)
                
                if current_distance < prev_distance:
                    reward += 2.0  # 接近目标奖励
                elif current_distance > prev_distance:
                    reward -= 1.0  # 远离目标惩罚
        
        # 停留惩罚（除非有合理原因）
        if action == MoveAction.STAY.value:
            if agv.status == AGVStatus.IDLE:
                reward -= 0.5  # 空闲停留轻微惩罚
            elif agv.status in [AGVStatus.LOADING, AGVStatus.UNLOADING]:
                reward += 1.0  # 执行任务时停留是合理的
        
        # 载重效率奖励
        if agv.current_load > 0:
            load_ratio = agv.current_load / agv.capacity
            reward += load_ratio * 2.0  # 鼓励充分利用载重
        
        return reward
    
    def _compute_collaboration_reward(self,
                                    agv: AGVEntity,
                                    action: int,
                                    other_agvs: List[AGVEntity],
                                    current_state: Dict) -> float:
        """计算协作奖励"""
        reward = 0.0
        
        # 协调动作奖励
        if action == CoordinationAction.REQUEST_HELP.value:
            reward += 2.0  # 请求帮助奖励
        elif action == CoordinationAction.OFFER_HELP.value:
            reward += 3.0  # 提供帮助奖励
        elif action == CoordinationAction.YIELD_PATH.value:
            reward += 1.5  # 让路奖励
        
        # 协作效果奖励
        nearby_agvs = self._get_nearby_agvs(agv, other_agvs, radius=3)
        if len(nearby_agvs) > 0:
            # 计算协作效率
            collaboration_score = self._calculate_collaboration_score(agv, nearby_agvs)
            reward += collaboration_score * 2.0

        # 避免聚集惩罚
        very_close_agvs = self._get_nearby_agvs(agv, other_agvs, radius=1)
        if len(very_close_agvs) > 2:
            reward -= len(very_close_agvs) * 1.0  # 过度聚集惩罚
        
        return reward
    
    def _compute_safety_reward(self,
                             agv: AGVEntity,
                             action: int,
                             other_agvs: List[AGVEntity],
                             current_state: Dict) -> float:
        """计算安全奖励"""
        reward = 0.0
        
        # 碰撞检测
        for other_agv in other_agvs:
            if other_agv.agv_id != agv.agv_id and other_agv.position == agv.position:
                reward -= 50.0  # 严重碰撞惩罚
                break
        
        # 近距离安全检查
        min_distance = float('inf')
        for other_agv in other_agvs:
            if other_agv.agv_id != agv.agv_id:
                distance = self._calculate_manhattan_distance(agv.position, other_agv.position)
                min_distance = min(min_distance, distance)
        
        if min_distance == 1:
            reward -= 5.0   # 过近惩罚
        elif min_distance == 2:
            reward -= 1.0   # 较近惩罚
        elif min_distance >= 3:
            reward += 0.5   # 安全距离奖励
        
        # 边界安全
        x, y = agv.position
        if x == 0 or x == 25 or y == 0 or y == 9:
            reward -= 0.5  # 边界位置轻微惩罚
        
        return reward
    
    def _compute_energy_reward(self, agv: AGVEntity, action: int) -> float:
        """计算能耗奖励"""
        reward = 0.0
        
        # 移动能耗
        if action in [MoveAction.UP.value, MoveAction.DOWN.value, 
                     MoveAction.LEFT.value, MoveAction.RIGHT.value]:
            base_cost = -0.5
            # 载重影响能耗
            if agv.current_load > 0:
                load_factor = 1 + (agv.current_load / agv.capacity) * 0.5
                reward += base_cost * load_factor
            else:
                reward += base_cost
        
        # 停留能耗
        elif action == MoveAction.STAY.value:
            reward += -0.1  # 停留也有少量能耗
        
        # 任务动作能耗
        elif action in [TaskAction.PICKUP.value, TaskAction.DELIVER.value]:
            reward += -0.3  # 任务动作能耗
        
        return reward
    
    def _compute_exploration_reward(self,
                                  agv: AGVEntity,
                                  action: int,
                                  current_state: Dict) -> float:
        """计算探索奖励"""
        reward = 0.0
        
        # 鼓励探索新区域（简化实现）
        if action in [MoveAction.UP.value, MoveAction.DOWN.value, 
                     MoveAction.LEFT.value, MoveAction.RIGHT.value]:
            # 基于位置的探索奖励
            x, y = agv.position
            exploration_score = self._calculate_exploration_score(x, y)
            reward += exploration_score
        
        return reward
    
    def _compute_bonus_rewards(self,
                             agv: AGVEntity,
                             action: int,
                             current_state: Dict,
                             other_agvs: List[AGVEntity]) -> Dict[str, float]:
        """计算奖励奖金"""
        bonuses = {}
        
        # 连续成功奖金
        if self._check_consecutive_success(agv):
            bonuses['consecutive_success'] = 5.0
        
        # 团队协作奖金
        team_score = self._calculate_team_performance(other_agvs)
        if team_score > 0.8:
            bonuses['team_performance'] = 3.0
        
        # 效率奖金
        if agv.current_load == agv.capacity:
            bonuses['full_capacity'] = 2.0
        
        return bonuses
    
    def _compute_penalty_rewards(self,
                               agv: AGVEntity,
                               action: int,
                               current_state: Dict,
                               other_agvs: List[AGVEntity]) -> Dict[str, float]:
        """计算惩罚"""
        penalties = {}
        
        # 超时惩罚
        if self._check_timeout(agv):
            penalties['timeout'] = -10.0
        
        # 无效动作惩罚
        if self._check_invalid_action(agv, action):
            penalties['invalid_action'] = -5.0
        
        # 资源浪费惩罚
        if self._check_resource_waste(agv):
            penalties['resource_waste'] = -3.0
        
        return penalties
    
    def _compute_weighted_reward(self,
                               component_rewards: Dict[str, float],
                               bonus_rewards: Dict[str, float],
                               penalty_rewards: Dict[str, float]) -> float:
        """计算加权总奖励"""
        total = 0.0
        
        # 组件奖励加权
        total += component_rewards.get(RewardComponent.TASK_COMPLETION.value, 0) * self.weights.task_completion
        total += component_rewards.get(RewardComponent.EFFICIENCY.value, 0) * self.weights.efficiency
        total += component_rewards.get(RewardComponent.COLLABORATION.value, 0) * self.weights.collaboration
        total += component_rewards.get(RewardComponent.SAFETY.value, 0) * self.weights.safety
        total += component_rewards.get(RewardComponent.ENERGY.value, 0) * self.weights.energy
        total += component_rewards.get(RewardComponent.EXPLORATION.value, 0) * self.weights.exploration
        
        # 奖金和惩罚
        total += sum(bonus_rewards.values())
        total += sum(penalty_rewards.values())
        
        return total
    
    def _normalize_reward(self, reward: float) -> float:
        """归一化奖励到[-1, 1]范围"""
        # 使用tanh函数进行软归一化
        return np.tanh(reward / 100.0)
    
    # 辅助方法
    def _get_distance_to_target(self, agv: AGVEntity, state: Dict) -> float:
        """获取到目标的距离"""
        # 简化实现，返回随机距离
        return np.random.uniform(1, 10)
    
    def _get_nearby_agvs(self, agv: AGVEntity, other_agvs: List[AGVEntity], radius: int) -> List[AGVEntity]:
        """获取附近的AGV"""
        nearby = []
        for other_agv in other_agvs:
            if other_agv.agv_id != agv.agv_id:
                distance = self._calculate_manhattan_distance(agv.position, other_agv.position)
                if distance <= radius:
                    nearby.append(other_agv)
        return nearby
    
    def _calculate_manhattan_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> int:
        """计算曼哈顿距离"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
    
    def _calculate_collaboration_score(self, agv: AGVEntity, nearby_agvs: List[AGVEntity]) -> float:
        """计算协作得分"""
        # 简化的协作得分计算
        return min(len(nearby_agvs) * 0.2, 1.0)
    
    def _calculate_exploration_score(self, x: int, y: int) -> float:
        """计算探索得分"""
        # 简化的探索得分，鼓励访问地图中心区域
        center_x, center_y = 13, 5
        distance_to_center = abs(x - center_x) + abs(y - center_y)
        return max(0, 1.0 - distance_to_center / 20.0)
    
    def _check_consecutive_success(self, agv: AGVEntity) -> bool:
        """检查连续成功"""
        # 简化实现
        return False
    
    def _calculate_team_performance(self, other_agvs: List[AGVEntity]) -> float:
        """计算团队表现"""
        # 简化实现
        return 0.5
    
    def _check_timeout(self, agv: AGVEntity) -> bool:
        """检查超时"""
        # 简化实现
        return False
    
    def _check_invalid_action(self, agv: AGVEntity, action: int) -> bool:
        """检查无效动作"""
        # 简化实现
        return False
    
    def _check_resource_waste(self, agv: AGVEntity) -> bool:
        """检查资源浪费"""
        # 简化实现
        return False
    
    def get_reward_statistics(self) -> Dict[str, float]:
        """获取奖励统计信息"""
        if not self.reward_history:
            return {}
        
        total_rewards = [info.total_reward for info in self.reward_history]
        
        return {
            'mean_reward': np.mean(total_rewards),
            'std_reward': np.std(total_rewards),
            'min_reward': np.min(total_rewards),
            'max_reward': np.max(total_rewards),
            'total_steps': len(self.reward_history)
        }
