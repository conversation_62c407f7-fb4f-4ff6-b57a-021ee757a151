# AGV训练监控Web仪表板使用指南

## 概述

AGV训练监控Web仪表板是一个基于FastAPI的现代化实时监控系统，为基于双层注意力机制的MAPPO多AGV协同调度系统提供全面的训练监控、可视化和控制功能。

## 功能特性

### 🎯 核心功能

1. **实时训练监控**
   - 训练进度跟踪
   - 关键指标实时更新
   - 训练状态监控

2. **交互式可视化**
   - 奖励曲线图表
   - 损失函数趋势
   - 注意力权重热图
   - AGV协作网络图

3. **训练控制**
   - 开始/暂停/停止训练
   - 实时参数调整
   - 模型保存控制

4. **数据分析**
   - 性能指标统计
   - 训练效果分析
   - 数据导出功能

### 🔧 技术特性

- **WebSocket实时通信**: 毫秒级数据更新
- **响应式设计**: 支持多设备访问
- **模块化架构**: 易于扩展和定制
- **高性能渲染**: 基于Chart.js和Plotly.js

## 快速开始

### 1. 环境准备

确保已激活biye_RL环境并安装了所需依赖：

```bash
# 激活环境
source /d/Test-zc/biye_RL/Scripts/activate

# 安装依赖（如果尚未安装）
pip install fastapi uvicorn websockets jinja2 python-multipart aiofiles plotly dash
```

### 2. 启动仪表板

```bash
# 基本启动
python run_dashboard.py

# 自定义配置启动
python run_dashboard.py --host 0.0.0.0 --port 8080 --debug

# 参数说明
# --host: 服务器主机地址（默认: localhost）
# --port: 服务器端口（默认: 8000）
# --debug: 启用调试模式
```

### 3. 访问仪表板

启动成功后，在浏览器中访问：
- 本地访问: http://localhost:8000
- 网络访问: http://your-ip:8000

## 界面介绍

### 📊 主要区域

#### 1. 控制面板
位于页面顶部，提供训练控制功能：
- **开始训练**: 启动新的训练会话
- **暂停训练**: 暂停当前训练
- **停止训练**: 完全停止训练
- **导出数据**: 导出训练数据和指标
- **重置指标**: 清空当前指标数据

#### 2. 关键指标卡片
显示核心训练指标：
- **当前回合**: 当前训练回合数
- **平均奖励**: 最近100回合的平均奖励
- **成功率**: 任务完成成功率
- **训练时间**: 累计训练时间

#### 3. 图表区域
实时更新的训练图表：

**奖励曲线图**:
- 显示训练过程中的奖励变化
- 自动缩放和平滑处理
- 支持最近100个数据点

**损失曲线图**:
- 策略损失和价值损失的变化趋势
- 双线图显示，便于对比分析

#### 4. 注意力可视化
专门的注意力机制可视化：

**任务分配注意力热图**:
- 显示AGV对不同任务的注意力权重
- 颜色深度表示注意力强度
- 实时更新，反映当前决策状态

**协作感知注意力网络**:
- AGV间协作关系的网络图
- 节点表示AGV，连线表示协作强度
- 动态布局，直观展示协作模式

#### 5. 系统日志
底部的日志区域：
- 实时显示系统事件
- 训练状态变化记录
- 错误和警告信息
- 支持日志清空和导出

### 🔄 实时更新机制

仪表板采用WebSocket技术实现实时数据更新：

- **指标更新**: 每5秒更新一次训练指标
- **注意力数据**: 每10秒更新一次注意力可视化
- **状态同步**: 训练状态变化立即同步
- **连接监控**: 实时显示连接状态

## 集成使用

### 与训练系统集成

#### 1. 基本集成

```python
from src.dashboard.dashboard_integration import get_dashboard_integration

# 获取集成实例
integration = get_dashboard_integration()

# 开始训练
integration.start_training(total_episodes=1000)

# 更新训练数据
for episode in range(1000):
    # 训练逻辑...
    reward = train_one_episode()
    success = check_success()
    
    # 更新仪表板
    integration.update_episode(episode, reward, success)
    integration.update_losses(policy_loss, value_loss)
```

#### 2. 注意力权重集成

```python
# 更新注意力权重
task_attention_weights = model.get_task_attention()
collaboration_weights = model.get_collaboration_attention()

integration.update_attention_weights(
    task_attention=task_attention_weights,
    collaboration_attention=collaboration_weights
)
```

#### 3. 回调函数注册

```python
# 注册训练事件回调
def on_training_start(total_episodes):
    print(f"训练开始，目标回合数: {total_episodes}")

def on_episode_end(episode, reward, success):
    print(f"回合 {episode} 完成，奖励: {reward}")

integration.register_callback("on_training_start", on_training_start)
integration.register_callback("on_episode_end", on_episode_end)
```

### 与MAPPO训练集成

```python
from src.mappo.marllib_integration import MARLlibAGVTrainer
from src.dashboard.dashboard_integration import get_dashboard_integration

# 创建训练器和仪表板集成
trainer = MARLlibAGVTrainer(config)
integration = get_dashboard_integration()

# 训练过程中更新仪表板
def training_callback(result):
    episode = result.get("episodes_total", 0)
    reward = result.get("episode_reward_mean", 0)
    
    integration.update_episode(episode, reward)
    
    # 更新损失（如果可用）
    if "policy_loss" in result:
        integration.update_losses(
            result["policy_loss"], 
            result["value_loss"]
        )

# 注册回调
trainer.register_callback(training_callback)
```

## 高级功能

### 🎛️ 自定义配置

#### 1. 修改更新频率

```python
# 在web_dashboard.py中修改
async def _metrics_update_task(self):
    while True:
        # 修改更新间隔（秒）
        await asyncio.sleep(3)  # 改为3秒更新
```

#### 2. 添加自定义指标

```python
# 在dashboard_integration.py中添加
def update_custom_metric(self, metric_name: str, value: float):
    """更新自定义指标"""
    if "custom_metrics" not in self.metrics_cache:
        self.metrics_cache["custom_metrics"] = {}
    
    self.metrics_cache["custom_metrics"][metric_name] = value
    self._queue_metrics_update()
```

#### 3. 自定义可视化

在`templates/dashboard.html`中添加新的图表区域，在`static/dashboard.js`中实现对应的渲染逻辑。

### 📊 数据导出

仪表板支持多种数据导出格式：

```python
# 导出训练指标
def export_training_data():
    integration = get_dashboard_integration()
    metrics = integration._get_current_metrics()
    
    # 导出为JSON
    with open("training_metrics.json", "w") as f:
        json.dump(metrics, f, indent=2)
    
    # 导出为CSV
    import pandas as pd
    df = pd.DataFrame({
        "episode": range(len(metrics["episode_rewards"])),
        "reward": metrics["episode_rewards"]
    })
    df.to_csv("training_rewards.csv", index=False)
```

## 故障排除

### 常见问题

#### 1. 仪表板无法启动
```bash
# 检查端口是否被占用
netstat -an | grep :8000

# 使用不同端口
python run_dashboard.py --port 8080
```

#### 2. WebSocket连接失败
- 检查防火墙设置
- 确认浏览器支持WebSocket
- 查看浏览器控制台错误信息

#### 3. 数据不更新
- 确认训练系统正确集成了dashboard_integration
- 检查WebSocket连接状态
- 查看系统日志中的错误信息

#### 4. 图表显示异常
- 清除浏览器缓存
- 检查JavaScript控制台错误
- 确认Chart.js和Plotly.js库加载正常

### 调试模式

启用调试模式获取详细日志：

```bash
python run_dashboard.py --debug
```

调试模式提供：
- 详细的请求日志
- WebSocket连接调试信息
- 自动重载功能
- 错误堆栈跟踪

## 性能优化

### 🚀 优化建议

1. **数据缓存**: 限制内存中的数据点数量
2. **更新频率**: 根据需要调整更新间隔
3. **图表优化**: 使用无动画更新提高性能
4. **连接管理**: 及时清理断开的WebSocket连接

### 📈 扩展性

仪表板设计为高度可扩展：

- **模块化组件**: 易于添加新功能
- **插件架构**: 支持自定义可视化插件
- **API扩展**: 简单的REST API扩展
- **主题定制**: 支持自定义CSS主题

## 总结

AGV训练监控Web仪表板为多AGV协同调度系统的训练过程提供了全面、实时、直观的监控解决方案。通过其丰富的可视化功能和灵活的集成接口，研究人员和开发者可以更好地理解和优化训练过程，提高系统性能。

更多技术细节和高级用法，请参考源代码中的详细注释和示例。
