{"best_params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.2, "attention_pos_dim": "32", "hidden_dim": "256", "num_layers": "4", "activation": "tanh", "learning_rate": 0.001, "batch_size": "1024", "num_epochs": "20", "clip_param": 0.05, "entropy_coeff": 0.05, "value_loss_coeff": 0.75, "near_threshold": 3.0, "far_threshold": 15.0, "temperature_scale": 3.0}, "best_score": 0.4935649375351573, "optimization_history": [{"trial": 0, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.2, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "4", "activation": "gelu", "learning_rate": 0.0005, "batch_size": "512", "num_epochs": "30", "clip_param": 0.1, "entropy_coeff": 0.01, "value_loss_coeff": 0.5, "near_threshold": 5.0, "far_threshold": 8.0, "temperature_scale": 3.0}, "score": 0.4812685847840621, "timestamp": "2025-07-23T19:40:28.429734"}, {"trial": 1, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.1, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "3", "activation": "tanh", "learning_rate": 1e-05, "batch_size": "64", "num_epochs": "30", "clip_param": 0.25, "entropy_coeff": 0.01, "value_loss_coeff": 0.5, "near_threshold": 5.0, "far_threshold": 15.0, "temperature_scale": 3.0}, "score": 0.4788558476033036, "timestamp": "2025-07-23T19:40:28.493390"}, {"trial": 2, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.1, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "2", "activation": "tanh", "learning_rate": 0.0005, "batch_size": "512", "num_epochs": "10", "clip_param": 0.3, "entropy_coeff": 0.001, "value_loss_coeff": 1.0, "near_threshold": 5.0, "far_threshold": 15.0, "temperature_scale": 2.0}, "score": 0.4803649029153081, "timestamp": "2025-07-23T19:40:28.556138"}, {"trial": 3, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.2, "attention_pos_dim": "32", "hidden_dim": "256", "num_layers": "4", "activation": "tanh", "learning_rate": 0.001, "batch_size": "1024", "num_epochs": "20", "clip_param": 0.05, "entropy_coeff": 0.05, "value_loss_coeff": 0.75, "near_threshold": 3.0, "far_threshold": 15.0, "temperature_scale": 3.0}, "score": 0.4935649375351573, "timestamp": "2025-07-23T19:40:28.635657"}, {"trial": 4, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.0, "attention_pos_dim": "32", "hidden_dim": "256", "num_layers": "4", "activation": "tanh", "learning_rate": 5e-05, "batch_size": "32", "num_epochs": "5", "clip_param": 0.3, "entropy_coeff": 0.0001, "value_loss_coeff": 0.25, "near_threshold": 3.0, "far_threshold": 8.0, "temperature_scale": 2.0}, "score": 0.4739759127760995, "timestamp": "2025-07-23T19:40:28.699528"}, {"trial": 5, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.1, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "4", "activation": "tanh", "learning_rate": 0.0005, "batch_size": "256", "num_epochs": "8", "clip_param": 0.3, "entropy_coeff": 0.2, "value_loss_coeff": 0.75, "near_threshold": 3.0, "far_threshold": 15.0, "temperature_scale": 2.0}, "score": 0.47902037976825707, "timestamp": "2025-07-23T19:40:28.762993"}, {"trial": 6, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.2, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "2", "activation": "gelu", "learning_rate": 0.0003, "batch_size": "1024", "num_epochs": "8", "clip_param": 0.05, "entropy_coeff": 0.05, "value_loss_coeff": 0.1, "near_threshold": 3.0, "far_threshold": 8.0, "temperature_scale": 2.0}, "score": 0.4675059627528647, "timestamp": "2025-07-23T19:40:28.826458"}, {"trial": 7, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.2, "attention_pos_dim": "64", "hidden_dim": "256", "num_layers": "2", "activation": "relu", "learning_rate": 0.0001, "batch_size": "32", "num_epochs": "5", "clip_param": 0.3, "entropy_coeff": 0.0001, "value_loss_coeff": 1.5, "near_threshold": 2.0, "far_threshold": 10.0, "temperature_scale": 2.0}, "score": 0.48736035017756785, "timestamp": "2025-07-23T19:40:28.905579"}, {"trial": 8, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.0, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "4", "activation": "relu", "learning_rate": 0.0005, "batch_size": "32", "num_epochs": "30", "clip_param": 0.3, "entropy_coeff": 0.05, "value_loss_coeff": 1.5, "near_threshold": 2.0, "far_threshold": 10.0, "temperature_scale": 2.0}, "score": 0.4789302229788177, "timestamp": "2025-07-23T19:40:28.969053"}, {"trial": 9, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.1, "attention_pos_dim": "64", "hidden_dim": "256", "num_layers": "3", "activation": "relu", "learning_rate": 0.0003, "batch_size": "512", "num_epochs": "8", "clip_param": 0.2, "entropy_coeff": 0.2, "value_loss_coeff": 0.75, "near_threshold": 2.0, "far_threshold": 10.0, "temperature_scale": 1.0}, "score": 0.483454774766065, "timestamp": "2025-07-23T19:40:29.032549"}, {"trial": 10, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.1, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "4", "activation": "gelu", "learning_rate": 0.0003, "batch_size": "32", "num_epochs": "10", "clip_param": 0.1, "entropy_coeff": 0.0001, "value_loss_coeff": 0.75, "near_threshold": 2.0, "far_threshold": 8.0, "temperature_scale": 3.0}, "score": 0.4783693272405988, "timestamp": "2025-07-23T19:40:29.111905"}, {"trial": 11, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.2, "attention_pos_dim": "32", "hidden_dim": "256", "num_layers": "4", "activation": "relu", "learning_rate": 0.0005, "batch_size": "32", "num_epochs": "5", "clip_param": 0.2, "entropy_coeff": 0.01, "value_loss_coeff": 1.5, "near_threshold": 3.0, "far_threshold": 8.0, "temperature_scale": 3.0}, "score": 0.4785229831987099, "timestamp": "2025-07-23T19:40:29.175456"}, {"trial": 12, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.0, "attention_pos_dim": "64", "hidden_dim": "256", "num_layers": "4", "activation": "gelu", "learning_rate": 0.0005, "batch_size": "32", "num_epochs": "15", "clip_param": 0.3, "entropy_coeff": 0.1, "value_loss_coeff": 0.5, "near_threshold": 5.0, "far_threshold": 8.0, "temperature_scale": 1.0}, "score": 0.4852408899767383, "timestamp": "2025-07-23T19:40:29.239066"}, {"trial": 13, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.1, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "4", "activation": "tanh", "learning_rate": 0.001, "batch_size": "512", "num_epochs": "30", "clip_param": 0.25, "entropy_coeff": 0.01, "value_loss_coeff": 0.1, "near_threshold": 5.0, "far_threshold": 8.0, "temperature_scale": 1.0}, "score": 0.4808160791186127, "timestamp": "2025-07-23T19:40:29.302821"}, {"trial": 14, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.2, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "3", "activation": "gelu", "learning_rate": 0.0001, "batch_size": "1024", "num_epochs": "3", "clip_param": 0.05, "entropy_coeff": 0.05, "value_loss_coeff": 0.1, "near_threshold": 2.0, "far_threshold": 15.0, "temperature_scale": 3.0}, "score": 0.4845432058492991, "timestamp": "2025-07-23T19:40:29.381969"}], "total_trials": 15, "optimization_time": 1.0316994190216064, "convergence_info": {"total_trials": 15, "best_score": 0.4935649375351573, "score_std": 0.00572715449608441, "score_range": [0.4675059627528647, 0.4935649375351573], "improvement_rate": 0.07142857142857142, "convergence_trial": 3}, "search_space": {"attention_feature_dim": [64], "attention_num_heads": [8], "attention_dropout": [0.0, 0.1, 0.2], "attention_pos_dim": [16, 32, 64], "hidden_dim": [256], "num_layers": [2, 3, 4], "activation": ["relu", "tanh", "gelu"], "learning_rate": [1e-05, 5e-05, 0.0001, 0.0003, 0.0005, 0.001, 0.003], "batch_size": [32, 64, 128, 256, 512, 1024], "num_epochs": [3, 5, 8, 10, 15, 20, 30], "clip_param": [0.05, 0.1, 0.15, 0.2, 0.25, 0.3], "entropy_coeff": [0.0001, 0.001, 0.01, 0.05, 0.1, 0.2], "value_loss_coeff": [0.1, 0.25, 0.5, 0.75, 1.0, 1.5], "near_threshold": [2.0, 3.0, 5.0], "far_threshold": [8.0, 10.0, 15.0], "temperature_scale": [1.0, 2.0, 3.0]}, "optimization_config": {"method": "random", "n_trials": 15, "n_jobs": 1, "random_seed": 42}, "timestamp": "2025-07-23T19:40:29.381969"}