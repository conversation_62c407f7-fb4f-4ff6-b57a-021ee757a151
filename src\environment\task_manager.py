"""
任务管理器
管理仓储环境中的所有任务
"""

from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
import random
import time

from config.env_config import EnvironmentConfig


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = 0     # 待处理
    ASSIGNED = 1    # 已分配
    IN_PROGRESS = 2 # 进行中
    COMPLETED = 3   # 已完成
    CANCELLED = 4   # 已取消


class Task:
    """任务类"""

    def __init__(self, task_id: int, position: Tuple[int, int],
                 weight: int):
        """
        初始化任务

        Args:
            task_id: 任务唯一标识
            position: 任务位置 (x, y)
            weight: 任务重量（只有5或10）
        """
        self.task_id = task_id
        self.position = position
        self.weight = weight
        self.status = TaskStatus.PENDING

        # 时间信息
        self.created_time = time.time()
        self.assigned_time = None
        self.completed_time = None

        # 分配信息
        self.assigned_agv = None

    def assign_to_agv(self, agv_id: int):
        """分配任务给AGV"""
        self.assigned_agv = agv_id
        self.assigned_time = time.time()
        self.status = TaskStatus.ASSIGNED

    def start_execution(self):
        """开始执行任务"""
        self.status = TaskStatus.IN_PROGRESS

    def complete(self):
        """完成任务"""
        self.completed_time = time.time()
        self.status = TaskStatus.COMPLETED

    def cancel(self):
        """取消任务"""
        self.status = TaskStatus.CANCELLED

    def get_waiting_time(self) -> float:
        """获取等待时间"""
        if self.assigned_time:
            return self.assigned_time - self.created_time
        return time.time() - self.created_time

    def get_execution_time(self) -> float:
        """获取执行时间"""
        if self.completed_time and self.assigned_time:
            return self.completed_time - self.assigned_time
        return 0.0

    def get_state_vector(self, map_width: int, map_height: int) -> List[float]:
        """
        获取任务状态向量

        Args:
            map_width: 地图宽度
            map_height: 地图高度

        Returns:
            state_vector: 状态向量 [x_norm, y_norm, weight_norm, status]
        """
        x_norm = self.position[0] / map_width
        y_norm = self.position[1] / map_height
        weight_norm = self.weight / 10  # 重量5或10，归一化到0.5或1.0
        status_norm = self.status.value / 4  # 状态归一化

        return [x_norm, y_norm, weight_norm, status_norm]

    def __str__(self) -> str:
        """字符串表示"""
        return f"Task{self.task_id}@{self.position} [W:{self.weight}, {self.status.name}]"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"Task(id={self.task_id}, pos={self.position}, "
                f"weight={self.weight}, status={self.status.name}, agv={self.assigned_agv})")


class TaskManager:
    """任务管理器"""

    def __init__(self, config: EnvironmentConfig):
        """
        初始化任务管理器

        Args:
            config: 环境配置
        """
        self.config = config
        self.tasks: Dict[int, Task] = {}
        self.task_counter = 0

        # 统计信息
        self.total_created = 0
        self.total_completed = 0
        self.total_cancelled = 0

    def create_task(self, task_id: int, position: Tuple[int, int],
                   weight: int) -> Task:
        """
        创建新任务

        Args:
            task_id: 任务ID
            position: 任务位置
            weight: 任务重量（5或10）

        Returns:
            task: 创建的任务对象
        """
        task = Task(task_id, position, weight)
        self.tasks[task_id] = task
        self.total_created += 1
        return task

    def get_task(self, task_id: int) -> Optional[Task]:
        """获取指定任务"""
        return self.tasks.get(task_id)

    def get_task_position(self, task_id: int) -> Optional[Tuple[int, int]]:
        """获取任务位置"""
        task = self.get_task(task_id)
        return task.position if task else None

    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        return list(self.tasks.values())

    def get_pending_tasks(self) -> List[Task]:
        """获取待处理任务"""
        return [task for task in self.tasks.values()
                if task.status == TaskStatus.PENDING]

    def get_assigned_tasks(self) -> List[Task]:
        """获取已分配任务"""
        return [task for task in self.tasks.values()
                if task.status == TaskStatus.ASSIGNED]

    def get_completed_tasks(self) -> List[Task]:
        """获取已完成任务"""
        return [task for task in self.tasks.values()
                if task.status == TaskStatus.COMPLETED]

    def assign_task_to_agv(self, task_id: int, agv_id: int) -> bool:
        """
        分配任务给AGV

        Args:
            task_id: 任务ID
            agv_id: AGV ID

        Returns:
            success: 是否成功分配
        """
        task = self.get_task(task_id)
        if task and task.status == TaskStatus.PENDING:
            task.assign_to_agv(agv_id)
            return True
        return False

    def complete_task(self, task_id: int) -> bool:
        """
        完成任务

        Args:
            task_id: 任务ID

        Returns:
            success: 是否成功完成
        """
        task = self.get_task(task_id)
        if task and task.status in [TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS]:
            task.complete()
            self.total_completed += 1
            return True
        return False

    def cancel_task(self, task_id: int) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            success: 是否成功取消
        """
        task = self.get_task(task_id)
        if task and task.status != TaskStatus.COMPLETED:
            task.cancel()
            self.total_cancelled += 1
            return True
        return False

    def get_tasks_by_weight(self, weight: int) -> List[Task]:
        """
        根据重量获取任务

        Args:
            weight: 任务重量（5或10）

        Returns:
            tasks: 指定重量的任务列表
        """
        return [task for task in self.tasks.values()
                if task.weight == weight and task.status == TaskStatus.PENDING]

    def get_nearest_tasks(self, position: Tuple[int, int], max_distance: int = 5) -> List[Task]:
        """
        获取指定位置附近的任务

        Args:
            position: 参考位置
            max_distance: 最大距离

        Returns:
            tasks: 附近的任务列表
        """
        nearby_tasks = []
        for task in self.get_pending_tasks():
            distance = abs(task.position[0] - position[0]) + abs(task.position[1] - position[1])
            if distance <= max_distance:
                nearby_tasks.append(task)

        # 按距离排序
        nearby_tasks.sort(key=lambda t: abs(t.position[0] - position[0]) + abs(t.position[1] - position[1]))
        return nearby_tasks

    def get_tasks_by_weight_range(self, min_weight: int, max_weight: int) -> List[Task]:
        """
        根据重量范围获取任务

        Args:
            min_weight: 最小重量
            max_weight: 最大重量

        Returns:
            tasks: 指定重量范围的任务列表
        """
        return [task for task in self.get_pending_tasks()
                if min_weight <= task.weight <= max_weight]

    def update(self):
        """更新任务状态（每步调用）"""
        # 检查超时任务
        current_time = time.time()
        for task in self.tasks.values():
            if (task.status == TaskStatus.ASSIGNED and
                current_time - task.assigned_time > 300):  # 5分钟超时
                task.cancel()
                self.total_cancelled += 1

    def all_tasks_completed(self) -> bool:
        """检查是否所有任务都已完成"""
        return all(task.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]
                  for task in self.tasks.values())

    def get_total_tasks(self) -> int:
        """获取总任务数"""
        return len(self.tasks)

    def get_completion_rate(self) -> float:
        """获取完成率"""
        if not self.tasks:
            return 0.0
        return self.total_completed / len(self.tasks)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        pending_count = len(self.get_pending_tasks())
        assigned_count = len(self.get_assigned_tasks())
        completed_count = len(self.get_completed_tasks())

        # 计算平均等待时间
        avg_waiting_time = 0.0
        completed_tasks = self.get_completed_tasks()
        if completed_tasks:
            total_waiting_time = sum(task.get_waiting_time() for task in completed_tasks)
            avg_waiting_time = total_waiting_time / len(completed_tasks)

        # 计算平均执行时间
        avg_execution_time = 0.0
        if completed_tasks:
            total_execution_time = sum(task.get_execution_time() for task in completed_tasks)
            avg_execution_time = total_execution_time / len(completed_tasks)

        return {
            "total_tasks": len(self.tasks),
            "pending_tasks": pending_count,
            "assigned_tasks": assigned_count,
            "completed_tasks": completed_count,
            "cancelled_tasks": self.total_cancelled,
            "completion_rate": self.get_completion_rate(),
            "avg_waiting_time": avg_waiting_time,
            "avg_execution_time": avg_execution_time
        }

    def reset(self):
        """重置任务管理器"""
        self.tasks.clear()
        self.task_counter = 0
        self.total_created = 0
        self.total_completed = 0
        self.total_cancelled = 0

    def generate_random_tasks(self, num_tasks: int, map_width: int, map_height: int,
                            shelf_positions: set) -> List[Task]:
        """
        生成随机任务

        Args:
            num_tasks: 任务数量
            map_width: 地图宽度
            map_height: 地图高度
            shelf_positions: 货架位置集合

        Returns:
            tasks: 生成的任务列表
        """
        tasks = []

        # 获取可用位置
        available_positions = []
        for x in range(map_width):
            for y in range(map_height):
                if (x, y) not in shelf_positions:
                    available_positions.append((x, y))

        for i in range(min(num_tasks, len(available_positions))):
            position = random.choice(available_positions)
            available_positions.remove(position)

            weight = random.choice(self.config.task_weights)

            task = self.create_task(i, position, weight)
            tasks.append(task)

        return tasks

    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_statistics()
        return (f"TaskManager: {stats['total_tasks']} tasks "
                f"(P:{stats['pending_tasks']}, A:{stats['assigned_tasks']}, "
                f"C:{stats['completed_tasks']}, X:{stats['cancelled_tasks']})")

    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"TaskManager(tasks={len(self.tasks)}, completed={self.total_completed})"