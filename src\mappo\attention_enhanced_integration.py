"""
注意力增强MAPPO集成模块
将注意力增强的策略价值网络集成到完整的训练流程中
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
from collections import defaultdict, deque
import time
import os

from .attention_enhanced_trainer import (
    AttentionEnhancedMAPPOTrainer,
    TrainingBatch,
    TrainingMetrics
)
from .attention_enhanced_networks import (
    AttentionEnhancedPolicyNetwork,
    AttentionEnhancedValueNetwork
)
from ..environment.agv_entity import AGVEntity
from ..environment.task_manager import Task
from ..utils.state_representation import StateSpaceManager


class AttentionEnhancedMAPPOAgent:
    """注意力增强的MAPPO智能体"""
    
    def __init__(self,
                 config: Dict[str, Any],
                 device: str = 'cpu'):
        
        self.config = config
        self.device = device
        
        # 网络参数
        self.feature_dim = config.get('feature_dim', 64)
        self.hidden_dim = config.get('hidden_dim', 256)
        self.action_dim = config.get('action_dim', 7)
        self.num_heads = config.get('num_heads', 8)
        
        # 训练参数
        self.learning_rate = config.get('learning_rate', 3e-4)
        self.clip_param = config.get('clip_param', 0.2)
        self.value_loss_coeff = config.get('value_loss_coeff', 0.5)
        self.entropy_coeff = config.get('entropy_coeff', 0.01)
        self.attention_loss_coeff = config.get('attention_loss_coeff', 0.1)
        self.max_grad_norm = config.get('max_grad_norm', 0.5)
        
        # 创建训练器
        self.trainer = AttentionEnhancedMAPPOTrainer(
            feature_dim=self.feature_dim,
            hidden_dim=self.hidden_dim,
            action_dim=self.action_dim,
            num_heads=self.num_heads,
            learning_rate=self.learning_rate,
            clip_param=self.clip_param,
            value_loss_coeff=self.value_loss_coeff,
            entropy_coeff=self.entropy_coeff,
            attention_loss_coeff=self.attention_loss_coeff,
            max_grad_norm=self.max_grad_norm,
            device=device
        )
        
        # 经验缓冲区
        self.buffer_size = config.get('buffer_size', 10000)
        self.batch_size = config.get('batch_size', 256)
        self.experience_buffer = deque(maxlen=self.buffer_size)
        
        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.training_metrics_history = []
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    def process_observations(self,
                           agvs: List[AGVEntity],
                           tasks: List[Task],
                           state_manager: StateSpaceManager) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        处理观察，转换为网络输入格式
        
        Args:
            agvs: AGV列表
            tasks: 任务列表
            state_manager: 状态管理器
            
        Returns:
            agv_features: AGV特征张量
            task_features: 任务特征张量
            agv_positions: AGV位置张量
        """
        # 获取AGV特征
        agv_features_list = []
        agv_positions_list = []
        
        for agv in agvs:
            # 获取AGV状态嵌入
            agv_embedding = state_manager.get_agv_embedding(agv)
            agv_features_list.append(agv_embedding)
            
            # 获取AGV位置
            agv_positions_list.append(torch.tensor(agv.position, dtype=torch.float32))
        
        # 获取任务特征
        task_features_list = []
        for task in tasks:
            task_embedding = state_manager.get_task_embedding(task)
            task_features_list.append(task_embedding)
        
        # 转换为张量
        agv_features = torch.stack(agv_features_list).unsqueeze(0)  # [1, num_agvs, feature_dim]
        agv_positions = torch.stack(agv_positions_list).unsqueeze(0)  # [1, num_agvs, 2]
        
        if task_features_list:
            task_features = torch.stack(task_features_list).unsqueeze(0)  # [1, num_tasks, feature_dim]
        else:
            # 如果没有任务，创建空的任务特征
            task_features = torch.zeros(1, 1, self.feature_dim)
        
        return agv_features.to(self.device), task_features.to(self.device), agv_positions.to(self.device)
    
    def get_actions(self,
                   agvs: List[AGVEntity],
                   tasks: List[Task],
                   state_manager: StateSpaceManager,
                   action_masks: Optional[torch.Tensor] = None,
                   deterministic: bool = False) -> Tuple[List[int], torch.Tensor]:
        """
        获取动作
        
        Args:
            agvs: AGV列表
            tasks: 任务列表
            state_manager: 状态管理器
            action_masks: 动作掩码
            deterministic: 是否确定性选择
            
        Returns:
            actions: 动作列表
            log_probs: 对数概率
        """
        # 处理观察
        agv_features, task_features, agv_positions = self.process_observations(
            agvs, tasks, state_manager
        )
        
        # 获取动作
        actions_tensor, log_probs = self.trainer.get_action(
            agv_features, task_features, agv_positions, action_masks, deterministic
        )
        
        # 转换为列表
        actions = actions_tensor.cpu().numpy().tolist()
        
        return actions, log_probs
    
    def store_experience(self,
                        agvs: List[AGVEntity],
                        tasks: List[Task],
                        actions: List[int],
                        rewards: List[float],
                        next_agvs: List[AGVEntity],
                        next_tasks: List[Task],
                        dones: List[bool],
                        state_manager: StateSpaceManager):
        """
        存储经验
        
        Args:
            agvs: 当前AGV状态
            tasks: 当前任务状态
            actions: 执行的动作
            rewards: 获得的奖励
            next_agvs: 下一步AGV状态
            next_tasks: 下一步任务状态
            dones: 是否结束
            state_manager: 状态管理器
        """
        # 处理当前观察
        agv_features, task_features, agv_positions = self.process_observations(
            agvs, tasks, state_manager
        )
        
        # 处理下一步观察
        next_agv_features, next_task_features, next_agv_positions = self.process_observations(
            next_agvs, next_tasks, state_manager
        )
        
        # 计算价值估计
        with torch.no_grad():
            values = self.trainer.value_network(
                agv_features, task_features, agv_positions
            )
            next_values = self.trainer.value_network(
                next_agv_features, next_task_features, next_agv_positions
            )
        
        # 存储经验
        experience = {
            'agv_features': agv_features.cpu(),
            'task_features': task_features.cpu(),
            'agv_positions': agv_positions.cpu(),
            'actions': torch.tensor(actions),
            'rewards': torch.tensor(rewards, dtype=torch.float32),
            'values': values.cpu(),
            'next_values': next_values.cpu(),
            'dones': torch.tensor(dones, dtype=torch.bool)
        }
        
        self.experience_buffer.append(experience)
    
    def train(self, num_epochs: int = 10) -> Optional[TrainingMetrics]:
        """
        训练智能体
        
        Args:
            num_epochs: 训练轮数
            
        Returns:
            metrics: 训练指标
        """
        if len(self.experience_buffer) < self.batch_size:
            return None
        
        # 采样经验
        batch_experiences = list(self.experience_buffer)[-self.batch_size:]
        
        # 构建训练批次
        agv_features = torch.cat([exp['agv_features'] for exp in batch_experiences], dim=0)
        task_features = torch.cat([exp['task_features'] for exp in batch_experiences], dim=0)
        agv_positions = torch.cat([exp['agv_positions'] for exp in batch_experiences], dim=0)
        actions = torch.cat([exp['actions'] for exp in batch_experiences], dim=0)
        rewards = torch.cat([exp['rewards'] for exp in batch_experiences], dim=0)
        values = torch.cat([exp['values'] for exp in batch_experiences], dim=0)
        
        # 计算优势
        advantages = torch.zeros_like(rewards)
        returns = torch.zeros_like(rewards)
        
        # 简化的优势计算（实际应该使用GAE）
        for i in range(len(batch_experiences)):
            exp = batch_experiences[i]
            if i < len(batch_experiences) - 1:
                next_exp = batch_experiences[i + 1]
                td_error = exp['rewards'] + 0.99 * next_exp['values'] * (1 - exp['dones'].float()) - exp['values']
            else:
                td_error = exp['rewards'] - exp['values']
            
            advantages[i] = td_error.squeeze()
            returns[i] = advantages[i] + exp['values'].squeeze()
        
        # 创建训练批次
        training_batch = TrainingBatch(
            agv_features=agv_features.to(self.device),
            task_features=task_features.to(self.device),
            agv_positions=agv_positions.to(self.device),
            actions=actions.to(self.device),
            rewards=rewards.to(self.device),
            values=values.to(self.device),
            advantages=advantages.to(self.device)
        )
        
        # 执行训练
        metrics = self.trainer.train_step(training_batch, num_epochs)
        
        # 记录训练指标
        self.training_metrics_history.append(metrics)
        
        # 清空部分经验缓冲区
        if len(self.experience_buffer) > self.buffer_size * 0.8:
            for _ in range(self.batch_size):
                self.experience_buffer.popleft()
        
        return metrics
    
    def evaluate(self,
                agvs: List[AGVEntity],
                tasks: List[Task],
                state_manager: StateSpaceManager) -> Dict[str, float]:
        """
        评估智能体性能
        
        Args:
            agvs: AGV列表
            tasks: 任务列表
            state_manager: 状态管理器
            
        Returns:
            evaluation_metrics: 评估指标
        """
        # 获取动作（确定性）
        actions, _ = self.get_actions(agvs, tasks, state_manager, deterministic=True)
        
        # 处理观察
        agv_features, task_features, agv_positions = self.process_observations(
            agvs, tasks, state_manager
        )
        
        # 获取策略输出
        with torch.no_grad():
            policy_output = self.trainer.policy_network(
                agv_features, task_features, agv_positions
            )
            
            value_estimates = self.trainer.value_network(
                agv_features, task_features, agv_positions
            )
        
        # 计算评估指标
        action_entropy = -(torch.softmax(policy_output.policy_logits, dim=-1) * 
                          torch.log_softmax(policy_output.policy_logits, dim=-1)).sum(dim=-1).mean()
        
        evaluation_metrics = {
            'action_entropy': action_entropy.item(),
            'attention_entropy': policy_output.attention_entropy.item(),
            'average_value': value_estimates.mean().item(),
            'task_attention_max': policy_output.task_attention_weights.max().item(),
            'collaboration_attention_max': policy_output.collaboration_attention_weights.max().item(),
            'feature_importance_mean': policy_output.feature_importance.mean().item()
        }
        
        return evaluation_metrics
    
    def save_model(self, filepath: str):
        """保存模型"""
        self.trainer.save_checkpoint(filepath)
        
        # 保存额外的智能体状态
        agent_state = {
            'config': self.config,
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'training_metrics_history': [
                {
                    'policy_loss': m.policy_loss,
                    'value_loss': m.value_loss,
                    'attention_loss': m.attention_loss,
                    'total_loss': m.total_loss,
                    'policy_entropy': m.policy_entropy,
                    'attention_entropy': m.attention_entropy,
                    'kl_divergence': m.kl_divergence,
                    'explained_variance': m.explained_variance,
                    'gradient_norm': m.gradient_norm
                } for m in self.training_metrics_history
            ]
        }
        
        agent_filepath = filepath.replace('.pth', '_agent.pth')
        torch.save(agent_state, agent_filepath)
        
        self.logger.info(f"模型已保存到: {filepath}")
    
    def load_model(self, filepath: str):
        """加载模型"""
        self.trainer.load_checkpoint(filepath)
        
        # 加载额外的智能体状态
        agent_filepath = filepath.replace('.pth', '_agent.pth')
        if os.path.exists(agent_filepath):
            agent_state = torch.load(agent_filepath, map_location=self.device)
            self.episode_rewards = agent_state.get('episode_rewards', [])
            self.episode_lengths = agent_state.get('episode_lengths', [])
            
            # 重建训练指标历史
            metrics_data = agent_state.get('training_metrics_history', [])
            self.training_metrics_history = [
                TrainingMetrics(**m) for m in metrics_data
            ]
        
        self.logger.info(f"模型已从 {filepath} 加载")
    
    def get_training_statistics(self) -> Dict[str, Any]:
        """获取训练统计信息"""
        if not self.training_metrics_history:
            return {}
        
        recent_metrics = self.training_metrics_history[-100:]  # 最近100次训练
        
        stats = {
            'total_training_steps': len(self.training_metrics_history),
            'recent_policy_loss': np.mean([m.policy_loss for m in recent_metrics]),
            'recent_value_loss': np.mean([m.value_loss for m in recent_metrics]),
            'recent_attention_loss': np.mean([m.attention_loss for m in recent_metrics]),
            'recent_total_loss': np.mean([m.total_loss for m in recent_metrics]),
            'recent_policy_entropy': np.mean([m.policy_entropy for m in recent_metrics]),
            'recent_attention_entropy': np.mean([m.attention_entropy for m in recent_metrics]),
            'recent_kl_divergence': np.mean([m.kl_divergence for m in recent_metrics]),
            'recent_explained_variance': np.mean([m.explained_variance for m in recent_metrics]),
            'recent_gradient_norm': np.mean([m.gradient_norm for m in recent_metrics]),
            'total_episodes': len(self.episode_rewards),
            'average_episode_reward': np.mean(self.episode_rewards[-100:]) if self.episode_rewards else 0,
            'average_episode_length': np.mean(self.episode_lengths[-100:]) if self.episode_lengths else 0
        }
        
        return stats
