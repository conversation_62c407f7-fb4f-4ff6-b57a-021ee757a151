"""
基于双层注意力机制的增强MAPPO算法
集成任务分配注意力和协作感知注意力的MAPPO实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.models.modelv2 import ModelV2
from ray.rllib.utils.annotations import override
from ray.rllib.utils.typing import ModelConfigDict, TensorType

from ..attention.task_allocation_attention import (
    MultiHeadTaskAllocationAttention,
    TaskAllocationAttentionManager
)
from ..attention.collaboration_attention import (
    HierarchicalCollaborationAttention,
    CollaborationAttentionManager
)


class AttentionEnhancedMAPPOModel(TorchModelV2, nn.Module):
    """
    基于双层注意力机制的MAPPO模型
    集成任务分配注意力和协作感知注意力
    """
    
    def __init__(self, obs_space, action_space, num_outputs, model_config, name, **kwargs):
        """
        初始化注意力增强MAPPO模型

        Args:
            obs_space: 观察空间
            action_space: 动作空间
            num_outputs: 输出维度
            model_config: 模型配置
            name: 模型名称
            **kwargs: 额外的关键字参数（兼容性）
        """
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        # 解析配置
        self.model_config = model_config
        self.custom_config = model_config.get("custom_model_config", {})
        
        # 网络参数
        self.obs_dim = obs_space.shape[0]  # 224维观察
        # 动作空间维度
        if hasattr(action_space, 'n'):
            # Discrete动作空间
            self.action_dim = action_space.n
        else:
            # Box动作空间
            self.action_dim = action_space.shape[0]
        self.hidden_dim = self.custom_config.get("hidden_dim", 256)
        self.feature_dim = self.custom_config.get("feature_dim", 64)
        self.num_heads = self.custom_config.get("num_heads", 8)
        self.dropout = self.custom_config.get("dropout", 0.1)
        
        # 观察解码器：将224维观察分解为不同组件
        self.obs_decoder = nn.Sequential(
            nn.Linear(self.obs_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU()
        )
        
        # 特征提取器：提取核心特征
        self.feature_extractor = nn.Sequential(
            nn.Linear(self.hidden_dim, self.feature_dim),
            nn.ReLU(),
            nn.LayerNorm(self.feature_dim)
        )
        
        # 双层注意力机制
        self.task_attention = MultiHeadTaskAllocationAttention(
            feature_dim=self.feature_dim,
            num_heads=self.num_heads,
            dropout=self.dropout
        )
        
        self.collaboration_attention = HierarchicalCollaborationAttention(
            feature_dim=self.feature_dim,
            pos_dim=32,
            num_heads=self.num_heads,
            dropout=self.dropout
        )
        
        # 注意力融合层
        self.attention_fusion = nn.Sequential(
            nn.Linear(self.feature_dim * 3, self.hidden_dim),  # 原始特征 + 任务注意力 + 协作注意力
            nn.ReLU(),
            nn.LayerNorm(self.hidden_dim)
        )
        
        # 策略网络（Actor）
        self.policy_net = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, num_outputs)
        )
        
        # 价值网络（Critic）
        self.value_net = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_dim // 2, 1)
        )
        
        # 存储中间结果
        self._features = None
        self._value = None
        
        # 注意力权重存储（用于分析）
        self.attention_weights = {
            'task_attention': None,
            'collaboration_attention': None
        }
    
    @override(TorchModelV2)
    def forward(self, input_dict, state, seq_lens):
        """
        前向传播
        
        Args:
            input_dict: 输入字典，包含观察
            state: RNN状态（如果使用）
            seq_lens: 序列长度
            
        Returns:
            logits: 策略logits
            state: 新的RNN状态
        """
        obs = input_dict["obs"]
        batch_size = obs.shape[0]
        
        # 观察解码
        decoded_obs = self.obs_decoder(obs)
        
        # 特征提取
        base_features = self.feature_extractor(decoded_obs)
        
        # 分解观察为不同组件（基于AGV环境适配器的观察结构）
        # obs = [self_state(64) + task_attention_features(64) + collaboration_features(64) + global_context(32)]
        self_state = obs[:, :64]
        task_features = obs[:, 64:128]
        collab_features = obs[:, 128:192]
        global_context = obs[:, 192:]
        
        # 模拟双层注意力计算（在实际环境中，这些已经在环境适配器中计算）
        # 这里我们使用已经计算好的特征，并进一步处理
        
        # 任务注意力增强
        task_enhanced_features = self._apply_task_attention(base_features, task_features)
        
        # 协作注意力增强
        collab_enhanced_features = self._apply_collaboration_attention(base_features, collab_features)
        
        # 融合所有注意力特征
        fused_features = torch.cat([
            base_features,
            task_enhanced_features,
            collab_enhanced_features
        ], dim=-1)
        
        # 注意力融合
        self._features = self.attention_fusion(fused_features)
        
        # 策略输出
        logits = self.policy_net(self._features)
        
        return logits, state
    
    def _apply_task_attention(self, base_features, task_features):
        """
        应用任务注意力机制

        Args:
            base_features: 基础特征
            task_features: 任务特征

        Returns:
            enhanced_features: 增强后的特征
        """
        # 确保特征维度匹配
        if base_features.shape[-1] != task_features.shape[-1]:
            # 如果维度不匹配，使用线性变换对齐
            if not hasattr(self, 'task_feature_proj'):
                self.task_feature_proj = nn.Linear(task_features.shape[-1], base_features.shape[-1])
            task_features = self.task_feature_proj(task_features)

        # 简化的任务注意力应用
        # 计算自注意力（base_features与自身的注意力）
        attention_scores = torch.matmul(base_features, base_features.transpose(-2, -1)) / np.sqrt(base_features.shape[-1])
        attention_weights = F.softmax(attention_scores, dim=-1)

        # 存储注意力权重用于分析
        self.attention_weights['task_attention'] = attention_weights.detach()

        # 应用注意力到任务特征
        enhanced_features = torch.matmul(attention_weights, task_features)

        return enhanced_features
    
    def _apply_collaboration_attention(self, base_features, collab_features):
        """
        应用协作注意力机制

        Args:
            base_features: 基础特征
            collab_features: 协作特征

        Returns:
            enhanced_features: 增强后的特征
        """
        # 确保特征维度匹配
        if base_features.shape[-1] != collab_features.shape[-1]:
            # 如果维度不匹配，使用线性变换对齐
            if not hasattr(self, 'collab_feature_proj'):
                self.collab_feature_proj = nn.Linear(collab_features.shape[-1], base_features.shape[-1])
            collab_features = self.collab_feature_proj(collab_features)

        # 简化的协作注意力应用
        # 计算自注意力（base_features与自身的注意力）
        attention_scores = torch.matmul(base_features, base_features.transpose(-2, -1)) / np.sqrt(base_features.shape[-1])
        attention_weights = F.softmax(attention_scores, dim=-1)

        # 存储注意力权重用于分析
        self.attention_weights['collaboration_attention'] = attention_weights.detach()

        # 应用注意力到协作特征
        enhanced_features = torch.matmul(attention_weights, collab_features)

        return enhanced_features
    
    @override(TorchModelV2)
    def value_function(self):
        """
        计算价值函数
        
        Returns:
            value: 状态价值
        """
        if self._features is None:
            raise ValueError("必须先调用forward()方法")
        
        self._value = self.value_net(self._features).squeeze(-1)
        return self._value
    
    def get_attention_weights(self):
        """
        获取注意力权重（用于分析和可视化）
        
        Returns:
            attention_weights: 注意力权重字典
        """
        return self.attention_weights.copy()
    
    def get_model_info(self):
        """
        获取模型信息
        
        Returns:
            model_info: 模型信息字典
        """
        return {
            'model_type': 'AttentionEnhancedMAPPO',
            'obs_dim': self.obs_dim,
            'action_dim': self.action_dim,
            'hidden_dim': self.hidden_dim,
            'feature_dim': self.feature_dim,
            'num_heads': self.num_heads,
            'dropout': self.dropout,
            'total_parameters': sum(p.numel() for p in self.parameters()),
            'trainable_parameters': sum(p.numel() for p in self.parameters() if p.requires_grad)
        }


class AttentionEnhancedMAPPOTrainer:
    """
    基于双层注意力机制的MAPPO训练器
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练器
        
        Args:
            config: 训练配置
        """
        self.config = config
        self.model_config = config.get('model_config', {})
        
        # 训练参数
        self.learning_rate = config.get('learning_rate', 3e-4)
        self.batch_size = config.get('batch_size', 256)
        self.num_epochs = config.get('num_epochs', 10)
        self.clip_param = config.get('clip_param', 0.2)
        self.value_loss_coeff = config.get('value_loss_coeff', 0.5)
        self.entropy_coeff = config.get('entropy_coeff', 0.01)
        
        # 注意力机制参数
        self.attention_loss_coeff = config.get('attention_loss_coeff', 0.1)
        self.attention_regularization = config.get('attention_regularization', True)
        
    def create_model_config(self):
        """
        创建模型配置
        
        Returns:
            model_config: 模型配置字典
        """
        return {
            "custom_model": "attention_enhanced_mappo",
            "custom_model_config": {
                "hidden_dim": self.model_config.get("hidden_dim", 256),
                "feature_dim": self.model_config.get("feature_dim", 64),
                "num_heads": self.model_config.get("num_heads", 8),
                "dropout": self.model_config.get("dropout", 0.1)
            }
        }
    
    def create_training_config(self):
        """
        创建训练配置
        
        Returns:
            training_config: 训练配置字典
        """
        return {
            # MAPPO基础配置
            "framework": "torch",
            "lr": self.learning_rate,
            "train_batch_size": self.batch_size,
            "sgd_minibatch_size": self.batch_size // 4,
            "num_sgd_iter": self.num_epochs,
            "clip_param": self.clip_param,
            "vf_loss_coeff": self.value_loss_coeff,
            "entropy_coeff": self.entropy_coeff,

            # 禁用新API栈以支持自定义模型
            "_enable_rl_module_api": False,
            "_enable_learner_api": False,

            # 多智能体配置
            "multiagent": {
                "policies_to_train": ["default_policy"],
                "policy_mapping_fn": lambda agent_id, *args, **kwargs: "default_policy",
            },

            # 模型配置
            "model": self.create_model_config(),

            # 注意力机制配置
            "custom_config": {
                "attention_loss_coeff": self.attention_loss_coeff,
                "attention_regularization": self.attention_regularization
            }
        }
    
    def compute_attention_loss(self, attention_weights: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        计算注意力正则化损失
        
        Args:
            attention_weights: 注意力权重字典
            
        Returns:
            attention_loss: 注意力损失
        """
        total_loss = 0.0
        
        # 任务注意力正则化：鼓励注意力分布的多样性
        if 'task_attention' in attention_weights:
            task_attention = attention_weights['task_attention']
            # 计算注意力熵，鼓励多样性
            task_entropy = -torch.sum(task_attention * torch.log(task_attention + 1e-8), dim=-1)
            task_loss = -task_entropy.mean()  # 负熵作为损失，鼓励高熵（多样性）
            total_loss += task_loss
        
        # 协作注意力正则化：鼓励稀疏但有意义的协作
        if 'collaboration_attention' in attention_weights:
            collab_attention = attention_weights['collaboration_attention']
            # L1正则化，鼓励稀疏性
            collab_l1 = torch.mean(torch.abs(collab_attention))
            total_loss += 0.1 * collab_l1
        
        return total_loss
