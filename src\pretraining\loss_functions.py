"""
注意力机制预训练损失函数
定义用于预训练双层注意力机制的各种损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
from dataclasses import dataclass

from .data_generator import PretrainingBatch


@dataclass
class PretrainingLossOutput:
    """预训练损失输出"""
    total_loss: torch.Tensor
    task_allocation_loss: torch.Tensor
    collaboration_loss: torch.Tensor
    attention_consistency_loss: torch.Tensor
    sparsity_loss: torch.Tensor
    
    # 详细损失组件
    task_allocation_mse: torch.Tensor
    task_allocation_ce: torch.Tensor
    collaboration_mse: torch.Tensor
    collaboration_contrastive: torch.Tensor
    
    # 统计信息
    task_allocation_accuracy: torch.Tensor
    collaboration_accuracy: torch.Tensor
    attention_entropy: torch.Tensor


class AttentionPretrainingLoss(nn.Module):
    """注意力机制预训练损失函数"""
    
    def __init__(self, 
                 task_allocation_weight: float = 1.0,
                 collaboration_weight: float = 1.0,
                 consistency_weight: float = 0.5,
                 sparsity_weight: float = 0.1,
                 temperature: float = 0.1):
        """
        初始化预训练损失函数
        
        Args:
            task_allocation_weight: 任务分配损失权重
            collaboration_weight: 协作损失权重
            consistency_weight: 一致性损失权重
            sparsity_weight: 稀疏性损失权重
            temperature: 对比学习温度参数
        """
        super().__init__()
        
        self.task_allocation_weight = task_allocation_weight
        self.collaboration_weight = collaboration_weight
        self.consistency_weight = consistency_weight
        self.sparsity_weight = sparsity_weight
        self.temperature = temperature
        
        # 损失函数
        self.mse_loss = nn.MSELoss()
        self.ce_loss = nn.CrossEntropyLoss()
        self.bce_loss = nn.BCEWithLogitsLoss()
    
    def forward(self, 
                predictions: Dict[str, torch.Tensor],
                batch: PretrainingBatch) -> PretrainingLossOutput:
        """
        计算预训练损失
        
        Args:
            predictions: 模型预测结果
            batch: 预训练批次数据
            
        Returns:
            loss_output: 损失输出对象
        """
        # 提取预测结果
        task_attention_weights = predictions['task_attention_weights']  # [B, N_agv, N_task]
        collaboration_weights = predictions['collaboration_weights']    # [B, N_agv, N_agv]
        
        # 计算任务分配损失
        task_allocation_loss, task_mse, task_ce, task_acc = self._compute_task_allocation_loss(
            task_attention_weights, batch.optimal_task_allocation
        )
        
        # 计算协作损失
        collaboration_loss, collab_mse, collab_contrastive, collab_acc = self._compute_collaboration_loss(
            collaboration_weights, batch.collaboration_targets
        )
        
        # 计算注意力一致性损失
        consistency_loss = self._compute_attention_consistency_loss(
            task_attention_weights, collaboration_weights
        )
        
        # 计算稀疏性损失
        sparsity_loss = self._compute_sparsity_loss(
            task_attention_weights, collaboration_weights
        )
        
        # 计算注意力熵
        attention_entropy = self._compute_attention_entropy(
            task_attention_weights, collaboration_weights
        )
        
        # 总损失
        total_loss = (
            self.task_allocation_weight * task_allocation_loss +
            self.collaboration_weight * collaboration_loss +
            self.consistency_weight * consistency_loss +
            self.sparsity_weight * sparsity_loss
        )
        
        return PretrainingLossOutput(
            total_loss=total_loss,
            task_allocation_loss=task_allocation_loss,
            collaboration_loss=collaboration_loss,
            attention_consistency_loss=consistency_loss,
            sparsity_loss=sparsity_loss,
            task_allocation_mse=task_mse,
            task_allocation_ce=task_ce,
            collaboration_mse=collab_mse,
            collaboration_contrastive=collab_contrastive,
            task_allocation_accuracy=task_acc,
            collaboration_accuracy=collab_acc,
            attention_entropy=attention_entropy
        )
    
    def _compute_task_allocation_loss(self, 
                                    pred_weights: torch.Tensor,
                                    target_allocation: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        """
        计算任务分配损失
        
        Args:
            pred_weights: 预测的任务注意力权重 [B, N_agv, N_task]
            target_allocation: 目标任务分配 [B, N_agv, N_task]
            
        Returns:
            total_loss, mse_loss, ce_loss, accuracy
        """
        # MSE损失：直接回归注意力权重
        mse_loss = self.mse_loss(pred_weights, target_allocation)
        
        # 交叉熵损失：将任务分配视为分类问题
        # 对每个AGV，选择最佳任务
        batch_size, num_agvs, num_tasks = pred_weights.shape
        
        # 重塑为分类格式
        pred_logits = pred_weights.view(-1, num_tasks)  # [B*N_agv, N_task]
        target_labels = target_allocation.argmax(dim=-1).view(-1)  # [B*N_agv]
        
        # 只对有分配的AGV计算交叉熵损失
        has_allocation = target_allocation.sum(dim=-1).view(-1) > 0  # [B*N_agv]
        
        if has_allocation.sum() > 0:
            ce_loss = self.ce_loss(pred_logits[has_allocation], target_labels[has_allocation])
        else:
            ce_loss = torch.tensor(0.0, device=pred_weights.device)
        
        # 计算准确率
        pred_allocation = (pred_weights == pred_weights.max(dim=-1, keepdim=True)[0]).float()
        accuracy = (pred_allocation * target_allocation).sum() / (target_allocation.sum() + 1e-8)
        
        # 总任务分配损失
        total_loss = 0.7 * mse_loss + 0.3 * ce_loss
        
        return total_loss, mse_loss, ce_loss, accuracy
    
    def _compute_collaboration_loss(self, 
                                  pred_weights: torch.Tensor,
                                  target_collaboration: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        """
        计算协作损失
        
        Args:
            pred_weights: 预测的协作注意力权重 [B, N_agv, N_agv]
            target_collaboration: 目标协作强度 [B, N_agv, N_agv]
            
        Returns:
            total_loss, mse_loss, contrastive_loss, accuracy
        """
        # MSE损失：直接回归协作强度
        mse_loss = self.mse_loss(pred_weights, target_collaboration)
        
        # 对比学习损失：学习协作关系的相对强度
        contrastive_loss = self._compute_contrastive_loss(pred_weights, target_collaboration)
        
        # 计算准确率（基于阈值的二分类）
        threshold = 0.3
        pred_binary = (pred_weights > threshold).float()
        target_binary = (target_collaboration > threshold).float()
        
        correct = (pred_binary == target_binary).float()
        accuracy = correct.mean()
        
        # 总协作损失
        total_loss = 0.6 * mse_loss + 0.4 * contrastive_loss
        
        return total_loss, mse_loss, contrastive_loss, accuracy
    
    def _compute_contrastive_loss(self, 
                                pred_weights: torch.Tensor,
                                target_weights: torch.Tensor) -> torch.Tensor:
        """
        计算对比学习损失
        
        Args:
            pred_weights: 预测权重 [B, N_agv, N_agv]
            target_weights: 目标权重 [B, N_agv, N_agv]
            
        Returns:
            contrastive_loss: 对比损失
        """
        batch_size, num_agvs, _ = pred_weights.shape
        
        # 计算相似度矩阵
        pred_sim = F.cosine_similarity(
            pred_weights.unsqueeze(2), 
            pred_weights.unsqueeze(1), 
            dim=-1
        ) / self.temperature
        
        target_sim = F.cosine_similarity(
            target_weights.unsqueeze(2),
            target_weights.unsqueeze(1),
            dim=-1
        ) / self.temperature
        
        # 对比损失
        contrastive_loss = F.mse_loss(pred_sim, target_sim)
        
        return contrastive_loss
    
    def _compute_attention_consistency_loss(self, 
                                          task_attention: torch.Tensor,
                                          collaboration_attention: torch.Tensor) -> torch.Tensor:
        """
        计算注意力一致性损失
        
        Args:
            task_attention: 任务注意力权重 [B, N_agv, N_task]
            collaboration_attention: 协作注意力权重 [B, N_agv, N_agv]
            
        Returns:
            consistency_loss: 一致性损失
        """
        # 计算任务注意力的集中度
        task_concentration = torch.sum(task_attention ** 2, dim=-1)  # [B, N_agv]
        
        # 计算协作注意力的集中度
        collab_concentration = torch.sum(collaboration_attention ** 2, dim=-1)  # [B, N_agv]
        
        # 一致性约束：高任务集中度应对应低协作集中度（专注于任务时减少协作）
        consistency_loss = F.mse_loss(
            task_concentration + collab_concentration,
            torch.ones_like(task_concentration)
        )
        
        return consistency_loss
    
    def _compute_sparsity_loss(self, 
                             task_attention: torch.Tensor,
                             collaboration_attention: torch.Tensor) -> torch.Tensor:
        """
        计算稀疏性损失
        
        Args:
            task_attention: 任务注意力权重 [B, N_agv, N_task]
            collaboration_attention: 协作注意力权重 [B, N_agv, N_agv]
            
        Returns:
            sparsity_loss: 稀疏性损失
        """
        # L1正则化鼓励稀疏性
        task_l1 = torch.mean(torch.abs(task_attention))
        collab_l1 = torch.mean(torch.abs(collaboration_attention))
        
        # 熵正则化鼓励集中的注意力分布
        task_entropy = -torch.sum(task_attention * torch.log(task_attention + 1e-8), dim=-1).mean()
        collab_entropy = -torch.sum(collaboration_attention * torch.log(collaboration_attention + 1e-8), dim=-1).mean()
        
        # 组合稀疏性损失
        sparsity_loss = 0.3 * (task_l1 + collab_l1) + 0.7 * (task_entropy + collab_entropy)
        
        return sparsity_loss
    
    def _compute_attention_entropy(self, 
                                 task_attention: torch.Tensor,
                                 collaboration_attention: torch.Tensor) -> torch.Tensor:
        """
        计算注意力熵（用于监控）
        
        Args:
            task_attention: 任务注意力权重
            collaboration_attention: 协作注意力权重
            
        Returns:
            average_entropy: 平均注意力熵
        """
        # 计算任务注意力熵
        task_entropy = -torch.sum(task_attention * torch.log(task_attention + 1e-8), dim=-1)
        
        # 计算协作注意力熵
        collab_entropy = -torch.sum(collaboration_attention * torch.log(collaboration_attention + 1e-8), dim=-1)
        
        # 平均熵
        average_entropy = (task_entropy.mean() + collab_entropy.mean()) / 2
        
        return average_entropy


class CurriculumLossScheduler:
    """课程学习损失调度器"""
    
    def __init__(self, 
                 initial_weights: Dict[str, float],
                 final_weights: Dict[str, float],
                 transition_steps: int):
        """
        初始化课程学习调度器
        
        Args:
            initial_weights: 初始损失权重
            final_weights: 最终损失权重
            transition_steps: 过渡步数
        """
        self.initial_weights = initial_weights
        self.final_weights = final_weights
        self.transition_steps = transition_steps
        self.current_step = 0
    
    def step(self) -> Dict[str, float]:
        """
        更新损失权重
        
        Returns:
            current_weights: 当前损失权重
        """
        progress = min(self.current_step / self.transition_steps, 1.0)
        
        current_weights = {}
        for key in self.initial_weights:
            initial = self.initial_weights[key]
            final = self.final_weights[key]
            current_weights[key] = initial + (final - initial) * progress
        
        self.current_step += 1
        return current_weights
    
    def reset(self):
        """重置调度器"""
        self.current_step = 0
