"""
超参数评估器
用于评估不同超参数配置的性能
"""

import os
import time
import numpy as np
import torch
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

from ..mappo.agv_env_adapter import AGVMARLlibEnv
from ..mappo.attention_enhanced_mappo import AttentionEnhancedMAPPOModel
from ..utils.logger import Logger
from ..utils.metrics import MetricsCalculator
from config.mappo_config import MAPPOConfig


@dataclass
class EvaluationMetrics:
    """评估指标"""
    avg_reward: float
    success_rate: float
    episode_length: float
    convergence_speed: float
    training_stability: float
    computational_efficiency: float
    attention_quality: float
    overall_score: float


class HyperparameterEvaluator:
    """超参数评估器"""
    
    def __init__(self, 
                 base_config: MAPPOConfig,
                 evaluation_episodes: int = 20,
                 max_training_steps: int = 1000,
                 device: str = "cpu"):
        """
        初始化超参数评估器
        
        Args:
            base_config: 基础MAPPO配置
            evaluation_episodes: 评估回合数
            max_training_steps: 最大训练步数
            device: 计算设备
        """
        self.base_config = base_config
        self.evaluation_episodes = evaluation_episodes
        self.max_training_steps = max_training_steps
        self.device = device
        
        self.logger = Logger("HyperparameterEvaluator")
        self.metrics_calculator = MetricsCalculator()
        
        # 评估权重
        self.metric_weights = {
            'avg_reward': 0.25,
            'success_rate': 0.20,
            'episode_length': 0.10,
            'convergence_speed': 0.15,
            'training_stability': 0.10,
            'computational_efficiency': 0.10,
            'attention_quality': 0.10
        }
        
        self.logger.info("超参数评估器初始化完成")
    
    def evaluate_hyperparameters(self, hyperparams: Dict[str, Any]) -> EvaluationMetrics:
        """
        评估超参数配置
        
        Args:
            hyperparams: 超参数字典
            
        Returns:
            evaluation_metrics: 评估指标
        """
        self.logger.info(f"开始评估超参数配置: {hyperparams}")
        
        try:
            # 创建配置
            config = self._create_config_from_hyperparams(hyperparams)
            
            # 创建环境
            env = self._create_environment(config)
            
            # 创建模型
            model = self._create_model(config, env)
            
            # 快速训练
            training_metrics = self._quick_training(model, env, config)
            
            # 评估性能
            performance_metrics = self._evaluate_performance(model, env)
            
            # 计算注意力质量
            attention_metrics = self._evaluate_attention_quality(model, env)
            
            # 计算计算效率
            efficiency_metrics = self._evaluate_computational_efficiency(model, env)
            
            # 综合评估指标
            evaluation_metrics = self._compute_overall_metrics(
                training_metrics, performance_metrics, 
                attention_metrics, efficiency_metrics
            )
            
            env.close()
            
            self.logger.info(f"超参数评估完成，总分: {evaluation_metrics.overall_score:.4f}")
            
            return evaluation_metrics
            
        except Exception as e:
            self.logger.error(f"超参数评估失败: {e}")
            # 返回最低分数
            return EvaluationMetrics(
                avg_reward=0.0,
                success_rate=0.0,
                episode_length=1000.0,
                convergence_speed=0.0,
                training_stability=0.0,
                computational_efficiency=0.0,
                attention_quality=0.0,
                overall_score=0.0
            )
    
    def _create_config_from_hyperparams(self, hyperparams: Dict[str, Any]) -> MAPPOConfig:
        """从超参数创建配置"""
        config = MAPPOConfig()
        
        # 更新配置参数
        if 'learning_rate' in hyperparams:
            config.learning_rate = hyperparams['learning_rate']
        if 'batch_size' in hyperparams:
            config.batch_size = hyperparams['batch_size']
        if 'num_epochs' in hyperparams:
            config.num_epochs = hyperparams['num_epochs']
        if 'clip_param' in hyperparams:
            config.clip_param = hyperparams['clip_param']
        if 'entropy_coeff' in hyperparams:
            config.entropy_coeff = hyperparams['entropy_coeff']
        if 'value_loss_coeff' in hyperparams:
            config.value_loss_coeff = hyperparams['value_loss_coeff']
        if 'hidden_dim' in hyperparams:
            config.hidden_dim = hyperparams['hidden_dim']
        if 'attention_feature_dim' in hyperparams:
            config.feature_dim = hyperparams['attention_feature_dim']
        if 'attention_num_heads' in hyperparams:
            config.num_heads = hyperparams['attention_num_heads']
        if 'attention_dropout' in hyperparams:
            config.dropout = hyperparams['attention_dropout']
        
        return config
    
    def _create_environment(self, config: MAPPOConfig) -> AGVMARLlibEnv:
        """创建环境"""
        env_config = {
            "num_agvs": 3,
            "num_tasks": 6,
            "max_episode_steps": 200
        }
        
        return AGVMARLlibEnv(env_config)
    
    def _create_model(self, config: MAPPOConfig, env: AGVMARLlibEnv) -> AttentionEnhancedMAPPOModel:
        """创建模型"""
        model_config = {
            "custom_model_config": {
                "hidden_dim": config.hidden_dim,
                "feature_dim": config.feature_dim,
                "num_heads": config.num_heads,
                "dropout": config.dropout
            }
        }
        
        model = AttentionEnhancedMAPPOModel(
            obs_space=env.observation_space,
            action_space=env.action_space,
            num_outputs=env.action_space.n,
            model_config=model_config,
            name="eval_model"
        )
        
        return model
    
    def _quick_training(self, model, env, config: MAPPOConfig) -> Dict[str, float]:
        """快速训练评估"""
        training_rewards = []
        training_losses = []
        
        # 简化的训练循环
        for step in range(min(self.max_training_steps, 100)):  # 限制训练步数
            # 模拟训练步骤
            obs = env.reset()
            episode_reward = 0
            done = False
            step_count = 0
            
            while not done and step_count < 50:  # 限制每回合步数
                # 随机动作（模拟策略输出）
                actions = {agent: env.action_space.sample() for agent in env.agents}
                
                obs, rewards, dones, infos = env.step(actions)
                episode_reward += sum(rewards.values())
                step_count += 1
                done = dones.get("__all__", False)
            
            training_rewards.append(episode_reward)
            
            # 模拟损失（实际训练中会有真实损失）
            simulated_loss = max(0, 1.0 - step * 0.01 + np.random.normal(0, 0.1))
            training_losses.append(simulated_loss)
        
        # 计算训练指标
        convergence_speed = self._calculate_convergence_speed(training_rewards)
        training_stability = self._calculate_training_stability(training_losses)
        
        return {
            'convergence_speed': convergence_speed,
            'training_stability': training_stability,
            'final_reward': np.mean(training_rewards[-10:]) if training_rewards else 0.0
        }
    
    def _evaluate_performance(self, model, env) -> Dict[str, float]:
        """评估性能指标"""
        episode_rewards = []
        episode_lengths = []
        success_count = 0
        
        for episode in range(min(self.evaluation_episodes, 10)):  # 限制评估回合数
            obs = env.reset()
            episode_reward = 0
            episode_length = 0
            done = False
            
            while not done and episode_length < 100:  # 限制回合长度
                # 使用模型预测动作（简化版本）
                actions = {agent: env.action_space.sample() for agent in env.agents}
                
                obs, rewards, dones, infos = env.step(actions)
                episode_reward += sum(rewards.values())
                episode_length += 1
                done = dones.get("__all__", False)
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            
            # 简单的成功判断
            if episode_reward > 0:
                success_count += 1
        
        return {
            'avg_reward': np.mean(episode_rewards) if episode_rewards else 0.0,
            'success_rate': success_count / len(episode_rewards) if episode_rewards else 0.0,
            'avg_episode_length': np.mean(episode_lengths) if episode_lengths else 100.0
        }
    
    def _evaluate_attention_quality(self, model, env) -> Dict[str, float]:
        """评估注意力质量"""
        # 简化的注意力质量评估
        try:
            # 获取注意力权重
            obs = env.reset()
            obs_tensor = torch.tensor(list(obs.values())[0]).unsqueeze(0)
            input_dict = {"obs": obs_tensor}
            
            # 前向传播
            logits, state = model.forward(input_dict, [], None)
            attention_weights = model.get_attention_weights()
            
            # 计算注意力质量指标
            attention_entropy = 0.0
            attention_sparsity = 0.0
            
            if attention_weights:
                for key, weights in attention_weights.items():
                    if weights is not None:
                        # 计算熵（多样性）
                        entropy = -torch.sum(weights * torch.log(weights + 1e-8))
                        attention_entropy += entropy.item()
                        
                        # 计算稀疏性
                        sparsity = (weights < 0.1).float().mean()
                        attention_sparsity += sparsity.item()
            
            attention_quality = min(1.0, (attention_entropy + attention_sparsity) / 2)
            
        except Exception as e:
            self.logger.warning(f"注意力质量评估失败: {e}")
            attention_quality = 0.5  # 默认中等质量
        
        return {'attention_quality': attention_quality}
    
    def _evaluate_computational_efficiency(self, model, env) -> Dict[str, float]:
        """评估计算效率"""
        # 测试前向传播时间
        obs = env.reset()
        obs_tensor = torch.tensor(list(obs.values())[0]).unsqueeze(0)
        input_dict = {"obs": obs_tensor}
        
        # 预热
        for _ in range(10):
            logits, state = model.forward(input_dict, [], None)
            value = model.value_function()
        
        # 测试时间
        start_time = time.time()
        for _ in range(100):
            logits, state = model.forward(input_dict, [], None)
            value = model.value_function()
        forward_time = (time.time() - start_time) / 100
        
        # 计算效率分数（时间越短分数越高）
        efficiency_score = min(1.0, 0.01 / (forward_time + 1e-6))
        
        return {'computational_efficiency': efficiency_score}
    
    def _compute_overall_metrics(self, 
                                training_metrics: Dict[str, float],
                                performance_metrics: Dict[str, float],
                                attention_metrics: Dict[str, float],
                                efficiency_metrics: Dict[str, float]) -> EvaluationMetrics:
        """计算综合评估指标"""
        
        # 归一化指标
        avg_reward = max(0, min(1, performance_metrics['avg_reward'] / 100.0))  # 假设最大奖励100
        success_rate = performance_metrics['success_rate']
        episode_length = max(0, min(1, 1.0 - performance_metrics['avg_episode_length'] / 200.0))  # 步数越少越好
        convergence_speed = training_metrics['convergence_speed']
        training_stability = training_metrics['training_stability']
        computational_efficiency = efficiency_metrics['computational_efficiency']
        attention_quality = attention_metrics['attention_quality']
        
        # 计算加权总分
        overall_score = (
            self.metric_weights['avg_reward'] * avg_reward +
            self.metric_weights['success_rate'] * success_rate +
            self.metric_weights['episode_length'] * episode_length +
            self.metric_weights['convergence_speed'] * convergence_speed +
            self.metric_weights['training_stability'] * training_stability +
            self.metric_weights['computational_efficiency'] * computational_efficiency +
            self.metric_weights['attention_quality'] * attention_quality
        )
        
        return EvaluationMetrics(
            avg_reward=performance_metrics['avg_reward'],
            success_rate=success_rate,
            episode_length=performance_metrics['avg_episode_length'],
            convergence_speed=convergence_speed,
            training_stability=training_stability,
            computational_efficiency=computational_efficiency,
            attention_quality=attention_quality,
            overall_score=overall_score
        )
    
    def _calculate_convergence_speed(self, rewards: List[float]) -> float:
        """计算收敛速度"""
        if len(rewards) < 10:
            return 0.0
        
        # 计算奖励改进的速度
        early_avg = np.mean(rewards[:len(rewards)//3])
        late_avg = np.mean(rewards[-len(rewards)//3:])
        
        improvement = max(0, late_avg - early_avg)
        convergence_speed = min(1.0, improvement / 50.0)  # 归一化
        
        return convergence_speed
    
    def _calculate_training_stability(self, losses: List[float]) -> float:
        """计算训练稳定性"""
        if len(losses) < 5:
            return 0.0
        
        # 计算损失的变异系数
        loss_std = np.std(losses)
        loss_mean = np.mean(losses)
        
        if loss_mean == 0:
            return 1.0
        
        cv = loss_std / loss_mean
        stability = max(0, min(1, 1.0 - cv))  # 变异系数越小越稳定
        
        return stability
