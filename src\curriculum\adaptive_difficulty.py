"""
自适应难度调整系统
根据学习进度动态调整课程难度，确保最优的学习效率
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from collections import deque
import logging


@dataclass
class DifficultyMetrics:
    """难度评估指标"""
    success_rate: float
    average_reward: float
    learning_speed: float
    stability: float
    exploration_ratio: float


@dataclass
class DifficultyAdjustment:
    """难度调整建议"""
    action: str  # 'increase', 'decrease', 'maintain'
    magnitude: float  # 0.0-1.0
    reason: str
    confidence: float  # 0.0-1.0


class AdaptiveDifficultyController:
    """
    自适应难度控制器
    根据学习表现动态调整训练难度
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化自适应难度控制器
        
        Args:
            config: 配置参数
        """
        self.config = config
        self.logger = logging.getLogger('AdaptiveDifficulty')
        
        # 难度调整参数
        self.min_success_rate = config.get('min_success_rate', 0.3)
        self.max_success_rate = config.get('max_success_rate', 0.9)
        self.target_success_rate = config.get('target_success_rate', 0.7)
        self.adjustment_threshold = config.get('adjustment_threshold', 0.1)
        self.stability_window = config.get('stability_window', 20)
        
        # 性能历史
        self.performance_history = deque(maxlen=100)
        self.success_history = deque(maxlen=50)
        self.reward_history = deque(maxlen=50)
        
        # 难度状态
        self.current_difficulty = 0.5  # 0.0-1.0
        self.difficulty_history = deque(maxlen=100)
        self.adjustment_history = []
        
        self.logger.info("自适应难度控制器初始化完成")
    
    def update_performance(self, episode_results: List[Dict[str, Any]]):
        """
        更新性能数据
        
        Args:
            episode_results: 回合结果列表
        """
        for result in episode_results:
            # 提取关键指标
            success = result.get('success', False)
            reward = result.get('total_reward', 0)
            length = result.get('episode_length', 0)
            
            # 更新历史
            self.success_history.append(1.0 if success else 0.0)
            self.reward_history.append(reward)
            
            # 计算性能指标
            performance_data = {
                'success': success,
                'reward': reward,
                'length': length,
                'timestamp': result.get('timestamp', 0)
            }
            self.performance_history.append(performance_data)
    
    def calculate_difficulty_metrics(self) -> DifficultyMetrics:
        """
        计算当前难度评估指标
        
        Returns:
            metrics: 难度评估指标
        """
        if len(self.performance_history) < 5:
            return DifficultyMetrics(0.5, 0.0, 0.0, 0.0, 0.5)
        
        # 成功率
        recent_successes = list(self.success_history)[-20:] if len(self.success_history) >= 20 else list(self.success_history)
        success_rate = np.mean(recent_successes) if recent_successes else 0.0
        
        # 平均奖励
        recent_rewards = list(self.reward_history)[-20:] if len(self.reward_history) >= 20 else list(self.reward_history)
        average_reward = np.mean(recent_rewards) if recent_rewards else 0.0
        
        # 学习速度（奖励增长率）
        learning_speed = self._calculate_learning_speed()
        
        # 稳定性（奖励方差）
        stability = self._calculate_stability()
        
        # 探索比例（估算）
        exploration_ratio = self._estimate_exploration_ratio()
        
        return DifficultyMetrics(
            success_rate=success_rate,
            average_reward=average_reward,
            learning_speed=learning_speed,
            stability=stability,
            exploration_ratio=exploration_ratio
        )
    
    def _calculate_learning_speed(self) -> float:
        """计算学习速度"""
        if len(self.reward_history) < 10:
            return 0.0
        
        rewards = list(self.reward_history)
        
        # 计算前半段和后半段的平均奖励
        mid_point = len(rewards) // 2
        early_avg = np.mean(rewards[:mid_point])
        late_avg = np.mean(rewards[mid_point:])
        
        # 学习速度 = (后期平均 - 前期平均) / 前期平均
        if early_avg > 0:
            learning_speed = (late_avg - early_avg) / early_avg
        else:
            learning_speed = 0.0
        
        # 归一化到 [-1, 1]
        return np.clip(learning_speed, -1.0, 1.0)
    
    def _calculate_stability(self) -> float:
        """计算性能稳定性"""
        if len(self.reward_history) < 5:
            return 0.0
        
        rewards = list(self.reward_history)
        
        # 计算变异系数（标准差/均值）
        mean_reward = np.mean(rewards)
        std_reward = np.std(rewards)
        
        if mean_reward > 0:
            cv = std_reward / mean_reward
            # 稳定性 = 1 - 归一化的变异系数
            stability = 1.0 - np.clip(cv / 2.0, 0.0, 1.0)
        else:
            stability = 0.0
        
        return stability
    
    def _estimate_exploration_ratio(self) -> float:
        """估算探索比例"""
        # 简化实现：基于成功率变化估算探索程度
        if len(self.success_history) < 10:
            return 0.5
        
        recent_successes = list(self.success_history)[-10:]
        success_variance = np.var(recent_successes)
        
        # 高方差表示更多探索
        exploration_ratio = np.clip(success_variance * 4, 0.0, 1.0)
        
        return exploration_ratio
    
    def suggest_difficulty_adjustment(self) -> DifficultyAdjustment:
        """
        建议难度调整
        
        Returns:
            adjustment: 难度调整建议
        """
        metrics = self.calculate_difficulty_metrics()
        
        # 基于成功率的主要调整逻辑
        if metrics.success_rate < self.min_success_rate:
            # 成功率过低，降低难度
            magnitude = (self.min_success_rate - metrics.success_rate) / self.min_success_rate
            return DifficultyAdjustment(
                action='decrease',
                magnitude=np.clip(magnitude, 0.1, 0.5),
                reason=f"成功率过低 ({metrics.success_rate:.2f} < {self.min_success_rate})",
                confidence=0.8
            )
        
        elif metrics.success_rate > self.max_success_rate:
            # 成功率过高，增加难度
            magnitude = (metrics.success_rate - self.max_success_rate) / (1.0 - self.max_success_rate)
            return DifficultyAdjustment(
                action='increase',
                magnitude=np.clip(magnitude, 0.1, 0.3),
                reason=f"成功率过高 ({metrics.success_rate:.2f} > {self.max_success_rate})",
                confidence=0.7
            )
        
        # 基于学习速度的辅助调整
        elif metrics.learning_speed < -0.2 and metrics.success_rate < self.target_success_rate:
            # 学习停滞且成功率不理想，降低难度
            return DifficultyAdjustment(
                action='decrease',
                magnitude=0.2,
                reason=f"学习停滞 (速度={metrics.learning_speed:.2f})",
                confidence=0.6
            )
        
        elif metrics.learning_speed > 0.3 and metrics.success_rate > self.target_success_rate:
            # 学习快速且成功率良好，可以增加难度
            return DifficultyAdjustment(
                action='increase',
                magnitude=0.15,
                reason=f"学习快速 (速度={metrics.learning_speed:.2f})",
                confidence=0.5
            )
        
        # 基于稳定性的调整
        elif metrics.stability < 0.3:
            # 性能不稳定，降低难度
            return DifficultyAdjustment(
                action='decrease',
                magnitude=0.1,
                reason=f"性能不稳定 (稳定性={metrics.stability:.2f})",
                confidence=0.4
            )
        
        else:
            # 保持当前难度
            return DifficultyAdjustment(
                action='maintain',
                magnitude=0.0,
                reason="性能指标在合理范围内",
                confidence=0.7
            )
    
    def apply_difficulty_adjustment(self, adjustment: DifficultyAdjustment) -> Dict[str, Any]:
        """
        应用难度调整
        
        Args:
            adjustment: 难度调整建议
            
        Returns:
            adjustment_result: 调整结果
        """
        old_difficulty = self.current_difficulty
        
        if adjustment.action == 'increase':
            self.current_difficulty = min(1.0, self.current_difficulty + adjustment.magnitude)
        elif adjustment.action == 'decrease':
            self.current_difficulty = max(0.0, self.current_difficulty - adjustment.magnitude)
        # 'maintain' 不改变难度
        
        # 记录调整历史
        adjustment_record = {
            'timestamp': len(self.difficulty_history),
            'old_difficulty': old_difficulty,
            'new_difficulty': self.current_difficulty,
            'action': adjustment.action,
            'magnitude': adjustment.magnitude,
            'reason': adjustment.reason,
            'confidence': adjustment.confidence
        }
        
        self.adjustment_history.append(adjustment_record)
        self.difficulty_history.append(self.current_difficulty)
        
        self.logger.info(f"难度调整: {adjustment.action} ({old_difficulty:.3f} -> {self.current_difficulty:.3f})")
        self.logger.info(f"调整原因: {adjustment.reason}")
        
        return adjustment_record
    
    def get_difficulty_config(self) -> Dict[str, Any]:
        """
        获取当前难度对应的环境配置
        
        Returns:
            config: 环境配置调整
        """
        difficulty = self.current_difficulty
        
        # 根据难度调整环境参数
        config_adjustments = {
            # AGV数量调整（难度越高，AGV越多）
            'num_agvs_multiplier': 0.8 + 0.4 * difficulty,
            
            # 任务数量调整（难度越高，任务越多）
            'num_tasks_multiplier': 0.7 + 0.6 * difficulty,
            
            # 地图复杂度调整
            'map_complexity': 0.3 + 0.7 * difficulty,
            
            # 任务复杂度调整
            'task_complexity': 0.2 + 0.6 * difficulty,
            
            # 协作要求调整
            'collaboration_requirement': 0.1 + 0.8 * difficulty,
            
            # 时间限制调整（难度越高，时间越紧）
            'time_pressure': 0.5 + 0.5 * difficulty,
            
            # 奖励缩放（难度越高，奖励要求越高）
            'reward_scale': 0.8 + 0.4 * difficulty
        }
        
        return config_adjustments
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        metrics = self.calculate_difficulty_metrics()
        
        return {
            'current_difficulty': self.current_difficulty,
            'metrics': {
                'success_rate': metrics.success_rate,
                'average_reward': metrics.average_reward,
                'learning_speed': metrics.learning_speed,
                'stability': metrics.stability,
                'exploration_ratio': metrics.exploration_ratio
            },
            'history_length': len(self.performance_history),
            'adjustments_made': len(self.adjustment_history),
            'recent_adjustments': self.adjustment_history[-5:] if self.adjustment_history else []
        }
    
    def reset(self):
        """重置控制器状态"""
        self.performance_history.clear()
        self.success_history.clear()
        self.reward_history.clear()
        self.difficulty_history.clear()
        self.adjustment_history.clear()
        self.current_difficulty = 0.5
        
        self.logger.info("自适应难度控制器已重置")
