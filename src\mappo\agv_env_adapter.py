"""
AGV环境适配器
将自定义AGV环境适配为MARLlib兼容的多智能体环境
"""

import gymnasium as gym
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from gymnasium import spaces
import copy

from .simple_agv_env import SimpleAGVEnvironment, SimpleAGVEntity, SimpleTask
import torch


class AGVMARLlibEnv(gym.Env):
    """
    AGV环境的MARLlib适配器
    实现多智能体环境接口，支持MARLlib框架
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化AGV MARLlib环境
        
        Args:
            config: 环境配置字典
        """
        super().__init__()
        
        # 解析配置
        if config is None:
            config = {}

        self.map_name = config.get('map_name', 'default')
        self.num_agvs = config.get('num_agvs', 3)
        self.num_tasks = config.get('num_tasks', 5)
        self.max_episode_steps = config.get('max_episode_steps', 200)

        # 创建简化的核心环境
        env_config = {
            'num_agvs': self.num_agvs,
            'num_tasks': self.num_tasks,
            'max_episode_steps': self.max_episode_steps
        }
        self.agv_env = SimpleAGVEnvironment(env_config)
        
        # 设置智能体信息
        self.agents = [f"agv_{i}" for i in range(self.num_agvs)]
        self.num_agents = len(self.agents)
        
        # 定义观察和动作空间
        self._setup_spaces()
        
        # 环境状态
        self.current_step = 0
        self.total_episode_reward = 0.0
        self.total_episode_steps = 0

        # AGV性能统计
        self.episode_stats = {
            'completed_tasks': 0,
            'total_tasks': 0,
            'collision_count': 0,
            'total_load_capacity': 0,
            'used_load_capacity': 0,
            'path_lengths': [],
            'episode_start_time': 0
        }

        # MARLlib兼容性属性
        self.agent_ids = self.agents
        
    def _setup_spaces(self):
        """设置观察和动作空间"""
        # 动作空间：多智能体的动作向量
        self.action_space = spaces.Box(
            low=0,
            high=6,
            shape=(self.num_agvs,),
            dtype=np.int32
        )

        # 观察空间：所有智能体的观察拼接
        # 每个智能体：自身状态(64) + 任务注意力特征(64) + 协作注意力特征(64) + 全局上下文(32) = 224维
        # 总观察维度：224 * num_agvs
        total_obs_dim = 224 * self.num_agvs
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(total_obs_dim,),
            dtype=np.float32
        )

        # 保持兼容性的字典格式（用于某些检查）
        single_obs_dim = 224
        single_action_space = spaces.Discrete(7)
        single_observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(single_obs_dim,),
            dtype=np.float32
        )

        self.action_spaces = {agent: single_action_space for agent in self.agents}
        self.observation_spaces = {agent: single_observation_space for agent in self.agents}
    
    def reset(self, *, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """
        重置环境

        Args:
            seed: 随机种子
            options: 重置选项

        Returns:
            observations: 所有智能体的观察数组
            info: 环境信息字典
        """
        # 重置核心环境
        _, _ = self.agv_env.reset()  # 忽略simple_agv_env的返回值

        # 重置环境状态
        self.current_step = 0
        self.total_episode_reward = 0.0
        self.total_episode_steps = 0

        # 重置AGV性能统计
        self.episode_stats = {
            'completed_tasks': 0,
            'total_tasks': len(self.agv_env.task_manager.tasks) if hasattr(self.agv_env, 'task_manager') else 0,
            'collision_count': 0,
            'total_load_capacity': sum(agv.capacity for agv in self.agv_env.agvs),
            'used_load_capacity': 0,
            'path_lengths': [],
            'episode_start_time': self.current_step
        }

        # 注意：简化版本中暂时跳过注意力管理器重置

        # 获取初始观察
        observations = self._get_observations()

        # 构建信息字典
        info = {
            'episode_step': self.current_step,
            'total_agents': self.num_agents,
            'active_tasks': len(self.agv_env.task_manager.tasks) if hasattr(self.agv_env, 'task_manager') else 0
        }

        return observations, info
    
    def step(self, actions: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """
        执行一步环境交互

        Args:
            actions: 所有智能体的动作数组

        Returns:
            observations: 新的观察数组
            reward: 总奖励
            terminated: 终止标志
            truncated: 截断标志
            info: 额外信息
        """
        # 转换动作格式
        agv_actions = {}
        for i in range(self.num_agvs):
            if i < len(actions):
                agv_actions[i] = self._convert_action(int(actions[i]))
            else:
                agv_actions[i] = 'stay'  # 默认动作

        # 执行环境步骤
        step_result = self.agv_env.step(agv_actions)

        # 更新环境状态
        self.current_step += 1

        # 获取新的观察
        observations = self._get_observations()

        # 计算奖励
        total_reward = self._compute_total_reward(step_result)

        # 检查完成条件
        terminated = self._check_terminated()

        # 检查截断条件（时间限制等）
        truncated = self._check_truncated()

        # 构建信息字典
        info = self._build_info(step_result)

        # 更新累积奖励和步数
        self.total_episode_reward += total_reward
        self.total_episode_steps += 1

        # 更新AGV性能统计
        self._update_agv_stats(step_result)

        return observations, total_reward, terminated, truncated, info
    
    def _convert_action(self, action: int) -> str:
        """
        转换MARLlib动作到AGV环境动作
        
        Args:
            action: MARLlib动作索引
            
        Returns:
            agv_action: AGV环境动作字符串
        """
        action_mapping = {
            0: 'up',
            1: 'down', 
            2: 'left',
            3: 'right',
            4: 'stay',
            5: 'load',
            6: 'unload'
        }
        return action_mapping.get(action, 'stay')
    
    def _get_observations(self) -> np.ndarray:
        """
        获取所有智能体的观察（简化版本）

        Returns:
            observations: 单一的观察数组，包含所有智能体的观察
        """
        all_observations = []

        # 获取环境观察
        env_obs = self.agv_env._get_observation()
        agvs = env_obs['agvs']
        tasks = env_obs['tasks']

        # 为每个AGV构建简化的观察
        for i in range(self.num_agvs):
            if i < len(agvs):
                agv = agvs[i]

                # 自身状态特征 (64维)
                self_state = np.zeros(64, dtype=np.float32)
                self_state[0] = agv.position[0] / 25.0  # 归一化x坐标
                self_state[1] = agv.position[1] / 9.0   # 归一化y坐标
                self_state[2] = agv.current_load / agv.capacity  # 载重比例
                self_state[3] = 1.0 if agv.is_idle() else 0.0  # 是否空闲

                # 任务注意力特征 (64维) - 简化为随机特征
                task_attention_features = np.random.randn(64).astype(np.float32) * 0.1

                # 协作注意力特征 (64维) - 简化为随机特征
                collaboration_features = np.random.randn(64).astype(np.float32) * 0.1

                # 全局上下文 (32维)
                global_context = self._get_global_context()

                # 拼接所有特征
                obs = np.concatenate([
                    self_state,
                    task_attention_features,
                    collaboration_features,
                    global_context
                ], axis=0).astype(np.float32)

                all_observations.append(obs)
            else:
                # 如果AGV数量不足，填充零观察
                all_observations.append(np.zeros(224, dtype=np.float32))

        # 将所有智能体的观察拼接成一个大的观察向量
        return np.concatenate(all_observations, axis=0).astype(np.float32)
    
    def _get_global_context(self) -> np.ndarray:
        """
        获取全局上下文信息（简化版本）

        Returns:
            global_context: 32维全局上下文向量
        """
        agvs = self.agv_env.agvs
        tasks = self.agv_env.task_manager.get_pending_tasks()

        # 计算全局统计信息
        total_agvs = len(agvs)
        total_tasks = len(tasks)
        completed_tasks = self.agv_env.task_manager.get_completed_task_count()

        # AGV状态统计
        idle_agvs = sum(1 for agv in agvs if agv.is_idle())
        loaded_agvs = sum(1 for agv in agvs if agv.current_load > 0)

        # 任务统计
        pending_tasks = len(tasks)

        # 时间信息
        time_progress = self.current_step / self.max_episode_steps

        # 构建32维上下文向量
        context = np.array([
            # 基本统计 (8维)
            total_agvs / 10.0,  # 归一化AGV数量
            total_tasks / 20.0,  # 归一化任务数量
            completed_tasks / 20.0,  # 归一化完成任务数
            pending_tasks / 20.0,  # 归一化待处理任务数
            idle_agvs / total_agvs if total_agvs > 0 else 0,  # 空闲AGV比例
            loaded_agvs / total_agvs if total_agvs > 0 else 0,  # 载重AGV比例
            0.0,  # 简化：已分配任务比例
            time_progress,  # 时间进度

            # 扩展上下文 (24维，填充零)
            *([0.0] * 24)
        ], dtype=np.float32)

        return context
    
    def _compute_rewards(self, step_result: Dict) -> Dict[str, float]:
        """
        计算奖励
        
        Args:
            step_result: 环境步骤结果
            
        Returns:
            rewards: 每个智能体的奖励
        """
        rewards = {}
        
        # 从环境获取基础奖励
        env_rewards = step_result.get('rewards', {})
        
        for i, agent in enumerate(self.agents):
            if i in env_rewards:
                rewards[agent] = float(env_rewards[i])
            else:
                rewards[agent] = 0.0
        
        return rewards
    
    def _check_terminated(self) -> bool:
        """
        检查终止条件（任务完成等）

        Returns:
            terminated: 是否终止
        """
        # 检查任务完成条件
        return self.agv_env.is_episode_complete()

    def _check_truncated(self) -> bool:
        """
        检查截断条件（时间限制等）

        Returns:
            truncated: 是否截断
        """
        # 检查时间限制
        return self.current_step >= self.max_episode_steps

    def _compute_total_reward(self, step_result) -> float:
        """
        计算总奖励

        Args:
            step_result: 环境步骤结果

        Returns:
            total_reward: 总奖励值
        """
        # 简化的奖励计算
        total_reward = 0.0

        # 基础奖励：每步存活奖励
        total_reward += 0.1

        # 任务完成奖励
        if hasattr(step_result, 'completed_tasks'):
            total_reward += len(step_result.completed_tasks) * 10.0

        # 效率奖励：基于AGV利用率
        agvs = self.agv_env.agvs
        busy_agvs = sum(1 for agv in agvs if not agv.is_idle())
        efficiency = busy_agvs / len(agvs) if agvs else 0
        total_reward += efficiency * 2.0

        return total_reward

    def _build_info(self, step_result) -> Dict:
        """
        构建信息字典

        Args:
            step_result: 环境步骤结果

        Returns:
            info: 信息字典
        """
        return {
            'episode_step': self.current_step,
            'total_reward': self.total_episode_reward,
            'completed_tasks': getattr(step_result, 'completed_tasks', 0),
            'active_agvs': len([agv for agv in self.agv_env.agvs if not agv.is_idle()])
        }
    
    def _build_infos(self, step_result: Dict) -> Dict[str, Dict]:
        """
        构建信息字典（简化版本）

        Args:
            step_result: 环境步骤结果

        Returns:
            infos: 信息字典
        """
        infos = {}

        # 为每个智能体构建信息
        for agent in self.agents:
            infos[agent] = {
                'episode_reward': self.episode_rewards[agent],
                'episode_length': self.episode_lengths[agent],
                'current_step': self.current_step,
                'environment_stats': step_result.get('stats', {})
            }

        return infos
    
    def render(self, mode='human'):
        """渲染环境"""
        return self.agv_env.render(mode)
    
    def close(self):
        """关闭环境"""
        self.agv_env.close()
    
    def seed(self, seed=None):
        """设置随机种子"""
        return self.agv_env.seed(seed)
    
    # MARLlib兼容性方法
    def get_agent_ids(self):
        """获取智能体ID列表"""
        return self.agents
    
    def get_env_info(self):
        """获取环境信息"""
        return {
            'state_shape': self.observation_spaces[self.agents[0]].shape[0],
            'obs_shape': self.observation_spaces[self.agents[0]].shape[0],
            'n_actions': self.action_spaces[self.agents[0]].n,
            'n_agents': self.num_agents,
            'episode_limit': self.max_episode_steps
        }

    def _update_agv_stats(self, step_result):
        """
        更新AGV性能统计

        Args:
            step_result: 环境步骤结果
        """
        try:
            # 更新任务完成统计
            if hasattr(step_result, 'completed_tasks'):
                self.episode_stats['completed_tasks'] += len(step_result.completed_tasks)

            # 更新碰撞统计
            if hasattr(step_result, 'collisions'):
                self.episode_stats['collision_count'] += len(step_result.collisions)

            # 更新载重利用率
            current_load = sum(agv.current_load for agv in self.agv_env.agvs)
            self.episode_stats['used_load_capacity'] = max(self.episode_stats['used_load_capacity'], current_load)

            # 更新路径长度（每个AGV的移动距离）
            for agv in self.agv_env.agvs:
                if hasattr(agv, 'path_length'):
                    if len(self.episode_stats['path_lengths']) <= agv.id:
                        self.episode_stats['path_lengths'].extend([0] * (agv.id + 1 - len(self.episode_stats['path_lengths'])))
                    self.episode_stats['path_lengths'][agv.id] = getattr(agv, 'path_length', 0)

        except Exception as e:
            # 静默处理统计错误，不影响训练
            pass

    def get_agv_performance_metrics(self) -> Dict[str, float]:
        """
        获取AGV性能指标

        Returns:
            AGV性能指标字典
        """
        try:
            # 计算任务完成率
            task_completion_rate = 0.0
            if self.episode_stats['total_tasks'] > 0:
                task_completion_rate = self.episode_stats['completed_tasks'] / self.episode_stats['total_tasks']

            # 计算载重利用率
            load_utilization = 0.0
            if self.episode_stats['total_load_capacity'] > 0:
                load_utilization = self.episode_stats['used_load_capacity'] / self.episode_stats['total_load_capacity']

            # 计算平均路径长度
            avg_path_length = 0.0
            if self.episode_stats['path_lengths']:
                avg_path_length = np.mean(self.episode_stats['path_lengths'])

            return {
                'task_completion_rate': min(task_completion_rate, 1.0),  # 限制在0-1之间
                'load_utilization': min(load_utilization, 1.0),  # 限制在0-1之间
                'collision_count': self.episode_stats['collision_count'],
                'avg_path_length': avg_path_length
            }

        except Exception as e:
            # 返回默认值
            return {
                'task_completion_rate': 0.0,
                'load_utilization': 0.0,
                'collision_count': 0,
                'avg_path_length': 0.0
            }
