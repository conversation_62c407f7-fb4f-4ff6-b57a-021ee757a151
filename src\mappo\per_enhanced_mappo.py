"""
优先级经验回放增强的MAPPO算法
集成优先级经验回放机制的MAPPO实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import copy
from collections import defaultdict

from ..memory.prioritized_replay_buffer import MultiAgentPrioritizedReplayManager, MultiAgentExperience
from .attention_enhanced_mappo import AttentionEnhancedMAPPOModel, AttentionEnhancedMAPPOTrainer


class PEREnhancedMAPPOTrainer(AttentionEnhancedMAPPOTrainer):
    """
    优先级经验回放增强的MAPPO训练器
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化PER增强MAPPO训练器
        
        Args:
            config: 训练配置
        """
        super().__init__(config)
        
        # PER相关配置
        self.per_config = config.get('per_config', {})
        self.buffer_capacity = self.per_config.get('buffer_capacity', 100000)
        self.per_alpha = self.per_config.get('alpha', 0.6)
        self.per_beta = self.per_config.get('beta', 0.4)
        self.per_beta_increment = self.per_config.get('beta_increment', 0.001)
        self.per_epsilon = self.per_config.get('epsilon', 1e-6)
        self.attention_priority_weight = self.per_config.get('attention_priority_weight', 0.3)
        
        # 创建优先级经验回放管理器
        self.replay_manager = MultiAgentPrioritizedReplayManager(
            capacity=self.buffer_capacity,
            alpha=self.per_alpha,
            beta=self.per_beta,
            beta_increment=self.per_beta_increment,
            epsilon=self.per_epsilon,
            attention_priority_weight=self.attention_priority_weight
        )
        
        # PER训练参数
        self.per_update_frequency = self.per_config.get('update_frequency', 4)
        self.per_batch_size = self.per_config.get('batch_size', 256)
        self.per_learning_starts = self.per_config.get('learning_starts', 1000)
        self.per_target_update_frequency = self.per_config.get('target_update_frequency', 100)
        
        # 训练统计
        self.per_updates = 0
        self.td_error_history = []
        self.priority_stats_history = []
        
    def collect_experience(self,
                          env,
                          model: AttentionEnhancedMAPPOModel,
                          num_steps: int) -> Dict[str, Any]:
        """
        收集经验并存储到优先级回放缓冲区
        
        Args:
            env: 环境
            model: MAPPO模型
            num_steps: 收集步数
            
        Returns:
            collection_stats: 收集统计信息
        """
        model.eval()
        
        episode_rewards = defaultdict(float)
        episode_lengths = defaultdict(int)
        episodes_completed = 0
        
        obs = env.reset()
        
        for step in range(num_steps):
            # 获取动作和注意力权重
            with torch.no_grad():
                actions = {}
                task_attention_weights = {}
                collaboration_attention_weights = {}
                
                for agent_id in env.agents:
                    if agent_id in obs:
                        obs_tensor = torch.FloatTensor(obs[agent_id]).unsqueeze(0)
                        input_dict = {"obs": obs_tensor}
                        
                        # 前向传播
                        logits, _ = model.forward(input_dict, [], None)
                        
                        # 采样动作
                        action_dist = torch.distributions.Categorical(logits=logits)
                        action = action_dist.sample()
                        actions[agent_id] = action.item()
                        
                        # 获取注意力权重
                        attention_weights = model.get_attention_weights()
                        if 'task_attention' in attention_weights:
                            task_attention_weights[agent_id] = attention_weights['task_attention'].cpu().numpy()
                        if 'collaboration_attention' in attention_weights:
                            collaboration_attention_weights[agent_id] = attention_weights['collaboration_attention'].cpu().numpy()
            
            # 执行动作
            next_obs, rewards, dones, infos = env.step(actions)
            
            # 计算TD误差（简化版本）
            td_errors = self._compute_td_errors(obs, actions, rewards, next_obs, dones, model)
            
            # 存储经验
            self.replay_manager.add_experience(
                observations=obs,
                actions=actions,
                rewards=rewards,
                next_observations=next_obs,
                dones=dones,
                infos=infos,
                task_attention_weights=task_attention_weights,
                collaboration_attention_weights=collaboration_attention_weights,
                td_errors=td_errors
            )
            
            # 更新统计
            for agent_id in env.agents:
                if agent_id in rewards:
                    episode_rewards[agent_id] += rewards[agent_id]
                    episode_lengths[agent_id] += 1
            
            # 检查回合结束
            if dones.get('__all__', False):
                self.replay_manager.end_episode()
                episodes_completed += 1
                obs = env.reset()
                
                # 重置回合统计
                episode_rewards = defaultdict(float)
                episode_lengths = defaultdict(int)
            else:
                obs = next_obs
        
        return {
            'episodes_completed': episodes_completed,
            'total_steps': num_steps,
            'buffer_size': len(self.replay_manager.buffer),
            'buffer_utilization': len(self.replay_manager.buffer) / self.buffer_capacity
        }
    
    def _compute_td_errors(self,
                          obs: Dict[str, np.ndarray],
                          actions: Dict[str, int],
                          rewards: Dict[str, float],
                          next_obs: Dict[str, np.ndarray],
                          dones: Dict[str, bool],
                          model: AttentionEnhancedMAPPOModel) -> Dict[str, float]:
        """
        计算TD误差
        
        Args:
            obs: 当前观察
            actions: 动作
            rewards: 奖励
            next_obs: 下一状态观察
            dones: 完成标志
            model: MAPPO模型
            
        Returns:
            td_errors: TD误差字典
        """
        td_errors = {}
        
        with torch.no_grad():
            for agent_id in obs.keys():
                if agent_id in next_obs and agent_id in rewards:
                    # 当前状态价值
                    obs_tensor = torch.FloatTensor(obs[agent_id]).unsqueeze(0)
                    input_dict = {"obs": obs_tensor}
                    model.forward(input_dict, [], None)
                    current_value = model.value_function().item()
                    
                    # 下一状态价值
                    next_obs_tensor = torch.FloatTensor(next_obs[agent_id]).unsqueeze(0)
                    next_input_dict = {"obs": next_obs_tensor}
                    model.forward(next_input_dict, [], None)
                    next_value = model.value_function().item()
                    
                    # 计算TD误差
                    reward = rewards[agent_id]
                    done = dones.get(agent_id, False)
                    gamma = 0.99  # 折扣因子
                    
                    target_value = reward + gamma * next_value * (1 - done)
                    td_error = abs(target_value - current_value)
                    
                    td_errors[agent_id] = td_error
        
        return td_errors
    
    def train_step(self, model: AttentionEnhancedMAPPOModel, optimizer: torch.optim.Optimizer) -> Dict[str, float]:
        """
        执行一步PER增强的MAPPO训练
        
        Args:
            model: MAPPO模型
            optimizer: 优化器
            
        Returns:
            training_stats: 训练统计信息
        """
        if not self.replay_manager.buffer.is_ready(self.per_batch_size):
            return {'skipped': True, 'reason': 'insufficient_data'}
        
        model.train()
        
        # 从优先级回放缓冲区采样
        batch_data, importance_weights, indices = self.replay_manager.sample_batch(self.per_batch_size)
        
        # 计算损失
        losses = self._compute_per_losses(model, batch_data, importance_weights)
        
        # 反向传播
        total_loss = losses['total_loss']
        optimizer.zero_grad()
        total_loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), self.max_grad_norm)
        
        optimizer.step()
        
        # 更新优先级
        td_errors = losses['td_errors'].detach().cpu().numpy()
        self.replay_manager.update_priorities(indices, td_errors)
        
        # 更新统计
        self.per_updates += 1
        self.td_error_history.extend(td_errors.tolist())
        
        # 保持历史记录长度
        if len(self.td_error_history) > 10000:
            self.td_error_history = self.td_error_history[-10000:]
        
        return {
            'total_loss': total_loss.item(),
            'policy_loss': losses['policy_loss'].item(),
            'value_loss': losses['value_loss'].item(),
            'entropy_loss': losses['entropy_loss'].item(),
            'attention_loss': losses.get('attention_loss', torch.tensor(0.0)).item(),
            'mean_td_error': np.mean(td_errors),
            'max_td_error': np.max(td_errors),
            'mean_importance_weight': importance_weights.mean().item(),
            'max_importance_weight': importance_weights.max().item(),
            'per_updates': self.per_updates
        }
    
    def _compute_per_losses(self,
                           model: AttentionEnhancedMAPPOModel,
                           batch_data: Dict[str, torch.Tensor],
                           importance_weights: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算PER增强的损失函数
        
        Args:
            model: MAPPO模型
            batch_data: 批次数据
            importance_weights: 重要性采样权重
            
        Returns:
            losses: 损失字典
        """
        batch_size = importance_weights.shape[0]
        agent_ids = list(batch_data['observations'].keys())
        
        total_policy_loss = 0.0
        total_value_loss = 0.0
        total_entropy_loss = 0.0
        total_attention_loss = 0.0
        td_errors_list = []
        
        for agent_id in agent_ids:
            # 获取智能体数据
            obs = batch_data['observations'][agent_id]
            actions = batch_data['actions'][agent_id]
            rewards = batch_data['rewards'][agent_id]
            next_obs = batch_data['next_observations'][agent_id]
            dones = batch_data['dones'][agent_id]
            
            # 前向传播
            input_dict = {"obs": obs}
            logits, _ = model.forward(input_dict, [], None)
            values = model.value_function()
            
            # 计算下一状态价值
            next_input_dict = {"obs": next_obs}
            model.forward(next_input_dict, [], None)
            next_values = model.value_function()
            
            # 计算目标价值和优势
            gamma = 0.99
            targets = rewards + gamma * next_values * (~dones).float()
            advantages = targets - values
            td_errors = torch.abs(advantages)
            
            # 策略损失
            action_dist = torch.distributions.Categorical(logits=logits)
            log_probs = action_dist.log_prob(actions)
            policy_loss = -(log_probs * advantages.detach() * importance_weights).mean()
            
            # 价值损失
            value_loss = F.mse_loss(values, targets.detach(), reduction='none')
            value_loss = (value_loss * importance_weights).mean()
            
            # 熵损失
            entropy = action_dist.entropy()
            entropy_loss = -entropy.mean()
            
            # 累积损失
            total_policy_loss += policy_loss
            total_value_loss += value_loss
            total_entropy_loss += entropy_loss
            td_errors_list.append(td_errors)
        
        # 注意力正则化损失
        attention_weights = model.get_attention_weights()
        if self.attention_regularization and attention_weights:
            attention_loss = self.compute_attention_loss(attention_weights)
            total_attention_loss = attention_loss
        
        # 总损失
        total_loss = (total_policy_loss + 
                     self.value_loss_coeff * total_value_loss + 
                     self.entropy_coeff * total_entropy_loss +
                     self.attention_loss_coeff * total_attention_loss)
        
        # 合并TD误差
        all_td_errors = torch.cat(td_errors_list, dim=0)
        
        return {
            'total_loss': total_loss,
            'policy_loss': total_policy_loss,
            'value_loss': total_value_loss,
            'entropy_loss': total_entropy_loss,
            'attention_loss': total_attention_loss,
            'td_errors': all_td_errors
        }
    
    def get_per_stats(self) -> Dict[str, Any]:
        """
        获取PER相关统计信息
        
        Returns:
            per_stats: PER统计信息
        """
        replay_stats = self.replay_manager.get_stats()
        
        td_error_stats = {}
        if self.td_error_history:
            td_error_stats = {
                'mean_td_error': np.mean(self.td_error_history),
                'std_td_error': np.std(self.td_error_history),
                'max_td_error': np.max(self.td_error_history),
                'min_td_error': np.min(self.td_error_history)
            }
        
        return {
            'per_updates': self.per_updates,
            'replay_manager_stats': replay_stats,
            'td_error_stats': td_error_stats,
            'per_config': {
                'buffer_capacity': self.buffer_capacity,
                'alpha': self.per_alpha,
                'beta': self.replay_manager.buffer.beta,
                'attention_priority_weight': self.attention_priority_weight
            }
        }
    
    def save_per_state(self, filepath: str):
        """
        保存PER状态
        
        Args:
            filepath: 保存路径
        """
        state = {
            'replay_buffer_state': self.replay_manager.buffer.save_state(),
            'per_updates': self.per_updates,
            'td_error_history': self.td_error_history[-1000:],  # 只保存最近的历史
            'per_config': {
                'buffer_capacity': self.buffer_capacity,
                'alpha': self.per_alpha,
                'beta': self.per_beta,
                'attention_priority_weight': self.attention_priority_weight
            }
        }
        
        torch.save(state, filepath)
    
    def load_per_state(self, filepath: str):
        """
        加载PER状态
        
        Args:
            filepath: 加载路径
        """
        state = torch.load(filepath)
        
        self.replay_manager.buffer.load_state(state['replay_buffer_state'])
        self.per_updates = state['per_updates']
        self.td_error_history = state['td_error_history']
