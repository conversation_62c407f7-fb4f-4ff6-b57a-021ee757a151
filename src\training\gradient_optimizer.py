"""
梯度优化组件
实现训练稳定性的梯度优化技术
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import logging
from collections import deque


@dataclass
class GradientStats:
    """梯度统计信息"""
    grad_norm: float
    max_grad: float
    min_grad: float
    grad_std: float
    clipped: bool
    scale_factor: float


class AdaptiveGradientClipper:
    """自适应梯度裁剪器"""
    
    def __init__(self, 
                 max_norm: float = 1.0,
                 adaptive: bool = True,
                 percentile: float = 95.0,
                 window_size: int = 100):
        """
        初始化自适应梯度裁剪器
        
        Args:
            max_norm: 最大梯度范数
            adaptive: 是否使用自适应裁剪
            percentile: 自适应阈值百分位数
            window_size: 统计窗口大小
        """
        self.max_norm = max_norm
        self.adaptive = adaptive
        self.percentile = percentile
        self.window_size = window_size
        
        # 梯度历史记录
        self.grad_norms = deque(maxlen=window_size)
        self.adaptive_threshold = max_norm
        
        # 统计信息
        self.total_clips = 0
        self.total_steps = 0
        
    def clip_gradients(self, parameters) -> GradientStats:
        """
        裁剪梯度
        
        Args:
            parameters: 模型参数
            
        Returns:
            grad_stats: 梯度统计信息
        """
        # 计算梯度范数
        total_norm = 0.0
        max_grad = 0.0
        min_grad = float('inf')
        grad_values = []
        
        for param in parameters:
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
                
                # 收集梯度值统计
                grad_flat = param.grad.data.flatten()
                max_grad = max(max_grad, grad_flat.max().item())
                min_grad = min(min_grad, grad_flat.min().item())
                grad_values.extend(grad_flat.cpu().numpy())
        
        total_norm = total_norm ** (1. / 2)
        grad_std = np.std(grad_values) if grad_values else 0.0
        
        # 更新自适应阈值
        if self.adaptive:
            self.grad_norms.append(total_norm)
            if len(self.grad_norms) >= 10:  # 至少需要10个样本
                self.adaptive_threshold = np.percentile(self.grad_norms, self.percentile)
                self.adaptive_threshold = max(self.adaptive_threshold, self.max_norm * 0.1)  # 最小阈值
        
        # 确定裁剪阈值
        clip_threshold = self.adaptive_threshold if self.adaptive else self.max_norm
        
        # 执行梯度裁剪
        clipped = total_norm > clip_threshold
        scale_factor = 1.0
        
        if clipped:
            scale_factor = clip_threshold / total_norm
            torch.nn.utils.clip_grad_norm_(parameters, clip_threshold)
            self.total_clips += 1
        
        self.total_steps += 1
        
        return GradientStats(
            grad_norm=total_norm,
            max_grad=max_grad,
            min_grad=min_grad if min_grad != float('inf') else 0.0,
            grad_std=grad_std,
            clipped=clipped,
            scale_factor=scale_factor
        )
    
    def get_stats(self) -> Dict[str, float]:
        """获取裁剪统计信息"""
        return {
            'clip_rate': self.total_clips / max(self.total_steps, 1),
            'adaptive_threshold': self.adaptive_threshold,
            'total_clips': self.total_clips,
            'total_steps': self.total_steps,
            'avg_grad_norm': np.mean(self.grad_norms) if self.grad_norms else 0.0
        }


class GradientNormalizer:
    """梯度归一化器"""
    
    def __init__(self, 
                 method: str = 'layer_wise',
                 momentum: float = 0.9,
                 eps: float = 1e-8):
        """
        初始化梯度归一化器
        
        Args:
            method: 归一化方法 ('layer_wise', 'global', 'adaptive')
            momentum: 动量系数
            eps: 数值稳定性常数
        """
        self.method = method
        self.momentum = momentum
        self.eps = eps
        
        # 运行统计
        self.running_mean = {}
        self.running_var = {}
        self.step_count = 0
        
    def normalize_gradients(self, named_parameters) -> Dict[str, float]:
        """
        归一化梯度
        
        Args:
            named_parameters: 命名参数迭代器
            
        Returns:
            norm_stats: 归一化统计信息
        """
        self.step_count += 1
        norm_stats = {}
        
        if self.method == 'layer_wise':
            # 逐层归一化
            for name, param in named_parameters:
                if param.grad is not None:
                    grad = param.grad.data
                    
                    # 计算层级统计
                    grad_mean = grad.mean()
                    grad_var = grad.var()
                    
                    # 更新运行统计
                    if name not in self.running_mean:
                        self.running_mean[name] = grad_mean
                        self.running_var[name] = grad_var
                    else:
                        self.running_mean[name] = (self.momentum * self.running_mean[name] + 
                                                 (1 - self.momentum) * grad_mean)
                        self.running_var[name] = (self.momentum * self.running_var[name] + 
                                                (1 - self.momentum) * grad_var)
                    
                    # 归一化
                    normalized_grad = (grad - self.running_mean[name]) / (
                        torch.sqrt(self.running_var[name] + self.eps))
                    param.grad.data = normalized_grad
                    
                    norm_stats[f'{name}_mean'] = grad_mean.item()
                    norm_stats[f'{name}_std'] = torch.sqrt(grad_var).item()
                    
        elif self.method == 'global':
            # 全局归一化
            all_grads = []
            for name, param in named_parameters:
                if param.grad is not None:
                    all_grads.append(param.grad.data.flatten())
            
            if all_grads:
                all_grads = torch.cat(all_grads)
                global_mean = all_grads.mean()
                global_std = all_grads.std()
                
                # 归一化所有梯度
                for name, param in named_parameters:
                    if param.grad is not None:
                        param.grad.data = (param.grad.data - global_mean) / (global_std + self.eps)
                
                norm_stats['global_mean'] = global_mean.item()
                norm_stats['global_std'] = global_std.item()
        
        return norm_stats


class AdaptiveLearningRateScheduler:
    """自适应学习率调度器"""
    
    def __init__(self, 
                 optimizer: torch.optim.Optimizer,
                 patience: int = 10,
                 factor: float = 0.5,
                 min_lr: float = 1e-6,
                 threshold: float = 1e-4):
        """
        初始化自适应学习率调度器
        
        Args:
            optimizer: 优化器
            patience: 耐心值
            factor: 衰减因子
            min_lr: 最小学习率
            threshold: 改善阈值
        """
        self.optimizer = optimizer
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.threshold = threshold
        
        # 状态跟踪
        self.best_loss = float('inf')
        self.wait_count = 0
        self.lr_reductions = 0
        
        # 学习率历史
        self.lr_history = []
        
    def step(self, loss: float) -> bool:
        """
        更新学习率
        
        Args:
            loss: 当前损失
            
        Returns:
            reduced: 是否降低了学习率
        """
        current_lr = self.optimizer.param_groups[0]['lr']
        self.lr_history.append(current_lr)
        
        # 检查是否有改善
        if loss < self.best_loss - self.threshold:
            self.best_loss = loss
            self.wait_count = 0
            return False
        else:
            self.wait_count += 1
            
            # 检查是否需要降低学习率
            if self.wait_count >= self.patience:
                new_lr = max(current_lr * self.factor, self.min_lr)
                
                if new_lr < current_lr:
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] = new_lr
                    
                    self.lr_reductions += 1
                    self.wait_count = 0
                    
                    logging.info(f"学习率降低: {current_lr:.6f} -> {new_lr:.6f}")
                    return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        return {
            'current_lr': self.optimizer.param_groups[0]['lr'],
            'best_loss': self.best_loss,
            'wait_count': self.wait_count,
            'lr_reductions': self.lr_reductions,
            'lr_history': self.lr_history[-100:]  # 最近100步的学习率历史
        }


class GradientOptimizer:
    """综合梯度优化器"""
    
    def __init__(self, 
                 model: nn.Module,
                 optimizer: torch.optim.Optimizer,
                 clip_config: Optional[Dict] = None,
                 norm_config: Optional[Dict] = None,
                 lr_config: Optional[Dict] = None):
        """
        初始化综合梯度优化器
        
        Args:
            model: 模型
            optimizer: 优化器
            clip_config: 梯度裁剪配置
            norm_config: 梯度归一化配置
            lr_config: 学习率调度配置
        """
        self.model = model
        self.optimizer = optimizer
        
        # 初始化组件
        self.clipper = AdaptiveGradientClipper(**(clip_config or {}))
        self.normalizer = GradientNormalizer(**(norm_config or {})) if norm_config else None
        self.lr_scheduler = AdaptiveLearningRateScheduler(
            optimizer, **(lr_config or {})) if lr_config else None
        
        # 统计信息
        self.step_count = 0
        self.optimization_history = []
        
    def optimize_step(self, loss: torch.Tensor) -> Dict[str, Any]:
        """
        执行优化步骤
        
        Args:
            loss: 损失值
            
        Returns:
            stats: 优化统计信息
        """
        self.step_count += 1
        stats = {'step': self.step_count, 'loss': loss.item()}
        
        # 反向传播
        loss.backward()
        
        # 梯度归一化
        if self.normalizer:
            norm_stats = self.normalizer.normalize_gradients(self.model.named_parameters())
            stats.update(norm_stats)
        
        # 梯度裁剪
        grad_stats = self.clipper.clip_gradients(self.model.parameters())
        stats.update({
            'grad_norm': grad_stats.grad_norm,
            'max_grad': grad_stats.max_grad,
            'min_grad': grad_stats.min_grad,
            'grad_std': grad_stats.grad_std,
            'grad_clipped': grad_stats.clipped,
            'grad_scale': grad_stats.scale_factor
        })
        
        # 优化器步骤
        self.optimizer.step()
        self.optimizer.zero_grad()
        
        # 学习率调度
        if self.lr_scheduler:
            lr_reduced = self.lr_scheduler.step(loss.item())
            stats['lr_reduced'] = lr_reduced
            stats.update(self.lr_scheduler.get_stats())
        
        # 记录历史
        self.optimization_history.append(stats)
        if len(self.optimization_history) > 1000:  # 保持最近1000步
            self.optimization_history.pop(0)
        
        return stats
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        stats = {
            'step_count': self.step_count,
            'clipper_stats': self.clipper.get_stats()
        }
        
        if self.lr_scheduler:
            stats['lr_scheduler_stats'] = self.lr_scheduler.get_stats()
        
        # 最近性能统计
        if self.optimization_history:
            recent_history = self.optimization_history[-100:]
            stats['recent_performance'] = {
                'avg_loss': np.mean([h['loss'] for h in recent_history]),
                'avg_grad_norm': np.mean([h['grad_norm'] for h in recent_history]),
                'clip_rate': np.mean([h['grad_clipped'] for h in recent_history])
            }
        
        return stats
