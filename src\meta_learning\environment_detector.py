"""
环境变化检测器
检测AGV仓储环境的变化并触发快速适应
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum
import time
from collections import deque


class ChangeType(Enum):
    """变化类型"""
    NO_CHANGE = "no_change"
    MINOR_CHANGE = "minor_change"
    MODERATE_CHANGE = "moderate_change"
    MAJOR_CHANGE = "major_change"
    CRITICAL_CHANGE = "critical_change"


@dataclass
class DetectionConfig:
    """检测配置"""
    
    # 检测阈值
    minor_change_threshold: float = 0.1
    moderate_change_threshold: float = 0.3
    major_change_threshold: float = 0.6
    critical_change_threshold: float = 0.8
    
    # 统计窗口
    observation_window_size: int = 100
    performance_window_size: int = 50
    
    # 检测频率
    detection_interval: int = 10  # 每10步检测一次
    
    # 平滑参数
    smoothing_factor: float = 0.9
    
    # 性能阈值
    performance_drop_threshold: float = 0.2
    adaptation_trigger_threshold: float = 0.4


class EnvironmentChangeDetector:
    """
    环境变化检测器
    实时监控环境变化并触发适应机制
    """
    
    def __init__(self, config: DetectionConfig = None):
        """
        初始化环境变化检测器
        
        Args:
            config: 检测配置
        """
        self.config = config or DetectionConfig()
        
        # 历史数据缓存
        self.observation_history = deque(maxlen=self.config.observation_window_size)
        self.reward_history = deque(maxlen=self.config.performance_window_size)
        self.action_history = deque(maxlen=self.config.observation_window_size)
        
        # 基线统计
        self.baseline_stats = {
            'obs_mean': None,
            'obs_std': None,
            'reward_mean': None,
            'reward_std': None,
            'action_distribution': None
        }
        
        # 当前统计
        self.current_stats = {
            'obs_mean': None,
            'obs_std': None,
            'reward_mean': None,
            'reward_std': None,
            'action_distribution': None
        }
        
        # 变化检测状态
        self.last_detection_step = 0
        self.change_history = []
        self.adaptation_triggered = False
        
        # 性能监控
        self.performance_tracker = {
            'recent_performance': deque(maxlen=20),
            'baseline_performance': None,
            'performance_trend': 0.0
        }
        
        # 平滑的变化指标
        self.smoothed_change_magnitude = 0.0
        
    def update(self, 
               observations: torch.Tensor,
               actions: torch.Tensor,
               rewards: torch.Tensor,
               step: int) -> Dict[str, Any]:
        """
        更新检测器状态
        
        Args:
            observations: 观察数据
            actions: 动作数据
            rewards: 奖励数据
            step: 当前步数
            
        Returns:
            detection_result: 检测结果
        """
        # 添加到历史
        self._add_to_history(observations, actions, rewards)
        
        # 更新性能跟踪
        self._update_performance_tracking(rewards)
        
        # 检测变化
        detection_result = {'change_detected': False, 'change_type': ChangeType.NO_CHANGE}
        
        if step - self.last_detection_step >= self.config.detection_interval:
            detection_result = self._detect_changes(step)
            self.last_detection_step = step
        
        return detection_result
    
    def _add_to_history(self, 
                       observations: torch.Tensor,
                       actions: torch.Tensor,
                       rewards: torch.Tensor):
        """添加数据到历史缓存"""
        # 转换为numpy以便处理
        if isinstance(observations, torch.Tensor):
            observations = observations.detach().cpu().numpy()
        if isinstance(actions, torch.Tensor):
            actions = actions.detach().cpu().numpy()
        if isinstance(rewards, torch.Tensor):
            rewards = rewards.detach().cpu().numpy()
        
        # 添加到历史
        self.observation_history.extend(observations)
        self.action_history.extend(actions)
        self.reward_history.extend(rewards)
    
    def _update_performance_tracking(self, rewards: torch.Tensor):
        """更新性能跟踪"""
        if isinstance(rewards, torch.Tensor):
            rewards = rewards.detach().cpu().numpy()
        
        # 计算平均奖励
        avg_reward = np.mean(rewards)
        self.performance_tracker['recent_performance'].append(avg_reward)
        
        # 更新基线性能
        if self.performance_tracker['baseline_performance'] is None:
            if len(self.performance_tracker['recent_performance']) >= 10:
                self.performance_tracker['baseline_performance'] = np.mean(
                    list(self.performance_tracker['recent_performance'])[:10]
                )
    
    def _detect_changes(self, step: int) -> Dict[str, Any]:
        """
        检测环境变化
        
        Args:
            step: 当前步数
            
        Returns:
            detection_result: 检测结果
        """
        if len(self.observation_history) < 20:  # 需要足够的数据
            return {'change_detected': False, 'change_type': ChangeType.NO_CHANGE}
        
        # 更新统计信息
        self._update_statistics()
        
        # 计算变化幅度
        change_magnitude = self._calculate_change_magnitude()
        
        # 平滑变化幅度
        self.smoothed_change_magnitude = (
            self.config.smoothing_factor * self.smoothed_change_magnitude +
            (1 - self.config.smoothing_factor) * change_magnitude
        )
        
        # 确定变化类型
        change_type = self._classify_change(self.smoothed_change_magnitude)
        
        # 检查性能下降
        performance_drop = self._check_performance_drop()
        
        # 决定是否触发适应
        should_adapt = (
            self.smoothed_change_magnitude > self.config.adaptation_trigger_threshold or
            performance_drop > self.config.performance_drop_threshold
        )
        
        # 记录检测结果
        detection_result = {
            'change_detected': change_type != ChangeType.NO_CHANGE,
            'change_type': change_type,
            'change_magnitude': self.smoothed_change_magnitude,
            'performance_drop': performance_drop,
            'should_adapt': should_adapt,
            'step': step,
            'statistics': {
                'obs_change': self._calculate_observation_change(),
                'reward_change': self._calculate_reward_change(),
                'action_change': self._calculate_action_change()
            }
        }
        
        # 记录到历史
        self.change_history.append(detection_result)
        
        # 更新适应状态
        if should_adapt:
            self.adaptation_triggered = True
        
        return detection_result
    
    def _update_statistics(self):
        """更新当前统计信息"""
        if len(self.observation_history) == 0:
            return
        
        # 观察统计
        recent_obs = np.array(list(self.observation_history)[-50:])  # 最近50个观察
        self.current_stats['obs_mean'] = np.mean(recent_obs, axis=0)
        self.current_stats['obs_std'] = np.std(recent_obs, axis=0)
        
        # 奖励统计
        if len(self.reward_history) > 0:
            recent_rewards = np.array(list(self.reward_history)[-20:])  # 最近20个奖励
            self.current_stats['reward_mean'] = np.mean(recent_rewards)
            self.current_stats['reward_std'] = np.std(recent_rewards)
        
        # 动作分布统计
        if len(self.action_history) > 0:
            recent_actions = np.array(list(self.action_history)[-50:])
            action_counts = np.bincount(recent_actions.astype(int), minlength=7)
            self.current_stats['action_distribution'] = action_counts / action_counts.sum()
        
        # 更新基线统计（如果还没有）
        if self.baseline_stats['obs_mean'] is None and len(self.observation_history) >= 50:
            baseline_obs = np.array(list(self.observation_history)[:50])  # 前50个观察作为基线
            self.baseline_stats['obs_mean'] = np.mean(baseline_obs, axis=0)
            self.baseline_stats['obs_std'] = np.std(baseline_obs, axis=0)
            
            if len(self.reward_history) >= 20:
                baseline_rewards = np.array(list(self.reward_history)[:20])
                self.baseline_stats['reward_mean'] = np.mean(baseline_rewards)
                self.baseline_stats['reward_std'] = np.std(baseline_rewards)
            
            if len(self.action_history) >= 50:
                baseline_actions = np.array(list(self.action_history)[:50])
                action_counts = np.bincount(baseline_actions.astype(int), minlength=7)
                self.baseline_stats['action_distribution'] = action_counts / action_counts.sum()
    
    def _calculate_change_magnitude(self) -> float:
        """计算总体变化幅度"""
        if self.baseline_stats['obs_mean'] is None:
            return 0.0
        
        changes = []
        
        # 观察变化
        obs_change = self._calculate_observation_change()
        changes.append(obs_change)
        
        # 奖励变化
        reward_change = self._calculate_reward_change()
        changes.append(reward_change)
        
        # 动作分布变化
        action_change = self._calculate_action_change()
        changes.append(action_change)
        
        # 加权平均
        weights = [0.5, 0.3, 0.2]  # 观察变化权重最高
        weighted_change = sum(w * c for w, c in zip(weights, changes))
        
        return weighted_change
    
    def _calculate_observation_change(self) -> float:
        """计算观察变化"""
        if (self.baseline_stats['obs_mean'] is None or 
            self.current_stats['obs_mean'] is None):
            return 0.0
        
        # 均值变化
        mean_diff = np.linalg.norm(
            self.current_stats['obs_mean'] - self.baseline_stats['obs_mean']
        )
        
        # 标准差变化
        std_diff = np.linalg.norm(
            self.current_stats['obs_std'] - self.baseline_stats['obs_std']
        )
        
        # 归一化
        obs_change = (mean_diff + std_diff) / (np.linalg.norm(self.baseline_stats['obs_mean']) + 1e-8)
        
        return min(obs_change, 1.0)  # 限制在[0,1]范围
    
    def _calculate_reward_change(self) -> float:
        """计算奖励变化"""
        if (self.baseline_stats['reward_mean'] is None or 
            self.current_stats['reward_mean'] is None):
            return 0.0
        
        # 均值变化
        mean_diff = abs(self.current_stats['reward_mean'] - self.baseline_stats['reward_mean'])
        
        # 标准差变化
        std_diff = abs(self.current_stats['reward_std'] - self.baseline_stats['reward_std'])
        
        # 归一化
        baseline_scale = abs(self.baseline_stats['reward_mean']) + self.baseline_stats['reward_std'] + 1e-8
        reward_change = (mean_diff + std_diff) / baseline_scale
        
        return min(reward_change, 1.0)
    
    def _calculate_action_change(self) -> float:
        """计算动作分布变化"""
        if (self.baseline_stats['action_distribution'] is None or 
            self.current_stats['action_distribution'] is None):
            return 0.0
        
        # KL散度
        baseline_dist = self.baseline_stats['action_distribution'] + 1e-8
        current_dist = self.current_stats['action_distribution'] + 1e-8
        
        kl_div = np.sum(current_dist * np.log(current_dist / baseline_dist))
        
        # 归一化KL散度
        action_change = min(kl_div / np.log(7), 1.0)  # 7是动作数量
        
        return action_change
    
    def _classify_change(self, change_magnitude: float) -> ChangeType:
        """分类变化类型"""
        if change_magnitude < self.config.minor_change_threshold:
            return ChangeType.NO_CHANGE
        elif change_magnitude < self.config.moderate_change_threshold:
            return ChangeType.MINOR_CHANGE
        elif change_magnitude < self.config.major_change_threshold:
            return ChangeType.MODERATE_CHANGE
        elif change_magnitude < self.config.critical_change_threshold:
            return ChangeType.MAJOR_CHANGE
        else:
            return ChangeType.CRITICAL_CHANGE
    
    def _check_performance_drop(self) -> float:
        """检查性能下降"""
        if (self.performance_tracker['baseline_performance'] is None or
            len(self.performance_tracker['recent_performance']) < 5):
            return 0.0
        
        recent_avg = np.mean(list(self.performance_tracker['recent_performance'])[-5:])
        baseline = self.performance_tracker['baseline_performance']
        
        if baseline > 0:
            performance_drop = (baseline - recent_avg) / baseline
        else:
            performance_drop = abs(recent_avg - baseline)
        
        return max(0.0, performance_drop)
    
    def reset_adaptation_trigger(self):
        """重置适应触发状态"""
        self.adaptation_triggered = False
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        if not self.change_history:
            return {}
        
        recent_changes = self.change_history[-20:]  # 最近20次检测
        
        change_type_counts = {}
        for change in recent_changes:
            change_type = change['change_type']
            change_type_counts[change_type.value] = change_type_counts.get(change_type.value, 0) + 1
        
        return {
            'total_detections': len(self.change_history),
            'recent_change_types': change_type_counts,
            'current_change_magnitude': self.smoothed_change_magnitude,
            'adaptation_triggered': self.adaptation_triggered,
            'average_change_magnitude': np.mean([c['change_magnitude'] for c in recent_changes]),
            'performance_status': {
                'baseline_performance': self.performance_tracker['baseline_performance'],
                'recent_performance': np.mean(list(self.performance_tracker['recent_performance'])) if self.performance_tracker['recent_performance'] else None,
                'performance_drop': self._check_performance_drop()
            }
        }
