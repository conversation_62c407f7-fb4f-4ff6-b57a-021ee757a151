"""
注意力数据提取器
从注意力机制中提取可视化所需的数据
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
import json
from datetime import datetime

from ..attention.task_allocation_attention import TaskAllocationAttentionManager, AttentionOutput
from ..attention.collaboration_attention import CollaborationAttentionManager, CollaborationOutput
from ..environment.agv_entity import AGVEntity
from ..environment.task_manager import Task


class AttentionDataExtractor:
    """
    注意力数据提取器
    从双层注意力机制中提取可视化数据
    """
    
    def __init__(self):
        """初始化数据提取器"""
        self.attention_history = []
        self.current_step = 0
        
        print("✓ 注意力数据提取器初始化完成")
    
    def extract_task_attention_data(self,
                                  task_output: AttentionOutput,
                                  agvs: List[AGVEntity],
                                  tasks: List[Task]) -> Dict[str, Any]:
        """
        提取任务分配注意力数据
        
        Args:
            task_output: 任务分配注意力输出
            agvs: AGV列表
            tasks: 任务列表
            
        Returns:
            task_attention_data: 任务注意力数据字典
        """
        data = {
            'attention_weights': task_output.attention_weights,
            'attended_features': task_output.attended_features,
            'agv_ids': [f'AGV_{agv.agv_id}' for agv in agvs],
            'task_ids': [f'Task_{task.task_id}' for task in tasks],
            'agv_positions': [(agv.position[0], agv.position[1]) for agv in agvs],
            'task_positions': [(task.position[0], task.position[1]) for task in tasks],
            'agv_states': [self._get_agv_state_summary(agv) for agv in agvs],
            'task_states': [self._get_task_state_summary(task) for task in tasks],
            'timestamp': datetime.now().isoformat(),
            'step': self.current_step
        }
        
        # 计算注意力统计
        if isinstance(task_output.attention_weights, torch.Tensor):
            weights = task_output.attention_weights.detach().cpu().numpy()
        else:
            weights = np.array(task_output.attention_weights)
        
        data['statistics'] = {
            'mean_attention': float(np.mean(weights)),
            'max_attention': float(np.max(weights)),
            'min_attention': float(np.min(weights)),
            'std_attention': float(np.std(weights)),
            'attention_entropy': self._calculate_attention_entropy(weights)
        }
        
        # 分析注意力模式
        data['patterns'] = self._analyze_task_attention_patterns(weights, agvs, tasks)
        
        return data
    
    def extract_collaboration_attention_data(self,
                                           collab_output: CollaborationOutput,
                                           agvs: List[AGVEntity]) -> Dict[str, Any]:
        """
        提取协作感知注意力数据
        
        Args:
            collab_output: 协作感知注意力输出
            agvs: AGV列表
            
        Returns:
            collaboration_data: 协作注意力数据字典
        """
        data = {
            'collaboration_weights': collab_output.collaboration_weights,
            'collaborated_features': collab_output.collaborated_features,
            'near_attention': collab_output.near_attention,
            'far_attention': collab_output.far_attention,
            'adaptive_temperatures': collab_output.adaptive_temperatures,
            'agv_ids': [f'AGV_{agv.agv_id}' for agv in agvs],
            'agv_positions': [(agv.position[0], agv.position[1]) for agv in agvs],
            'agv_states': [self._get_agv_state_summary(agv) for agv in agvs],
            'timestamp': datetime.now().isoformat(),
            'step': self.current_step
        }
        
        # 计算协作统计
        if isinstance(collab_output.collaboration_weights, torch.Tensor):
            weights = collab_output.collaboration_weights.squeeze(0).detach().cpu().numpy()
        else:
            weights = np.array(collab_output.collaboration_weights).squeeze(0)
        
        data['statistics'] = {
            'mean_collaboration': float(np.mean(weights)),
            'max_collaboration': float(np.max(weights)),
            'collaboration_density': float(np.sum(weights > 0.1) / weights.size),
            'collaboration_entropy': self._calculate_attention_entropy(weights)
        }
        
        # 分析协作模式
        data['patterns'] = self._analyze_collaboration_patterns(weights, agvs)
        
        return data
    
    def extract_combined_attention_data(self,
                                      task_output: AttentionOutput,
                                      collab_output: CollaborationOutput,
                                      agvs: List[AGVEntity],
                                      tasks: List[Task]) -> Dict[str, Any]:
        """
        提取双层注意力的综合数据
        
        Args:
            task_output: 任务分配注意力输出
            collab_output: 协作感知注意力输出
            agvs: AGV列表
            tasks: 任务列表
            
        Returns:
            combined_data: 综合注意力数据字典
        """
        # 提取各层数据
        task_data = self.extract_task_attention_data(task_output, agvs, tasks)
        collab_data = self.extract_collaboration_attention_data(collab_output, agvs)
        
        # 综合数据
        combined_data = {
            'task_attention': task_data,
            'collaboration_attention': collab_data,
            'timestamp': datetime.now().isoformat(),
            'step': self.current_step,
            'num_agvs': len(agvs),
            'num_tasks': len(tasks)
        }
        
        # 计算双层注意力的相关性
        combined_data['correlation'] = self._calculate_attention_correlation(
            task_data['attention_weights'],
            collab_data['collaboration_weights']
        )
        
        # 综合分析
        combined_data['analysis'] = self._analyze_dual_attention_interaction(
            task_data, collab_data, agvs, tasks
        )
        
        # 保存到历史记录
        self.attention_history.append(combined_data)
        self.current_step += 1
        
        return combined_data
    
    def _get_agv_state_summary(self, agv: AGVEntity) -> Dict[str, Any]:
        """获取AGV状态摘要"""
        return {
            'id': agv.agv_id,
            'position': agv.position,
            'status': agv.status.name if hasattr(agv.status, 'name') else str(agv.status),
            'current_load': agv.current_load,
            'capacity': agv.capacity,
            'load_ratio': agv.current_load / agv.capacity if agv.capacity > 0 else 0,
            'is_idle': agv.is_idle()
        }
    
    def _get_task_state_summary(self, task: Task) -> Dict[str, Any]:
        """获取任务状态摘要"""
        return {
            'id': task.task_id,
            'position': task.position,
            'weight': task.weight,
            'status': task.status.name if hasattr(task.status, 'name') else str(task.status),
            'assigned_agv': task.assigned_agv,
            'created_time': getattr(task, 'created_time', 0)
        }
    
    def _calculate_attention_entropy(self, weights: np.ndarray) -> float:
        """计算注意力熵"""
        # 避免log(0)
        weights_safe = weights + 1e-8
        # 归一化
        weights_norm = weights_safe / np.sum(weights_safe)
        # 计算熵
        entropy = -np.sum(weights_norm * np.log(weights_norm))
        return float(entropy)
    
    def _analyze_task_attention_patterns(self, 
                                       weights: np.ndarray,
                                       agvs: List[AGVEntity],
                                       tasks: List[Task]) -> Dict[str, Any]:
        """分析任务注意力模式"""
        patterns = {}
        
        # 找出每个AGV最关注的任务
        max_attention_tasks = np.argmax(weights, axis=1)
        patterns['preferred_tasks'] = {
            f'AGV_{agvs[i].agv_id}': f'Task_{tasks[max_attention_tasks[i]].task_id}'
            for i in range(len(agvs)) if max_attention_tasks[i] < len(tasks)
        }
        
        # 计算任务竞争度（多少个AGV关注同一个任务）
        task_competition = np.sum(weights > 0.1, axis=0)
        patterns['task_competition'] = {
            f'Task_{tasks[i].task_id}': int(task_competition[i])
            for i in range(len(tasks))
        }
        
        # 分析注意力分散度
        attention_variance = np.var(weights, axis=1)
        patterns['attention_focus'] = {
            f'AGV_{agvs[i].agv_id}': 'focused' if attention_variance[i] > 0.1 else 'dispersed'
            for i in range(len(agvs))
        }
        
        return patterns
    
    def _analyze_collaboration_patterns(self,
                                      weights: np.ndarray,
                                      agvs: List[AGVEntity]) -> Dict[str, Any]:
        """分析协作注意力模式"""
        patterns = {}
        
        # 找出协作关系
        collaboration_threshold = 0.1
        collaborations = []
        
        for i in range(len(agvs)):
            for j in range(len(agvs)):
                if i != j and weights[i, j] > collaboration_threshold:
                    collaborations.append({
                        'from': f'AGV_{agvs[i].agv_id}',
                        'to': f'AGV_{agvs[j].agv_id}',
                        'strength': float(weights[i, j])
                    })
        
        patterns['collaborations'] = collaborations
        
        # 计算每个AGV的协作度
        collab_out = np.sum(weights, axis=1) - np.diag(weights)  # 排除自己
        collab_in = np.sum(weights, axis=0) - np.diag(weights)
        
        patterns['collaboration_scores'] = {
            f'AGV_{agvs[i].agv_id}': {
                'outgoing': float(collab_out[i]),
                'incoming': float(collab_in[i]),
                'total': float(collab_out[i] + collab_in[i])
            }
            for i in range(len(agvs))
        }
        
        return patterns
    
    def _calculate_attention_correlation(self,
                                       task_weights: torch.Tensor,
                                       collab_weights: torch.Tensor) -> float:
        """计算任务注意力和协作注意力的相关性"""
        try:
            # 转换为numpy数组
            if isinstance(task_weights, torch.Tensor):
                task_w = task_weights.detach().cpu().numpy()
            else:
                task_w = np.array(task_weights)
            
            if isinstance(collab_weights, torch.Tensor):
                collab_w = collab_weights.squeeze(0).detach().cpu().numpy()
            else:
                collab_w = np.array(collab_weights).squeeze(0)
            
            # 计算每个AGV的平均注意力
            task_avg = np.mean(task_w, axis=1) if task_w.ndim > 1 else task_w
            collab_avg = np.mean(collab_w, axis=1) if collab_w.ndim > 1 else collab_w
            
            # 计算相关系数
            if len(task_avg) == len(collab_avg) and len(task_avg) > 1:
                correlation = np.corrcoef(task_avg, collab_avg)[0, 1]
                return float(correlation) if not np.isnan(correlation) else 0.0
            else:
                return 0.0
                
        except Exception as e:
            print(f"计算注意力相关性时出错: {e}")
            return 0.0
    
    def _analyze_dual_attention_interaction(self,
                                          task_data: Dict[str, Any],
                                          collab_data: Dict[str, Any],
                                          agvs: List[AGVEntity],
                                          tasks: List[Task]) -> Dict[str, Any]:
        """分析双层注意力的交互作用"""
        analysis = {}
        
        # 分析注意力平衡
        task_intensity = task_data['statistics']['mean_attention']
        collab_intensity = collab_data['statistics']['mean_collaboration']
        
        analysis['attention_balance'] = {
            'task_dominance': task_intensity / (task_intensity + collab_intensity + 1e-8),
            'collaboration_dominance': collab_intensity / (task_intensity + collab_intensity + 1e-8),
            'balance_score': 1 - abs(task_intensity - collab_intensity) / (task_intensity + collab_intensity + 1e-8)
        }
        
        # 分析决策一致性
        task_patterns = task_data['patterns']
        collab_patterns = collab_data['patterns']
        
        analysis['decision_consistency'] = self._calculate_decision_consistency(
            task_patterns, collab_patterns, agvs
        )
        
        return analysis
    
    def _calculate_decision_consistency(self,
                                      task_patterns: Dict[str, Any],
                                      collab_patterns: Dict[str, Any],
                                      agvs: List[AGVEntity]) -> Dict[str, Any]:
        """计算决策一致性"""
        consistency = {}
        
        # 简化的一致性分析
        for agv in agvs:
            agv_id = f'AGV_{agv.agv_id}'
            
            # 检查任务偏好和协作行为的一致性
            task_focus = task_patterns.get('attention_focus', {}).get(agv_id, 'unknown')
            collab_score = collab_patterns.get('collaboration_scores', {}).get(agv_id, {}).get('total', 0)
            
            # 简单的一致性评分
            if task_focus == 'focused' and collab_score > 0.5:
                consistency[agv_id] = 'high'  # 专注任务且积极协作
            elif task_focus == 'dispersed' and collab_score < 0.3:
                consistency[agv_id] = 'low'   # 注意力分散且协作少
            else:
                consistency[agv_id] = 'medium'
        
        return consistency
    
    def get_attention_history(self) -> List[Dict[str, Any]]:
        """获取注意力历史记录"""
        return self.attention_history.copy()
    
    def clear_history(self):
        """清空历史记录"""
        self.attention_history.clear()
        self.current_step = 0
        print("✓ 注意力历史记录已清空")
    
    def save_attention_data(self, filepath: str):
        """保存注意力数据到文件"""
        try:
            # 转换torch.Tensor为可序列化的格式
            serializable_history = []
            for step_data in self.attention_history:
                serializable_step = self._make_serializable(step_data)
                serializable_history.append(serializable_step)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_history, f, indent=2, ensure_ascii=False)
            
            print(f"✓ 注意力数据已保存到: {filepath}")
            
        except Exception as e:
            print(f"❌ 保存注意力数据失败: {e}")
    
    def _make_serializable(self, data: Any) -> Any:
        """将数据转换为可序列化的格式"""
        if isinstance(data, torch.Tensor):
            return data.detach().cpu().numpy().tolist()
        elif isinstance(data, np.ndarray):
            return data.tolist()
        elif isinstance(data, dict):
            return {k: self._make_serializable(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        else:
            return data
