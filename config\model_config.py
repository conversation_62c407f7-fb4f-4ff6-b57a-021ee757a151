"""
模型配置文件
定义双层注意力机制和MAPPO模型的各种参数
"""

from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class AttentionConfig:
    """注意力机制配置"""

    # 基础配置
    embed_dim: int = 64                    # 嵌入维度
    num_heads: int = 4                     # 注意力头数
    dropout: float = 0.1                   # Dropout率

    # 第一层任务分配注意力配置
    task_attention_dim: int = 64           # 任务注意力维度
    distance_penalty: float = 2.0          # 距离惩罚系数
    capacity_reward: float = 1.0           # 载重约束奖励系数
    priority_weight: float = 0.5           # 优先级权重系数

    # 第二层协作感知注意力配置
    collab_attention_dim: int = 64         # 协作注意力维度
    relative_pos_dim: int = 32             # 相对位置编码维度
    temperature_init: float = 1.0          # 初始温度参数
    temperature_learnable: bool = True     # 温度参数是否可学习

    # 稀疏化配置
    use_sparse_attention: bool = True      # 是否使用稀疏注意力
    top_k: int = 8                         # Top-K稀疏化参数
    sparsity_threshold: float = 0.1        # 稀疏化阈值

    # 融合配置
    fusion_method: str = "gate"            # 融合方法：gate, concat, add
    gate_activation: str = "sigmoid"       # 门控激活函数
    use_layer_norm: bool = True            # 是否使用层归一化
    use_residual: bool = True              # 是否使用残差连接

    # 正则化配置
    attention_l1_reg: float = 0.001        # L1正则化系数
    attention_l2_reg: float = 0.0001       # L2正则化系数
    temporal_consistency_weight: float = 0.1  # 时序一致性权重


@dataclass
class NetworkConfig:
    """网络架构配置"""

    # 特征提取网络
    agv_feature_dim: int = 6               # AGV特征维度
    task_feature_dim: int = 5              # 任务特征维度
    feature_hidden_dims: List[int] = None  # 特征提取隐藏层维度

    # 策略网络配置
    policy_hidden_dims: List[int] = None   # 策略网络隐藏层维度
    policy_activation: str = "relu"        # 策略网络激活函数
    policy_output_activation: str = "softmax"  # 策略输出激活函数

    # 价值网络配置
    value_hidden_dims: List[int] = None    # 价值网络隐藏层维度
    value_activation: str = "relu"         # 价值网络激活函数
    value_output_activation: str = "linear"  # 价值输出激活函数

    # 共享网络配置
    shared_backbone: bool = False          # 是否使用共享主干网络
    shared_hidden_dims: List[int] = None   # 共享网络隐藏层维度

    def __post_init__(self):
        """初始化后处理"""
        if self.feature_hidden_dims is None:
            self.feature_hidden_dims = [64]

        if self.policy_hidden_dims is None:
            self.policy_hidden_dims = [512, 256]

        if self.value_hidden_dims is None:
            self.value_hidden_dims = [512, 256]

        if self.shared_hidden_dims is None:
            self.shared_hidden_dims = [256, 128]


@dataclass
class MAPPOConfig:
    """MAPPO算法配置"""

    # PPO核心参数
    learning_rate: float = 3e-4            # 学习率
    gamma: float = 0.99                    # 折扣因子
    gae_lambda: float = 0.95               # GAE参数
    clip_param: float = 0.2                # PPO裁剪参数
    value_loss_coeff: float = 0.5          # 价值损失系数
    entropy_coeff: float = 0.01            # 熵正则化系数

    # 训练参数
    num_epochs: int = 10                   # 每次更新的epoch数
    batch_size: int = 128                  # 批次大小
    buffer_size: int = 4000                # 缓冲区大小
    max_grad_norm: float = 0.5             # 梯度裁剪阈值

    # 网络更新参数
    policy_lr: float = 3e-4                # 策略网络学习率
    value_lr: float = 1e-3                 # 价值网络学习率
    attention_lr: float = 1e-4             # 注意力机制学习率

    # 优化器配置
    optimizer: str = "adam"                # 优化器类型
    adam_eps: float = 1e-5                 # Adam优化器epsilon
    weight_decay: float = 0.0              # 权重衰减

    # 学习率调度
    use_lr_scheduler: bool = True          # 是否使用学习率调度
    lr_scheduler_type: str = "cosine"      # 学习率调度类型
    warmup_steps: int = 1000               # 预热步数


@dataclass
class ModelConfig:
    """完整模型配置"""

    # 子配置
    attention: AttentionConfig = None
    network: NetworkConfig = None
    mappo: MAPPOConfig = None

    # 设备配置
    device: str = "cuda"                   # 计算设备
    mixed_precision: bool = True           # 是否使用混合精度

    # 模型保存配置
    save_interval: int = 1000              # 模型保存间隔
    max_checkpoints: int = 5               # 最大检查点数量
    save_best_only: bool = True            # 是否只保存最佳模型

    def __post_init__(self):
        """初始化后处理"""
        if self.attention is None:
            self.attention = AttentionConfig()

        if self.network is None:
            self.network = NetworkConfig()

        if self.mappo is None:
            self.mappo = MAPPOConfig()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "attention": self.attention.__dict__,
            "network": self.network.__dict__,
            "mappo": self.mappo.__dict__,
            "device": self.device,
            "mixed_precision": self.mixed_precision,
            "save_interval": self.save_interval,
            "max_checkpoints": self.max_checkpoints,
            "save_best_only": self.save_best_only
        }


# 默认模型配置实例
DEFAULT_MODEL_CONFIG = ModelConfig()

# 课程学习阶段的模型配置
CURRICULUM_MODEL_CONFIGS = {
    "stage1": ModelConfig(
        mappo=MAPPOConfig(
            learning_rate=1e-3,
            buffer_size=2000,
            batch_size=64
        )
    ),
    "stage2": ModelConfig(
        mappo=MAPPOConfig(
            learning_rate=5e-4,
            buffer_size=3000,
            batch_size=96
        )
    ),
    "stage3": ModelConfig(
        mappo=MAPPOConfig(
            learning_rate=3e-4,
            buffer_size=4000,
            batch_size=128
        )
    )
}