"""
超参数优化主程序
执行系统性的超参数优化，包括注意力机制参数、训练参数和网络架构参数的调优
"""

import sys
import os
import argparse
import json
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.optimization.hyperparameter_optimizer import HyperparameterOptimizer, HyperparameterSpace
from src.optimization.hyperparameter_evaluator import HyperparameterEvaluator
from src.optimization.optimization_visualizer import OptimizationVisualizer
from config.mappo_config import MAPPOConfig


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="超参数优化程序")
    
    # 优化方法
    parser.add_argument("--method", type=str, default="optuna",
                       choices=["optuna", "grid", "random"],
                       help="优化方法")
    
    # 优化参数
    parser.add_argument("--n_trials", type=int, default=50,
                       help="试验次数")
    parser.add_argument("--timeout", type=int, default=3600,
                       help="超时时间（秒）")
    parser.add_argument("--n_jobs", type=int, default=1,
                       help="并行作业数")
    
    # 评估参数
    parser.add_argument("--eval_episodes", type=int, default=10,
                       help="评估回合数")
    parser.add_argument("--max_training_steps", type=int, default=100,
                       help="最大训练步数")
    
    # 搜索空间
    parser.add_argument("--search_space", type=str, default="default",
                       choices=["default", "attention_focused", "training_focused", "architecture_focused"],
                       help="搜索空间类型")
    
    # 输出设置
    parser.add_argument("--output_dir", type=str, default="./results/hyperparameter_optimization",
                       help="输出目录")
    parser.add_argument("--experiment_name", type=str, default=None,
                       help="实验名称")
    
    # 其他设置
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子")
    parser.add_argument("--device", type=str, default="cpu",
                       help="计算设备")
    parser.add_argument("--visualize", action="store_true",
                       help="生成可视化结果")
    
    return parser.parse_args()


def create_search_space(space_type: str) -> HyperparameterSpace:
    """创建搜索空间"""
    if space_type == "default":
        return HyperparameterSpace()
    
    elif space_type == "attention_focused":
        # 专注于注意力机制参数的搜索空间
        return HyperparameterSpace(
            attention_feature_dim=[32, 64, 128, 256],
            attention_num_heads=[4, 8, 16, 32],
            attention_dropout=[0.0, 0.1, 0.2, 0.3],
            attention_pos_dim=[16, 32, 64, 128],
            near_threshold=[1.0, 2.0, 3.0, 5.0, 8.0],
            far_threshold=[5.0, 8.0, 10.0, 15.0, 20.0],
            temperature_scale=[0.5, 1.0, 2.0, 3.0, 5.0],
            # 固定其他参数
            hidden_dim=[256],
            learning_rate=[3e-4],
            batch_size=[256]
        )
    
    elif space_type == "training_focused":
        # 专注于训练参数的搜索空间
        return HyperparameterSpace(
            learning_rate=[1e-5, 5e-5, 1e-4, 3e-4, 5e-4, 1e-3, 3e-3],
            batch_size=[32, 64, 128, 256, 512, 1024],
            num_epochs=[3, 5, 8, 10, 15, 20, 30],
            clip_param=[0.05, 0.1, 0.15, 0.2, 0.25, 0.3],
            entropy_coeff=[0.0001, 0.001, 0.01, 0.05, 0.1, 0.2],
            value_loss_coeff=[0.1, 0.25, 0.5, 0.75, 1.0, 1.5],
            # 固定其他参数
            attention_feature_dim=[64],
            attention_num_heads=[8],
            hidden_dim=[256]
        )
    
    elif space_type == "architecture_focused":
        # 专注于网络架构参数的搜索空间
        return HyperparameterSpace(
            hidden_dim=[64, 128, 256, 512, 1024],
            num_layers=[1, 2, 3, 4, 5],
            activation=['relu', 'tanh', 'gelu', 'swish'],
            attention_feature_dim=[16, 32, 64, 128, 256],
            attention_num_heads=[2, 4, 8, 16],
            # 固定其他参数
            learning_rate=[3e-4],
            batch_size=[256],
            num_epochs=[10]
        )
    
    else:
        raise ValueError(f"未知的搜索空间类型: {space_type}")


def create_objective_function(evaluator: HyperparameterEvaluator):
    """创建目标函数"""
    def objective(hyperparams: Dict[str, Any]) -> float:
        """
        目标函数：评估超参数配置的性能
        
        Args:
            hyperparams: 超参数字典
            
        Returns:
            score: 性能评分（越高越好）
        """
        try:
            # 评估超参数
            metrics = evaluator.evaluate_hyperparameters(hyperparams)
            
            # 返回总体评分
            return metrics.overall_score
            
        except Exception as e:
            print(f"评估超参数时出错: {e}")
            return 0.0  # 返回最低分
    
    return objective


def run_optimization(args):
    """运行超参数优化"""
    print("=" * 80)
    print("基于融合双层注意力机制的MAPPO多AGV协同调度系统")
    print("超参数优化程序")
    print("=" * 80)
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = args.experiment_name or f"hyperopt_{args.method}_{timestamp}"
    output_dir = os.path.join(args.output_dir, experiment_name)
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"实验名称: {experiment_name}")
    print(f"输出目录: {output_dir}")
    print(f"优化方法: {args.method}")
    print(f"试验次数: {args.n_trials}")
    print(f"搜索空间: {args.search_space}")
    
    # 创建搜索空间
    search_space = create_search_space(args.search_space)
    print(f"搜索空间参数数量: {len([k for k, v in search_space.__dict__.items() if v is not None])}")
    
    # 创建评估器
    base_config = MAPPOConfig()
    evaluator = HyperparameterEvaluator(
        base_config=base_config,
        evaluation_episodes=args.eval_episodes,
        max_training_steps=args.max_training_steps,
        device=args.device
    )
    
    # 创建优化器
    optimizer = HyperparameterOptimizer(
        search_space=search_space,
        optimization_method=args.method,
        n_trials=args.n_trials,
        n_jobs=args.n_jobs,
        random_seed=args.seed
    )
    
    # 创建目标函数
    objective_function = create_objective_function(evaluator)
    
    print("\n开始超参数优化...")
    print("-" * 60)
    
    # 执行优化
    result = optimizer.optimize(
        objective_function=objective_function,
        direction="maximize",
        timeout=args.timeout
    )
    
    print("\n优化完成！")
    print("-" * 60)
    print(f"最佳评分: {result.best_score:.4f}")
    print(f"总试验次数: {result.total_trials}")
    print(f"优化时间: {result.optimization_time:.2f}秒")
    
    print("\n最佳超参数配置:")
    for param, value in result.best_params.items():
        print(f"  {param}: {value}")
    
    # 保存结果
    results_file = os.path.join(output_dir, "optimization_results.json")
    optimizer.save_results(result, results_file)
    
    # 保存最佳配置（转换numpy类型为Python原生类型）
    best_config_file = os.path.join(output_dir, "best_hyperparameters.json")
    best_params_serializable = {}
    for k, v in result.best_params.items():
        if hasattr(v, 'item'):  # numpy类型
            best_params_serializable[k] = v.item()
        else:
            best_params_serializable[k] = v

    with open(best_config_file, 'w', encoding='utf-8') as f:
        json.dump(best_params_serializable, f, indent=2, ensure_ascii=False)
    
    print(f"\n结果已保存:")
    print(f"  详细结果: {results_file}")
    print(f"  最佳配置: {best_config_file}")
    
    # 生成可视化
    if args.visualize:
        print("\n生成可视化结果...")
        try:
            visualizer = OptimizationVisualizer()
            
            # 优化历史图
            history_plot = os.path.join(output_dir, "optimization_history.png")
            visualizer.plot_optimization_history(result.optimization_history, history_plot)
            
            # 参数重要性图
            importance_plot = os.path.join(output_dir, "parameter_importance.png")
            visualizer.plot_parameter_importance(result.optimization_history, importance_plot)
            
            # 收敛分析图
            convergence_plot = os.path.join(output_dir, "convergence_analysis.png")
            visualizer.plot_convergence_analysis(result.optimization_history, convergence_plot)
            
            print(f"  可视化结果已保存到: {output_dir}")
            
        except Exception as e:
            print(f"生成可视化时出错: {e}")
    
    # 生成优化报告
    report_file = os.path.join(output_dir, "optimization_report.md")
    generate_optimization_report(result, args, report_file)
    print(f"  优化报告: {report_file}")
    
    print(f"\n🎉 超参数优化完成！")
    print(f"📊 最佳性能评分: {result.best_score:.4f}")
    print(f"📁 结果目录: {output_dir}")
    
    return result


def generate_optimization_report(result, args, report_file: str):
    """生成优化报告"""
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 超参数优化报告\n\n")
        
        f.write("## 优化配置\n\n")
        f.write(f"- **优化方法**: {args.method}\n")
        f.write(f"- **试验次数**: {result.total_trials}\n")
        f.write(f"- **搜索空间**: {args.search_space}\n")
        f.write(f"- **优化时间**: {result.optimization_time:.2f}秒\n")
        f.write(f"- **随机种子**: {args.seed}\n\n")
        
        f.write("## 优化结果\n\n")
        f.write(f"- **最佳评分**: {result.best_score:.4f}\n")
        f.write(f"- **收敛试验**: {result.convergence_info.get('convergence_trial', 'N/A')}\n")
        f.write(f"- **改进率**: {result.convergence_info.get('improvement_rate', 0):.2%}\n\n")
        
        f.write("## 最佳超参数配置\n\n")
        f.write("```json\n")
        # 转换numpy类型为Python原生类型
        best_params_serializable = {}
        for k, v in result.best_params.items():
            if hasattr(v, 'item'):  # numpy类型
                best_params_serializable[k] = v.item()
            else:
                best_params_serializable[k] = v
        f.write(json.dumps(best_params_serializable, indent=2, ensure_ascii=False))
        f.write("\n```\n\n")
        
        f.write("## 收敛分析\n\n")
        convergence_info = result.convergence_info
        f.write(f"- **评分标准差**: {convergence_info.get('score_std', 0):.4f}\n")
        f.write(f"- **评分范围**: {convergence_info.get('score_range', [0, 0])}\n")
        f.write(f"- **总试验数**: {convergence_info.get('total_trials', 0)}\n\n")
        
        f.write("## 建议\n\n")
        if result.best_score > 0.8:
            f.write("✅ **优秀**: 超参数配置表现优秀，建议直接使用。\n")
        elif result.best_score > 0.6:
            f.write("⚠️ **良好**: 超参数配置表现良好，可考虑进一步优化。\n")
        else:
            f.write("❌ **需要改进**: 超参数配置表现不佳，建议扩大搜索空间或调整优化策略。\n")


def main():
    """主函数"""
    args = parse_args()
    
    try:
        result = run_optimization(args)
        return 0
    except Exception as e:
        print(f"\n❌ 超参数优化失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
