# 超参数优化报告

## 优化配置

- **优化方法**: optuna
- **试验次数**: 25
- **搜索空间**: default
- **优化时间**: 2.65秒
- **随机种子**: 42

## 优化结果

- **最佳评分**: 0.4801
- **收敛试验**: 13
- **改进率**: 12.50%

## 最佳超参数配置

```json
{
  "attention_feature_dim": 32,
  "attention_num_heads": 8,
  "attention_dropout": 0.0,
  "attention_pos_dim": 64,
  "hidden_dim": 512,
  "num_layers": 4,
  "activation": "relu",
  "learning_rate": 0.0005,
  "batch_size": 512,
  "num_epochs": 5,
  "clip_param": 0.1,
  "entropy_coeff": 0.1,
  "value_loss_coeff": 1.0,
  "near_threshold": 2.0,
  "far_threshold": 15.0,
  "temperature_scale": 3.0
}
```

## 收敛分析

- **评分标准差**: 0.0031
- **评分范围**: [np.float64(0.46719944196318197), np.float64(0.4800890260550368)]
- **总试验数**: 25

## 建议

❌ **需要改进**: 超参数配置表现不佳，建议扩大搜索空间或调整优化策略。
