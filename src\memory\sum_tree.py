"""
SumTree数据结构
用于优先级经验回放的高效采样实现
"""

import numpy as np
from typing import Union, Tuple, List


class SumTree:
    """
    SumTree数据结构
    支持O(log n)时间复杂度的优先级采样和更新
    """
    
    def __init__(self, capacity: int):
        """
        初始化SumTree
        
        Args:
            capacity: 树的容量（叶子节点数量）
        """
        self.capacity = capacity
        self.tree = np.zeros(2 * capacity - 1)  # 完全二叉树数组表示
        self.data = np.zeros(capacity, dtype=object)  # 存储实际数据
        self.write_pointer = 0  # 写入指针
        self.n_entries = 0  # 当前存储的条目数
        
    def _propagate(self, idx: int, change: float):
        """
        向上传播优先级变化（迭代版本，避免递归深度问题）

        Args:
            idx: 节点索引
            change: 优先级变化量
        """
        current = idx
        while current != 0:
            parent = (current - 1) // 2
            self.tree[parent] += change
            current = parent
    
    def _retrieve(self, idx: int, s: float) -> int:
        """
        根据累积优先级检索叶子节点（迭代版本，避免递归深度问题）

        Args:
            idx: 当前节点索引
            s: 目标累积优先级

        Returns:
            leaf_idx: 叶子节点索引
        """
        current = idx
        remaining = s

        while True:
            left = 2 * current + 1
            right = left + 1

            # 如果是叶子节点
            if left >= len(self.tree):
                return current

            # 选择左子树或右子树
            if remaining <= self.tree[left]:
                current = left
            else:
                remaining -= self.tree[left]
                current = right
    
    def total(self) -> float:
        """
        获取总优先级
        
        Returns:
            total_priority: 总优先级
        """
        return self.tree[0]
    
    def add(self, priority: float, data: object):
        """
        添加新的数据项
        
        Args:
            priority: 优先级
            data: 数据对象
        """
        idx = self.write_pointer + self.capacity - 1
        
        self.data[self.write_pointer] = data
        self.update(idx, priority)
        
        self.write_pointer += 1
        if self.write_pointer >= self.capacity:
            self.write_pointer = 0
        
        if self.n_entries < self.capacity:
            self.n_entries += 1
    
    def update(self, idx: int, priority: float):
        """
        更新节点优先级
        
        Args:
            idx: 节点索引
            priority: 新的优先级
        """
        change = priority - self.tree[idx]
        self.tree[idx] = priority
        self._propagate(idx, change)
    
    def get(self, s: float) -> Tuple[int, float, object]:
        """
        根据累积优先级获取数据
        
        Args:
            s: 目标累积优先级
            
        Returns:
            idx: 树中的索引
            priority: 优先级
            data: 数据对象
        """
        idx = self._retrieve(0, s)
        data_idx = idx - self.capacity + 1
        
        return idx, self.tree[idx], self.data[data_idx]
    
    def sample(self, n: int) -> Tuple[List[int], List[float], List[object]]:
        """
        批量采样
        
        Args:
            n: 采样数量
            
        Returns:
            indices: 树索引列表
            priorities: 优先级列表
            data_list: 数据列表
        """
        indices = []
        priorities = []
        data_list = []
        
        priority_segment = self.total() / n
        
        for i in range(n):
            a = priority_segment * i
            b = priority_segment * (i + 1)
            
            s = np.random.uniform(a, b)
            idx, priority, data = self.get(s)
            
            indices.append(idx)
            priorities.append(priority)
            data_list.append(data)
        
        return indices, priorities, data_list
    
    def get_min_priority(self) -> float:
        """
        获取最小优先级
        
        Returns:
            min_priority: 最小优先级
        """
        # 在叶子节点中查找最小值
        leaf_start = self.capacity - 1
        leaf_end = leaf_start + self.n_entries
        
        if self.n_entries == 0:
            return 0.0
        
        return np.min(self.tree[leaf_start:leaf_end])
    
    def get_max_priority(self) -> float:
        """
        获取最大优先级
        
        Returns:
            max_priority: 最大优先级
        """
        # 在叶子节点中查找最大值
        leaf_start = self.capacity - 1
        leaf_end = leaf_start + self.n_entries
        
        if self.n_entries == 0:
            return 0.0
        
        return np.max(self.tree[leaf_start:leaf_end])
    
    def __len__(self) -> int:
        """
        获取当前存储的条目数
        
        Returns:
            n_entries: 条目数
        """
        return self.n_entries
    
    def is_full(self) -> bool:
        """
        检查是否已满
        
        Returns:
            is_full: 是否已满
        """
        return self.n_entries >= self.capacity
    
    def get_stats(self) -> dict:
        """
        获取统计信息
        
        Returns:
            stats: 统计信息字典
        """
        if self.n_entries == 0:
            return {
                'total_priority': 0.0,
                'min_priority': 0.0,
                'max_priority': 0.0,
                'mean_priority': 0.0,
                'n_entries': 0,
                'capacity': self.capacity,
                'utilization': 0.0
            }
        
        leaf_start = self.capacity - 1
        leaf_end = leaf_start + self.n_entries
        leaf_priorities = self.tree[leaf_start:leaf_end]
        
        return {
            'total_priority': self.total(),
            'min_priority': np.min(leaf_priorities),
            'max_priority': np.max(leaf_priorities),
            'mean_priority': np.mean(leaf_priorities),
            'n_entries': self.n_entries,
            'capacity': self.capacity,
            'utilization': self.n_entries / self.capacity
        }


class SegmentTree:
    """
    线段树实现（SumTree的替代实现）
    提供更灵活的区间查询功能
    """
    
    def __init__(self, capacity: int, operation=np.add, neutral_element=0.0):
        """
        初始化线段树
        
        Args:
            capacity: 容量
            operation: 操作函数（默认为加法）
            neutral_element: 中性元素（默认为0）
        """
        assert capacity > 0 and capacity & (capacity - 1) == 0, "容量必须是2的幂"
        
        self.capacity = capacity
        self.operation = operation
        self.neutral_element = neutral_element
        self.tree = [neutral_element for _ in range(2 * capacity)]
    
    def _reduce_helper(self, start: int, end: int, node: int, node_start: int, node_end: int) -> float:
        """
        区间查询辅助函数
        
        Args:
            start: 查询起始位置
            end: 查询结束位置
            node: 当前节点
            node_start: 节点覆盖起始位置
            node_end: 节点覆盖结束位置
            
        Returns:
            result: 查询结果
        """
        if start == node_start and end == node_end:
            return self.tree[node]
        
        mid = (node_start + node_end) // 2
        
        if end <= mid:
            return self._reduce_helper(start, end, 2 * node, node_start, mid)
        elif start > mid:
            return self._reduce_helper(start, end, 2 * node + 1, mid + 1, node_end)
        else:
            left_result = self._reduce_helper(start, mid, 2 * node, node_start, mid)
            right_result = self._reduce_helper(mid + 1, end, 2 * node + 1, mid + 1, node_end)
            return self.operation(left_result, right_result)
    
    def reduce(self, start: int = 0, end: int = None) -> float:
        """
        区间查询
        
        Args:
            start: 起始位置
            end: 结束位置
            
        Returns:
            result: 查询结果
        """
        if end is None:
            end = self.capacity - 1
        
        return self._reduce_helper(start, end, 1, 0, self.capacity - 1)
    
    def __setitem__(self, idx: int, val: float):
        """
        设置值
        
        Args:
            idx: 索引
            val: 值
        """
        idx += self.capacity
        self.tree[idx] = val
        
        idx //= 2
        while idx >= 1:
            self.tree[idx] = self.operation(self.tree[2 * idx], self.tree[2 * idx + 1])
            idx //= 2
    
    def __getitem__(self, idx: int) -> float:
        """
        获取值
        
        Args:
            idx: 索引
            
        Returns:
            value: 值
        """
        return self.tree[self.capacity + idx]
