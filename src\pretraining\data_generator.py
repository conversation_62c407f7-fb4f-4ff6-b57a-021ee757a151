"""
注意力机制预训练数据生成器
生成用于预训练双层注意力机制的模拟数据
"""

import torch
import numpy as np
import random
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..environment.agv_entity import AGVEntity, AGVStatus
from ..environment.task_manager import Task, TaskStatus
from config.env_config import EnvironmentConfig


class PretrainingScenarioType(Enum):
    """预训练场景类型"""
    SIMPLE_TASK_ALLOCATION = "simple_task_allocation"
    COMPLEX_TASK_ALLOCATION = "complex_task_allocation"
    COLLABORATION_BASIC = "collaboration_basic"
    COLLABORATION_ADVANCED = "collaboration_advanced"
    MIXED_SCENARIO = "mixed_scenario"


@dataclass
class PretrainingBatch:
    """预训练批次数据"""
    agv_features: torch.Tensor  # [batch_size, num_agvs, feature_dim]
    task_features: torch.Tensor  # [batch_size, num_tasks, feature_dim]
    agv_positions: torch.Tensor  # [batch_size, num_agvs, 2]
    task_positions: torch.Tensor  # [batch_size, num_tasks, 2]
    
    # 监督学习标签
    optimal_task_allocation: torch.Tensor  # [batch_size, num_agvs, num_tasks]
    collaboration_targets: torch.Tensor  # [batch_size, num_agvs, num_agvs]
    
    # 元数据
    scenario_types: List[PretrainingScenarioType]
    difficulty_levels: torch.Tensor  # [batch_size]
    
    def to(self, device: torch.device):
        """移动到指定设备"""
        return PretrainingBatch(
            agv_features=self.agv_features.to(device),
            task_features=self.task_features.to(device),
            agv_positions=self.agv_positions.to(device),
            task_positions=self.task_positions.to(device),
            optimal_task_allocation=self.optimal_task_allocation.to(device),
            collaboration_targets=self.collaboration_targets.to(device),
            scenario_types=self.scenario_types,
            difficulty_levels=self.difficulty_levels.to(device)
        )


class AttentionPretrainingDataGenerator:
    """注意力机制预训练数据生成器"""
    
    def __init__(self, config: EnvironmentConfig, feature_dim: int = 64):
        """
        初始化数据生成器
        
        Args:
            config: 环境配置
            feature_dim: 特征维度
        """
        self.config = config
        self.feature_dim = feature_dim
        
        # 场景配置
        self.scenario_configs = {
            PretrainingScenarioType.SIMPLE_TASK_ALLOCATION: {
                'num_agvs': (2, 3),
                'num_tasks': (2, 4),
                'task_complexity': 'low',
                'collaboration_required': False
            },
            PretrainingScenarioType.COMPLEX_TASK_ALLOCATION: {
                'num_agvs': (3, 5),
                'num_tasks': (4, 8),
                'task_complexity': 'high',
                'collaboration_required': False
            },
            PretrainingScenarioType.COLLABORATION_BASIC: {
                'num_agvs': (3, 4),
                'num_tasks': (3, 5),
                'task_complexity': 'medium',
                'collaboration_required': True,
                'collaboration_intensity': 'low'
            },
            PretrainingScenarioType.COLLABORATION_ADVANCED: {
                'num_agvs': (4, 6),
                'num_tasks': (5, 10),
                'task_complexity': 'high',
                'collaboration_required': True,
                'collaboration_intensity': 'high'
            },
            PretrainingScenarioType.MIXED_SCENARIO: {
                'num_agvs': (3, 6),
                'num_tasks': (4, 10),
                'task_complexity': 'mixed',
                'collaboration_required': True,
                'collaboration_intensity': 'mixed'
            }
        }
        
        # 随机种子
        self.rng = np.random.RandomState(42)
    
    def generate_batch(self, 
                      batch_size: int,
                      scenario_type: Optional[PretrainingScenarioType] = None,
                      difficulty_range: Tuple[float, float] = (0.1, 1.0)) -> PretrainingBatch:
        """
        生成预训练批次数据
        
        Args:
            batch_size: 批次大小
            scenario_type: 场景类型，None表示随机选择
            difficulty_range: 难度范围[0, 1]
            
        Returns:
            batch: 预训练批次数据
        """
        batch_data = {
            'agv_features': [],
            'task_features': [],
            'agv_positions': [],
            'task_positions': [],
            'optimal_task_allocation': [],
            'collaboration_targets': [],
            'scenario_types': [],
            'difficulty_levels': []
        }
        
        for _ in range(batch_size):
            # 选择场景类型
            if scenario_type is None:
                current_scenario = self.rng.choice(list(PretrainingScenarioType))
            else:
                current_scenario = scenario_type
            
            # 生成单个样本
            sample = self._generate_single_sample(current_scenario, difficulty_range)
            
            # 添加到批次
            for key, value in sample.items():
                batch_data[key].append(value)
        
        # 转换为张量
        return self._convert_to_batch(batch_data)
    
    def _generate_single_sample(self, 
                               scenario_type: PretrainingScenarioType,
                               difficulty_range: Tuple[float, float]) -> Dict[str, Any]:
        """
        生成单个训练样本
        
        Args:
            scenario_type: 场景类型
            difficulty_range: 难度范围
            
        Returns:
            sample: 单个样本数据
        """
        config = self.scenario_configs[scenario_type]
        
        # 生成场景参数
        num_agvs = self.rng.randint(config['num_agvs'][0], config['num_agvs'][1] + 1)
        num_tasks = self.rng.randint(config['num_tasks'][0], config['num_tasks'][1] + 1)
        difficulty = self.rng.uniform(difficulty_range[0], difficulty_range[1])
        
        # 生成AGV数据
        agvs, agv_features, agv_positions = self._generate_agvs(num_agvs, difficulty)
        
        # 生成任务数据
        tasks, task_features, task_positions = self._generate_tasks(num_tasks, difficulty)
        
        # 计算最优分配
        optimal_allocation = self._compute_optimal_task_allocation(
            agvs, tasks, agv_positions, task_positions
        )
        
        # 计算协作目标
        collaboration_targets = self._compute_collaboration_targets(
            agvs, agv_positions, config.get('collaboration_required', False)
        )
        
        return {
            'agv_features': agv_features,
            'task_features': task_features,
            'agv_positions': agv_positions,
            'task_positions': task_positions,
            'optimal_task_allocation': optimal_allocation,
            'collaboration_targets': collaboration_targets,
            'scenario_types': scenario_type,
            'difficulty_levels': difficulty
        }
    
    def _generate_agvs(self, num_agvs: int, difficulty: float) -> Tuple[List[AGVEntity], torch.Tensor, torch.Tensor]:
        """
        生成AGV数据
        
        Args:
            num_agvs: AGV数量
            difficulty: 难度级别
            
        Returns:
            agvs: AGV实体列表
            features: AGV特征张量
            positions: AGV位置张量
        """
        agvs = []
        features = []
        positions = []
        
        for i in range(num_agvs):
            # 生成随机位置
            x = self.rng.randint(0, self.config.map_width)
            y = self.rng.randint(0, self.config.map_height)
            position = (x, y)
            
            # 创建AGV实体
            agv = AGVEntity(i, position, capacity=25)
            
            # 根据难度设置AGV状态
            if difficulty > 0.7:
                # 高难度：AGV可能有载重或特殊状态
                agv.current_load = self.rng.randint(0, agv.capacity)
                agv.status = self.rng.choice([AGVStatus.IDLE, AGVStatus.MOVING, AGVStatus.LOADING])
            else:
                # 低难度：AGV大多空闲
                agv.current_load = 0
                agv.status = AGVStatus.IDLE
            
            agvs.append(agv)
            
            # 生成特征向量
            feature = self._encode_agv_features(agv, difficulty)
            features.append(feature)
            positions.append([x, y])
        
        # 填充到最大数量
        max_agvs = 6  # 最大AGV数量
        while len(features) < max_agvs:
            features.append(torch.zeros(self.feature_dim))
            positions.append([0, 0])
        
        return agvs, torch.stack(features), torch.tensor(positions, dtype=torch.float32)
    
    def _generate_tasks(self, num_tasks: int, difficulty: float) -> Tuple[List[Task], torch.Tensor, torch.Tensor]:
        """
        生成任务数据
        
        Args:
            num_tasks: 任务数量
            difficulty: 难度级别
            
        Returns:
            tasks: 任务列表
            features: 任务特征张量
            positions: 任务位置张量
        """
        tasks = []
        features = []
        positions = []
        
        for i in range(num_tasks):
            # 生成取货和送货位置
            pickup_x = self.rng.randint(0, self.config.map_width)
            pickup_y = self.rng.randint(0, self.config.map_height)
            delivery_x = self.rng.randint(0, self.config.map_width)
            delivery_y = self.rng.randint(0, self.config.map_height)
            
            pickup_pos = (pickup_x, pickup_y)
            delivery_pos = (delivery_x, delivery_y)
            
            # 创建任务（使用实际的Task类构造函数）
            task = Task(
                task_id=i,
                position=pickup_pos,  # 使用取货位置作为任务位置
                weight=self.rng.randint(1, 10)  # 随机重量
            )

            # 添加额外属性（模拟）
            task.pickup_position = pickup_pos
            task.delivery_position = delivery_pos
            task.priority = self.rng.uniform(0.1, 1.0) if difficulty > 0.5 else 0.5
            task.deadline = self.rng.randint(50, 200) if difficulty > 0.3 else 200
            
            tasks.append(task)
            
            # 生成特征向量
            feature = self._encode_task_features(task, difficulty)
            features.append(feature)
            
            # 使用取货位置作为任务位置
            positions.append([pickup_x, pickup_y])
        
        # 填充到最大数量
        max_tasks = 10  # 最大任务数量
        while len(features) < max_tasks:
            features.append(torch.zeros(self.feature_dim))
            positions.append([0, 0])
        
        return tasks, torch.stack(features), torch.tensor(positions, dtype=torch.float32)
    
    def _encode_agv_features(self, agv: AGVEntity, difficulty: float) -> torch.Tensor:
        """
        编码AGV特征
        
        Args:
            agv: AGV实体
            difficulty: 难度级别
            
        Returns:
            features: AGV特征向量
        """
        features = torch.zeros(self.feature_dim)
        
        # 基础特征
        features[0] = agv.position[0] / self.config.map_width  # 归一化x坐标
        features[1] = agv.position[1] / self.config.map_height  # 归一化y坐标
        features[2] = agv.current_load / agv.capacity  # 载重比例
        features[3] = 1.0 if agv.status == AGVStatus.IDLE else 0.0  # 是否空闲
        features[4] = 1.0 if agv.status == AGVStatus.MOVING else 0.0  # 是否移动
        features[5] = 1.0 if agv.status == AGVStatus.LOADING else 0.0  # 是否装载
        features[6] = 1.0 if agv.status == AGVStatus.UNLOADING else 0.0  # 是否卸载
        features[7] = difficulty  # 难度级别
        
        # 添加随机噪声以增加多样性
        noise = torch.randn(self.feature_dim - 8) * 0.1
        features[8:] = noise
        
        return features
    
    def _encode_task_features(self, task: Task, difficulty: float) -> torch.Tensor:
        """
        编码任务特征
        
        Args:
            task: 任务实体
            difficulty: 难度级别
            
        Returns:
            features: 任务特征向量
        """
        features = torch.zeros(self.feature_dim)
        
        # 基础特征
        pickup_pos = getattr(task, 'pickup_position', task.position)
        delivery_pos = getattr(task, 'delivery_position', task.position)
        priority = getattr(task, 'priority', 0.5)
        deadline = getattr(task, 'deadline', 200)

        features[0] = pickup_pos[0] / self.config.map_width  # 归一化取货x
        features[1] = pickup_pos[1] / self.config.map_height  # 归一化取货y
        features[2] = delivery_pos[0] / self.config.map_width  # 归一化送货x
        features[3] = delivery_pos[1] / self.config.map_height  # 归一化送货y
        features[4] = priority  # 任务优先级
        features[5] = deadline / 200.0  # 归一化截止时间
        features[6] = 1.0 if task.status == TaskStatus.PENDING else 0.0  # 是否待处理
        features[7] = difficulty  # 难度级别

        # 计算任务距离
        distance = np.sqrt((pickup_pos[0] - delivery_pos[0])**2 +
                          (pickup_pos[1] - delivery_pos[1])**2)
        features[8] = distance / np.sqrt(self.config.map_width**2 + self.config.map_height**2)
        
        # 添加随机噪声
        noise = torch.randn(self.feature_dim - 9) * 0.1
        features[9:] = noise
        
        return features

    def _compute_optimal_task_allocation(self,
                                       agvs: List[AGVEntity],
                                       tasks: List[Task],
                                       agv_positions: torch.Tensor,
                                       task_positions: torch.Tensor) -> torch.Tensor:
        """
        计算最优任务分配（监督学习标签）

        Args:
            agvs: AGV列表
            tasks: 任务列表
            agv_positions: AGV位置
            task_positions: 任务位置

        Returns:
            allocation: 最优分配矩阵 [num_agvs, num_tasks]
        """
        num_agvs = len(agvs)
        num_tasks = len(tasks)
        max_agvs = agv_positions.shape[0]
        max_tasks = task_positions.shape[0]

        allocation = torch.zeros(max_agvs, max_tasks)

        # 简化的最优分配：基于距离的贪心分配
        available_agvs = list(range(num_agvs))
        unassigned_tasks = list(range(num_tasks))

        while available_agvs and unassigned_tasks:
            best_agv = None
            best_task = None
            best_score = float('inf')

            for agv_idx in available_agvs:
                agv = agvs[agv_idx]
                if not agv.is_idle():
                    continue

                for task_idx in unassigned_tasks:
                    task = tasks[task_idx]

                    # 计算分配得分（距离 + 优先级）
                    distance = torch.norm(agv_positions[agv_idx] - task_positions[task_idx]).item()
                    priority_bonus = (1.0 - task.priority) * 10  # 高优先级任务得分更低
                    score = distance + priority_bonus

                    if score < best_score:
                        best_score = score
                        best_agv = agv_idx
                        best_task = task_idx

            if best_agv is not None and best_task is not None:
                allocation[best_agv, best_task] = 1.0
                available_agvs.remove(best_agv)
                unassigned_tasks.remove(best_task)
            else:
                break

        return allocation

    def _compute_collaboration_targets(self,
                                     agvs: List[AGVEntity],
                                     agv_positions: torch.Tensor,
                                     collaboration_required: bool) -> torch.Tensor:
        """
        计算协作目标（监督学习标签）

        Args:
            agvs: AGV列表
            agv_positions: AGV位置
            collaboration_required: 是否需要协作

        Returns:
            targets: 协作目标矩阵 [num_agvs, num_agvs]
        """
        max_agvs = agv_positions.shape[0]
        targets = torch.zeros(max_agvs, max_agvs)

        if not collaboration_required:
            return targets

        num_agvs = len(agvs)

        # 基于距离的协作强度
        for i in range(num_agvs):
            for j in range(num_agvs):
                if i != j:
                    distance = torch.norm(agv_positions[i] - agv_positions[j]).item()

                    # 距离越近，协作强度越高
                    if distance <= 3.0:  # 近距离协作
                        targets[i, j] = 0.8
                    elif distance <= 8.0:  # 中距离协作
                        targets[i, j] = 0.4
                    elif distance <= 15.0:  # 远距离协作
                        targets[i, j] = 0.2
                    else:
                        targets[i, j] = 0.0

        return targets

    def _convert_to_batch(self, batch_data: Dict[str, List]) -> PretrainingBatch:
        """
        将列表数据转换为批次张量

        Args:
            batch_data: 批次数据字典

        Returns:
            batch: 预训练批次对象
        """
        return PretrainingBatch(
            agv_features=torch.stack(batch_data['agv_features']),
            task_features=torch.stack(batch_data['task_features']),
            agv_positions=torch.stack(batch_data['agv_positions']),
            task_positions=torch.stack(batch_data['task_positions']),
            optimal_task_allocation=torch.stack(batch_data['optimal_task_allocation']),
            collaboration_targets=torch.stack(batch_data['collaboration_targets']),
            scenario_types=batch_data['scenario_types'],
            difficulty_levels=torch.tensor(batch_data['difficulty_levels'], dtype=torch.float32)
        )

    def generate_curriculum_data(self,
                               stage: str,
                               batch_size: int,
                               num_batches: int) -> List[PretrainingBatch]:
        """
        生成课程学习数据

        Args:
            stage: 学习阶段 ('easy', 'medium', 'hard')
            batch_size: 批次大小
            num_batches: 批次数量

        Returns:
            batches: 预训练批次列表
        """
        stage_configs = {
            'easy': {
                'scenario_types': [PretrainingScenarioType.SIMPLE_TASK_ALLOCATION],
                'difficulty_range': (0.1, 0.4)
            },
            'medium': {
                'scenario_types': [
                    PretrainingScenarioType.COMPLEX_TASK_ALLOCATION,
                    PretrainingScenarioType.COLLABORATION_BASIC
                ],
                'difficulty_range': (0.3, 0.7)
            },
            'hard': {
                'scenario_types': [
                    PretrainingScenarioType.COLLABORATION_ADVANCED,
                    PretrainingScenarioType.MIXED_SCENARIO
                ],
                'difficulty_range': (0.6, 1.0)
            }
        }

        config = stage_configs.get(stage, stage_configs['medium'])
        batches = []

        for _ in range(num_batches):
            scenario_type = self.rng.choice(config['scenario_types'])
            batch = self.generate_batch(
                batch_size=batch_size,
                scenario_type=scenario_type,
                difficulty_range=config['difficulty_range']
            )
            batches.append(batch)

        return batches

    def get_data_statistics(self, batches: List[PretrainingBatch]) -> Dict[str, Any]:
        """
        获取数据统计信息

        Args:
            batches: 预训练批次列表

        Returns:
            stats: 统计信息字典
        """
        total_samples = sum(batch.agv_features.shape[0] for batch in batches)

        # 收集所有难度级别
        all_difficulties = torch.cat([batch.difficulty_levels for batch in batches])

        # 收集所有场景类型
        all_scenarios = []
        for batch in batches:
            all_scenarios.extend(batch.scenario_types)

        scenario_counts = {}
        for scenario in all_scenarios:
            scenario_counts[scenario.value] = scenario_counts.get(scenario.value, 0) + 1

        return {
            'total_samples': total_samples,
            'num_batches': len(batches),
            'difficulty_mean': all_difficulties.mean().item(),
            'difficulty_std': all_difficulties.std().item(),
            'difficulty_range': (all_difficulties.min().item(), all_difficulties.max().item()),
            'scenario_distribution': scenario_counts,
            'avg_batch_size': total_samples / len(batches) if batches else 0
        }
