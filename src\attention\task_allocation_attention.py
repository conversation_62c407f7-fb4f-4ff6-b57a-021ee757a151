"""
第一层任务分配注意力机制
实现AGV对任务的注意力分配，包括查询键值生成、约束融合、稀疏化优化等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import math

from ..utils.state_representation import StateSpaceManager
from ..environment.agv_entity import AGVEntity
from ..environment.task_manager import Task, TaskStatus


@dataclass
class AttentionOutput:
    """注意力机制输出"""
    attention_weights: torch.Tensor  # 注意力权重 [batch_size, num_agvs, num_tasks]
    attended_features: torch.Tensor  # 加权特征 [batch_size, num_agvs, feature_dim]
    constraint_scores: torch.Tensor  # 约束分数 [batch_size, num_agvs, num_tasks]
    top_k_indices: torch.Tensor     # Top-K索引 [batch_size, num_agvs, k]
    metadata: Dict[str, Any]         # 元数据信息


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    
    def __init__(self, d_model: int, max_distance: float = 50.0):
        """
        初始化位置编码
        
        Args:
            d_model: 模型维度
            max_distance: 最大距离
        """
        super().__init__()
        self.d_model = d_model
        self.max_distance = max_distance
        
        # 创建位置编码矩阵
        pe = torch.zeros(int(max_distance) + 1, d_model)
        position = torch.arange(0, max_distance + 1, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe)
    
    def forward(self, distances: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            distances: 距离张量 [batch_size, num_agvs, num_tasks]
            
        Returns:
            position_encoding: 位置编码 [batch_size, num_agvs, num_tasks, d_model]
        """
        # 将距离限制在有效范围内
        distances = torch.clamp(distances, 0, self.max_distance).long()
        
        # 获取位置编码
        batch_size, num_agvs, num_tasks = distances.shape
        position_encoding = self.pe[distances]  # [batch_size, num_agvs, num_tasks, d_model]
        
        return position_encoding


class ConstraintFusion(nn.Module):
    """约束信息融合模块"""
    
    def __init__(self, feature_dim: int = 64):
        """
        初始化约束融合模块
        
        Args:
            feature_dim: 特征维度
        """
        super().__init__()
        self.feature_dim = feature_dim
        
        # 距离约束网络
        self.distance_net = nn.Sequential(
            nn.Linear(1, feature_dim // 4),
            nn.ReLU(),
            nn.Linear(feature_dim // 4, 1),
            nn.Sigmoid()
        )
        
        # 载重约束网络
        self.capacity_net = nn.Sequential(
            nn.Linear(2, feature_dim // 4),  # AGV当前载重 + 任务重量
            nn.ReLU(),
            nn.Linear(feature_dim // 4, 1),
            nn.Sigmoid()
        )
        
        # 优先级约束网络
        self.priority_net = nn.Sequential(
            nn.Linear(1, feature_dim // 4),
            nn.ReLU(),
            nn.Linear(feature_dim // 4, 1),
            nn.Sigmoid()
        )
        
        # 约束融合网络
        self.fusion_net = nn.Sequential(
            nn.Linear(3, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                distances: torch.Tensor, agv_loads: torch.Tensor, 
                task_weights: torch.Tensor, task_priorities: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_features: AGV特征 [batch_size, num_agvs, feature_dim]
            task_features: 任务特征 [batch_size, num_tasks, feature_dim]
            distances: 距离矩阵 [batch_size, num_agvs, num_tasks]
            agv_loads: AGV载重 [batch_size, num_agvs]
            task_weights: 任务重量 [batch_size, num_tasks]
            task_priorities: 任务优先级 [batch_size, num_tasks]
            
        Returns:
            constraint_scores: 约束分数 [batch_size, num_agvs, num_tasks]
        """
        batch_size, num_agvs, num_tasks = distances.shape
        
        # 距离约束 - 距离越近分数越高
        distance_scores = self.distance_net(distances.unsqueeze(-1)).squeeze(-1)
        
        # 载重约束 - 检查AGV是否能承载任务
        agv_loads_expanded = agv_loads.unsqueeze(2).expand(-1, -1, num_tasks)  # [batch_size, num_agvs, num_tasks]
        task_weights_expanded = task_weights.unsqueeze(1).expand(-1, num_agvs, -1)  # [batch_size, num_agvs, num_tasks]
        
        capacity_input = torch.stack([agv_loads_expanded, task_weights_expanded], dim=-1)
        capacity_scores = self.capacity_net(capacity_input).squeeze(-1)
        
        # 优先级约束 - 高优先级任务获得更高分数
        task_priorities_expanded = task_priorities.unsqueeze(1).expand(-1, num_agvs, -1)
        priority_scores = self.priority_net(task_priorities_expanded.unsqueeze(-1)).squeeze(-1)
        
        # 融合所有约束
        constraint_input = torch.stack([distance_scores, capacity_scores, priority_scores], dim=-1)
        constraint_scores = self.fusion_net(constraint_input).squeeze(-1)
        
        return constraint_scores


class MultiHeadTaskAllocationAttention(nn.Module):
    """多头任务分配注意力机制"""
    
    def __init__(self, feature_dim: int = 64, num_heads: int = 8, 
                 dropout: float = 0.1, top_k: int = 5):
        """
        初始化多头注意力机制
        
        Args:
            feature_dim: 特征维度
            num_heads: 注意力头数
            dropout: Dropout比率
            top_k: Top-K稀疏化参数
        """
        super().__init__()
        assert feature_dim % num_heads == 0
        
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.head_dim = feature_dim // num_heads
        self.top_k = top_k
        self.scale = math.sqrt(self.head_dim)
        
        # Query, Key, Value投影层
        self.query_projection = nn.Linear(feature_dim, feature_dim)
        self.key_projection = nn.Linear(feature_dim, feature_dim)
        self.value_projection = nn.Linear(feature_dim, feature_dim)
        
        # 输出投影层
        self.output_projection = nn.Linear(feature_dim, feature_dim)
        
        # 位置编码
        self.positional_encoding = PositionalEncoding(self.head_dim)
        
        # 约束融合
        self.constraint_fusion = ConstraintFusion(feature_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(feature_dim)
    
    def forward(self, agv_features: torch.Tensor, task_features: torch.Tensor,
                distances: torch.Tensor, agv_loads: torch.Tensor,
                task_weights: torch.Tensor, task_priorities: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> AttentionOutput:
        """
        前向传播
        
        Args:
            agv_features: AGV特征 [batch_size, num_agvs, feature_dim]
            task_features: 任务特征 [batch_size, num_tasks, feature_dim]
            distances: 距离矩阵 [batch_size, num_agvs, num_tasks]
            agv_loads: AGV载重 [batch_size, num_agvs]
            task_weights: 任务重量 [batch_size, num_tasks]
            task_priorities: 任务优先级 [batch_size, num_tasks]
            mask: 注意力掩码 [batch_size, num_agvs, num_tasks]
            
        Returns:
            attention_output: 注意力输出
        """
        batch_size, num_agvs, _ = agv_features.shape
        _, num_tasks, _ = task_features.shape
        
        # 生成Query, Key, Value
        queries = self.query_projection(agv_features)  # [batch_size, num_agvs, feature_dim]
        keys = self.key_projection(task_features)      # [batch_size, num_tasks, feature_dim]
        values = self.value_projection(task_features)  # [batch_size, num_tasks, feature_dim]
        
        # 重塑为多头格式
        queries = queries.view(batch_size, num_agvs, self.num_heads, self.head_dim)
        keys = keys.view(batch_size, num_tasks, self.num_heads, self.head_dim)
        values = values.view(batch_size, num_tasks, self.num_heads, self.head_dim)
        
        # 转置以便计算注意力
        queries = queries.transpose(1, 2)  # [batch_size, num_heads, num_agvs, head_dim]
        keys = keys.transpose(1, 2)        # [batch_size, num_heads, num_tasks, head_dim]
        values = values.transpose(1, 2)    # [batch_size, num_heads, num_tasks, head_dim]
        
        # 计算注意力分数
        attention_scores = torch.matmul(queries, keys.transpose(-2, -1)) / self.scale
        # [batch_size, num_heads, num_agvs, num_tasks]
        
        # 添加位置编码
        position_encoding = self.positional_encoding(distances)  # [batch_size, num_agvs, num_tasks, head_dim]
        position_encoding = position_encoding.unsqueeze(1).expand(-1, self.num_heads, -1, -1, -1)
        
        # 将位置编码加到注意力分数中
        position_scores = torch.sum(queries.unsqueeze(-2) * position_encoding, dim=-1) / self.scale
        attention_scores = attention_scores + position_scores
        
        # 计算约束分数
        constraint_scores = self.constraint_fusion(
            agv_features, task_features, distances, 
            agv_loads, task_weights, task_priorities
        )
        
        # 将约束分数加到注意力分数中
        constraint_scores_expanded = constraint_scores.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
        attention_scores = attention_scores + constraint_scores_expanded
        
        # 应用掩码
        if mask is not None:
            mask_expanded = mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            attention_scores = attention_scores.masked_fill(mask_expanded == 0, -1e9)
        
        # Top-K稀疏化
        if self.top_k < num_tasks:
            top_k_values, top_k_indices = torch.topk(attention_scores, self.top_k, dim=-1)

            # 创建稀疏掩码
            sparse_mask = torch.zeros_like(attention_scores)
            sparse_mask.scatter_(-1, top_k_indices, 1.0)

            # 应用稀疏掩码
            attention_scores = attention_scores.masked_fill(sparse_mask == 0, -1e9)
        else:
            top_k_indices = torch.arange(num_tasks, device=attention_scores.device).unsqueeze(0).unsqueeze(0).unsqueeze(0).expand(batch_size, self.num_heads, num_agvs, -1)

        # Softmax归一化
        attention_weights = F.softmax(attention_scores, dim=-1)

        # 处理掩码后的归一化问题
        if mask is not None:
            mask_expanded = mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            attention_weights = attention_weights * mask_expanded
            # 重新归一化
            attention_sums = attention_weights.sum(dim=-1, keepdim=True)
            attention_weights = attention_weights / (attention_sums + 1e-8)

        # 处理Top-K稀疏化后的归一化问题
        if self.top_k < num_tasks:
            # 重新归一化稀疏化后的注意力权重
            attention_sums = attention_weights.sum(dim=-1, keepdim=True)
            attention_weights = attention_weights / (attention_sums + 1e-8)

        attention_weights = self.dropout(attention_weights)
        
        # 计算加权特征
        attended_features = torch.matmul(attention_weights, values)
        # [batch_size, num_heads, num_agvs, head_dim]
        
        # 重塑回原始格式
        attended_features = attended_features.transpose(1, 2).contiguous().view(
            batch_size, num_agvs, self.feature_dim
        )
        
        # 输出投影
        attended_features = self.output_projection(attended_features)
        
        # 残差连接和层归一化
        attended_features = self.layer_norm(attended_features + agv_features)
        
        # 平均多头注意力权重用于输出
        final_attention_weights = attention_weights.mean(dim=1)  # [batch_size, num_agvs, num_tasks]
        final_top_k_indices = top_k_indices[:, 0, :, :]  # 取第一个头的索引 [batch_size, num_agvs, top_k]
        
        return AttentionOutput(
            attention_weights=final_attention_weights,
            attended_features=attended_features,
            constraint_scores=constraint_scores,
            top_k_indices=final_top_k_indices,
            metadata={
                'num_heads': self.num_heads,
                'top_k': self.top_k,
                'feature_dim': self.feature_dim
            }
        )


class TaskAllocationAttentionManager:
    """任务分配注意力机制管理器"""

    def __init__(self, feature_dim: int = 64, num_heads: int = 8,
                 dropout: float = 0.1, top_k: int = 5):
        """
        初始化注意力管理器

        Args:
            feature_dim: 特征维度
            num_heads: 注意力头数
            dropout: Dropout比率
            top_k: Top-K稀疏化参数
        """
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.top_k = top_k

        # 创建注意力机制
        self.attention = MultiHeadTaskAllocationAttention(
            feature_dim=feature_dim,
            num_heads=num_heads,
            dropout=dropout,
            top_k=top_k
        )

        # 统计信息
        self.stats = {
            'total_calls': 0,
            'avg_attention_entropy': 0.0,
            'avg_constraint_score': 0.0,
            'top_k_coverage': 0.0
        }

    def compute_attention(self, agvs: List[AGVEntity], tasks: List[Task],
                         agv_embeddings: torch.Tensor, task_embeddings: torch.Tensor,
                         state_manager: StateSpaceManager) -> AttentionOutput:
        """
        计算任务分配注意力

        Args:
            agvs: AGV列表
            tasks: 任务列表
            agv_embeddings: AGV嵌入特征
            task_embeddings: 任务嵌入特征
            state_manager: 状态管理器

        Returns:
            attention_output: 注意力输出
        """
        # 计算距离矩阵
        distances = self._compute_distance_matrix(agvs, tasks)

        # 提取载重和重量信息
        agv_loads = torch.tensor([agv.current_load / agv.capacity for agv in agvs], dtype=torch.float32)
        task_weights = torch.tensor([task.weight / 10.0 for task in tasks], dtype=torch.float32)  # 归一化到[0,1]

        # 计算任务优先级（基于状态和等待时间）
        task_priorities = self._compute_task_priorities(tasks)

        # 创建任务可用性掩码
        mask = self._create_task_mask(agvs, tasks)

        # 添加批次维度
        agv_embeddings = agv_embeddings.unsqueeze(0)
        task_embeddings = task_embeddings.unsqueeze(0)
        distances = distances.unsqueeze(0)
        agv_loads = agv_loads.unsqueeze(0)
        task_weights = task_weights.unsqueeze(0)
        task_priorities = task_priorities.unsqueeze(0)
        mask = mask.unsqueeze(0)

        # 计算注意力
        attention_output = self.attention(
            agv_embeddings, task_embeddings, distances,
            agv_loads, task_weights, task_priorities, mask
        )

        # 更新统计信息
        self._update_stats(attention_output)

        return attention_output

    def _compute_distance_matrix(self, agvs: List[AGVEntity], tasks: List[Task]) -> torch.Tensor:
        """计算AGV到任务的距离矩阵"""
        distances = torch.zeros(len(agvs), len(tasks))

        for i, agv in enumerate(agvs):
            for j, task in enumerate(tasks):
                # 曼哈顿距离
                distance = abs(agv.position[0] - task.position[0]) + abs(agv.position[1] - task.position[1])
                distances[i, j] = distance

        return distances.float()

    def _compute_task_priorities(self, tasks: List[Task]) -> torch.Tensor:
        """计算任务优先级"""
        priorities = torch.zeros(len(tasks))

        for i, task in enumerate(tasks):
            # 基础优先级：重任务优先级更高
            base_priority = task.weight / 10.0

            # 状态优先级：待分配任务优先级更高
            if task.status == TaskStatus.PENDING:
                status_priority = 1.0
            elif task.status == TaskStatus.ASSIGNED:
                status_priority = 0.5
            else:
                status_priority = 0.0

            # 综合优先级
            priorities[i] = (base_priority + status_priority) / 2.0

        return priorities

    def _create_task_mask(self, agvs: List[AGVEntity], tasks: List[Task]) -> torch.Tensor:
        """创建任务可用性掩码"""
        mask = torch.ones(len(agvs), len(tasks))

        for i, agv in enumerate(agvs):
            for j, task in enumerate(tasks):
                # 检查任务是否可分配
                if task.status != TaskStatus.PENDING:
                    mask[i, j] = 0.0

                # 检查AGV载重能力
                if agv.current_load + task.weight > agv.capacity:
                    mask[i, j] = 0.0

        return mask

    def _update_stats(self, attention_output: AttentionOutput):
        """更新统计信息"""
        self.stats['total_calls'] += 1

        # 计算注意力熵（衡量注意力分布的均匀性）
        attention_weights = attention_output.attention_weights.squeeze(0)
        entropy = -torch.sum(attention_weights * torch.log(attention_weights + 1e-8), dim=-1).mean()
        self.stats['avg_attention_entropy'] = (
            self.stats['avg_attention_entropy'] * (self.stats['total_calls'] - 1) + entropy.item()
        ) / self.stats['total_calls']

        # 计算平均约束分数
        constraint_scores = attention_output.constraint_scores.squeeze(0)
        avg_constraint = constraint_scores.mean().item()
        self.stats['avg_constraint_score'] = (
            self.stats['avg_constraint_score'] * (self.stats['total_calls'] - 1) + avg_constraint
        ) / self.stats['total_calls']

        # 计算Top-K覆盖率
        top_k_indices = attention_output.top_k_indices.squeeze(0)
        coverage = top_k_indices.numel() / (attention_weights.shape[0] * attention_weights.shape[1])
        self.stats['top_k_coverage'] = (
            self.stats['top_k_coverage'] * (self.stats['total_calls'] - 1) + coverage
        ) / self.stats['total_calls']

    def get_attention_analysis(self, attention_output: AttentionOutput,
                              agvs: List[AGVEntity], tasks: List[Task]) -> Dict[str, Any]:
        """
        分析注意力输出

        Args:
            attention_output: 注意力输出
            agvs: AGV列表
            tasks: 任务列表

        Returns:
            analysis: 分析结果
        """
        attention_weights = attention_output.attention_weights.squeeze(0)
        constraint_scores = attention_output.constraint_scores.squeeze(0)
        top_k_indices = attention_output.top_k_indices.squeeze(0)

        analysis = {
            'attention_distribution': {
                'max_attention': attention_weights.max().item(),
                'min_attention': attention_weights.min().item(),
                'mean_attention': attention_weights.mean().item(),
                'std_attention': attention_weights.std().item()
            },
            'constraint_analysis': {
                'max_constraint': constraint_scores.max().item(),
                'min_constraint': constraint_scores.min().item(),
                'mean_constraint': constraint_scores.mean().item(),
                'std_constraint': constraint_scores.std().item()
            },
            'top_assignments': [],
            'attention_entropy': -torch.sum(attention_weights * torch.log(attention_weights + 1e-8), dim=-1).mean().item(),
            'sparsity_ratio': (attention_weights < 0.01).float().mean().item()
        }

        # 找出每个AGV的最佳任务分配
        for i, agv in enumerate(agvs):
            agv_attention = attention_weights[i]
            top_task_idx = agv_attention.argmax().item()
            top_attention_score = agv_attention[top_task_idx].item()

            if top_task_idx < len(tasks):
                analysis['top_assignments'].append({
                    'agv_id': agv.agv_id,
                    'agv_position': agv.position,
                    'task_id': tasks[top_task_idx].task_id,
                    'task_position': tasks[top_task_idx].position,
                    'attention_score': top_attention_score,
                    'constraint_score': constraint_scores[i, top_task_idx].item(),
                    'distance': abs(agv.position[0] - tasks[top_task_idx].position[0]) +
                               abs(agv.position[1] - tasks[top_task_idx].position[1])
                })

        return analysis

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_calls': 0,
            'avg_attention_entropy': 0.0,
            'avg_constraint_score': 0.0,
            'top_k_coverage': 0.0
        }
