{"best_params": {"attention_feature_dim": "32", "attention_num_heads": "32", "attention_dropout": 0.0, "attention_pos_dim": "32", "hidden_dim": "256", "num_layers": "3", "activation": "tanh", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "10", "clip_param": 0.3, "entropy_coeff": 0.1, "value_loss_coeff": 0.5, "near_threshold": 3.0, "far_threshold": 5.0, "temperature_scale": 5.0}, "best_score": 0.4897369398675947, "optimization_history": [{"trial": 0, "params": {"attention_feature_dim": "128", "attention_num_heads": "32", "attention_dropout": 0.0, "attention_pos_dim": "64", "hidden_dim": "256", "num_layers": "4", "activation": "relu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "5", "clip_param": 0.3, "entropy_coeff": 0.01, "value_loss_coeff": 1.0, "near_threshold": 3.0, "far_threshold": 10.0, "temperature_scale": 5.0}, "score": 0.4787706952446421, "timestamp": "2025-07-23T19:39:47.803267"}, {"trial": 1, "params": {"attention_feature_dim": "256", "attention_num_heads": "32", "attention_dropout": 0.3, "attention_pos_dim": "128", "hidden_dim": "256", "num_layers": "3", "activation": "relu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "10", "clip_param": 0.2, "entropy_coeff": 0.001, "value_loss_coeff": 0.5, "near_threshold": 8.0, "far_threshold": 10.0, "temperature_scale": 2.0}, "score": 0.4768804865318461, "timestamp": "2025-07-23T19:39:47.882876"}, {"trial": 2, "params": {"attention_feature_dim": "32", "attention_num_heads": "4", "attention_dropout": 0.1, "attention_pos_dim": "64", "hidden_dim": "256", "num_layers": "2", "activation": "gelu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "20", "clip_param": 0.3, "entropy_coeff": 0.1, "value_loss_coeff": 0.5, "near_threshold": 1.0, "far_threshold": 15.0, "temperature_scale": 1.0}, "score": 0.4840299296285612, "timestamp": "2025-07-23T19:39:47.948641"}, {"trial": 3, "params": {"attention_feature_dim": "64", "attention_num_heads": "8", "attention_dropout": 0.1, "attention_pos_dim": "128", "hidden_dim": "256", "num_layers": "4", "activation": "gelu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "10", "clip_param": 0.1, "entropy_coeff": 0.1, "value_loss_coeff": 0.25, "near_threshold": 5.0, "far_threshold": 8.0, "temperature_scale": 2.0}, "score": 0.4839218079360179, "timestamp": "2025-07-23T19:39:48.026058"}, {"trial": 4, "params": {"attention_feature_dim": "32", "attention_num_heads": "32", "attention_dropout": 0.1, "attention_pos_dim": "64", "hidden_dim": "256", "num_layers": "4", "activation": "gelu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "5", "clip_param": 0.3, "entropy_coeff": 0.1, "value_loss_coeff": 0.25, "near_threshold": 3.0, "far_threshold": 10.0, "temperature_scale": 3.0}, "score": 0.4813063896055346, "timestamp": "2025-07-23T19:39:48.089173"}, {"trial": 5, "params": {"attention_feature_dim": "256", "attention_num_heads": "16", "attention_dropout": 0.3, "attention_pos_dim": "128", "hidden_dim": "256", "num_layers": "3", "activation": "gelu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "20", "clip_param": 0.2, "entropy_coeff": 0.001, "value_loss_coeff": 1.0, "near_threshold": 3.0, "far_threshold": 10.0, "temperature_scale": 5.0}, "score": 0.47927905940661253, "timestamp": "2025-07-23T19:39:48.168018"}, {"trial": 6, "params": {"attention_feature_dim": "128", "attention_num_heads": "4", "attention_dropout": 0.1, "attention_pos_dim": "128", "hidden_dim": "256", "num_layers": "2", "activation": "tanh", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "10", "clip_param": 0.3, "entropy_coeff": 0.01, "value_loss_coeff": 0.5, "near_threshold": 3.0, "far_threshold": 5.0, "temperature_scale": 1.0}, "score": 0.4841922217189344, "timestamp": "2025-07-23T19:39:48.231410"}, {"trial": 7, "params": {"attention_feature_dim": "128", "attention_num_heads": "32", "attention_dropout": 0.0, "attention_pos_dim": "128", "hidden_dim": "256", "num_layers": "4", "activation": "relu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "20", "clip_param": 0.2, "entropy_coeff": 0.01, "value_loss_coeff": 0.5, "near_threshold": 8.0, "far_threshold": 5.0, "temperature_scale": 0.5}, "score": 0.4874350890278413, "timestamp": "2025-07-23T19:39:48.310331"}, {"trial": 8, "params": {"attention_feature_dim": "32", "attention_num_heads": "32", "attention_dropout": 0.0, "attention_pos_dim": "32", "hidden_dim": "256", "num_layers": "3", "activation": "tanh", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "10", "clip_param": 0.3, "entropy_coeff": 0.1, "value_loss_coeff": 0.5, "near_threshold": 3.0, "far_threshold": 5.0, "temperature_scale": 5.0}, "score": 0.4897369398675947, "timestamp": "2025-07-23T19:39:48.373920"}, {"trial": 9, "params": {"attention_feature_dim": "256", "attention_num_heads": "4", "attention_dropout": 0.2, "attention_pos_dim": "16", "hidden_dim": "256", "num_layers": "4", "activation": "gelu", "learning_rate": 0.0003, "batch_size": "256", "num_epochs": "20", "clip_param": 0.1, "entropy_coeff": 0.1, "value_loss_coeff": 1.0, "near_threshold": 8.0, "far_threshold": 8.0, "temperature_scale": 1.0}, "score": 0.4766087966944734, "timestamp": "2025-07-23T19:39:48.453184"}], "total_trials": 10, "optimization_time": 0.7117490768432617, "convergence_info": {"total_trials": 10, "best_score": 0.4897369398675947, "score_std": 0.004184979917764222, "score_range": [0.4766087966944734, 0.4897369398675947], "improvement_rate": 0.4444444444444444, "convergence_trial": 8}, "search_space": {"attention_feature_dim": [32, 64, 128, 256], "attention_num_heads": [4, 8, 16, 32], "attention_dropout": [0.0, 0.1, 0.2, 0.3], "attention_pos_dim": [16, 32, 64, 128], "hidden_dim": [256], "num_layers": [2, 3, 4], "activation": ["relu", "tanh", "gelu"], "learning_rate": [0.0003], "batch_size": [256], "num_epochs": [5, 10, 15, 20], "clip_param": [0.1, 0.2, 0.3], "entropy_coeff": [0.001, 0.01, 0.1], "value_loss_coeff": [0.25, 0.5, 1.0], "near_threshold": [1.0, 2.0, 3.0, 5.0, 8.0], "far_threshold": [5.0, 8.0, 10.0, 15.0, 20.0], "temperature_scale": [0.5, 1.0, 2.0, 3.0, 5.0]}, "optimization_config": {"method": "random", "n_trials": 10, "n_jobs": 1, "random_seed": 42}, "timestamp": "2025-07-23T19:39:48.453184"}