"""
训练稳定性保证管理器
集成所有稳定性保证组件的综合管理系统
"""

import torch
import torch.nn as nn
import numpy as np
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from .gradient_optimizer import GradientOptimizer
from .regularization import ComprehensiveRegularizer, RegularizationConfig
from .training_monitor import TrainingMonitor, TrainingMetrics
from .anomaly_recovery import CheckpointManager, AnomalyDetector, RecoveryManager
from .numerical_stability import NumericalStabilizer, NumericalConfig


@dataclass
class StabilityConfig:
    """稳定性配置"""
    # 梯度优化配置
    gradient_clip_max_norm: float = 1.0
    gradient_clip_adaptive: bool = True
    gradient_normalize: bool = False
    learning_rate_adaptive: bool = True
    
    # 正则化配置
    weight_decay: float = 1e-4
    attention_entropy_reg: float = 0.01
    collaboration_sparsity_reg: float = 0.1
    spectral_norm: bool = False
    
    # 监控配置
    monitor_frequency: int = 10
    save_frequency: int = 100
    plot_frequency: int = 500
    
    # 异常检测配置
    loss_spike_threshold: float = 2.0
    grad_explosion_threshold: float = 100.0
    performance_drop_threshold: float = 0.5
    
    # 数值稳定性配置
    numerical_eps: float = 1e-8
    max_value: float = 1e6
    use_mixed_precision: bool = False
    
    # 检查点配置
    max_checkpoints: int = 10
    checkpoint_frequency: int = 100
    
    # 日志配置
    log_dir: str = "./logs"
    checkpoint_dir: str = "./checkpoints"


class TrainingStabilityManager:
    """训练稳定性管理器"""
    
    def __init__(self, 
                 model: nn.Module,
                 optimizer: torch.optim.Optimizer,
                 config: Optional[StabilityConfig] = None):
        """
        初始化训练稳定性管理器
        
        Args:
            model: 模型
            optimizer: 优化器
            config: 稳定性配置
        """
        self.model = model
        self.optimizer = optimizer
        self.config = config or StabilityConfig()
        
        # 初始化组件
        self._initialize_components()
        
        # 状态跟踪
        self.step_count = 0
        self.epoch_count = 0
        self.training_start_time = time.time()
        
        # 性能统计
        self.stability_stats = {
            'total_anomalies': 0,
            'successful_recoveries': 0,
            'gradient_clips': 0,
            'lr_reductions': 0,
            'checkpoints_saved': 0
        }
        
        logging.info("Training Stability Manager initialized")
    
    def _initialize_components(self):
        """初始化所有稳定性组件"""
        # 梯度优化器
        clip_config = {
            'max_norm': self.config.gradient_clip_max_norm,
            'adaptive': self.config.gradient_clip_adaptive
        }
        norm_config = {'method': 'layer_wise'} if self.config.gradient_normalize else None
        lr_config = {'patience': 10, 'factor': 0.5} if self.config.learning_rate_adaptive else None
        
        self.gradient_optimizer = GradientOptimizer(
            self.model, self.optimizer, clip_config, norm_config, lr_config
        )
        
        # 正则化器
        reg_config = RegularizationConfig(
            weight_decay=self.config.weight_decay,
            attention_entropy_reg=self.config.attention_entropy_reg,
            collaboration_sparsity_reg=self.config.collaboration_sparsity_reg,
            spectral_norm=self.config.spectral_norm
        )
        self.regularizer = ComprehensiveRegularizer(reg_config)
        
        # 训练监控器
        self.monitor = TrainingMonitor(
            log_dir=self.config.log_dir,
            save_frequency=self.config.save_frequency,
            plot_frequency=self.config.plot_frequency
        )
        
        # 检查点管理器
        self.checkpoint_manager = CheckpointManager(
            checkpoint_dir=self.config.checkpoint_dir,
            max_checkpoints=self.config.max_checkpoints,
            save_frequency=self.config.checkpoint_frequency
        )
        
        # 异常检测器
        self.anomaly_detector = AnomalyDetector(
            loss_spike_threshold=self.config.loss_spike_threshold,
            grad_explosion_threshold=self.config.grad_explosion_threshold,
            performance_drop_threshold=self.config.performance_drop_threshold
        )
        
        # 恢复管理器
        self.recovery_manager = RecoveryManager(self.checkpoint_manager)
        
        # 数值稳定化器
        numerical_config = NumericalConfig(
            eps=self.config.numerical_eps,
            max_value=self.config.max_value,
            use_mixed_precision=self.config.use_mixed_precision
        )
        self.numerical_stabilizer = NumericalStabilizer(numerical_config)
        
        # 应用谱归一化
        if self.config.spectral_norm:
            self.regularizer.apply_spectral_norm(self.model)
    
    def training_step(self, 
                     loss: torch.Tensor,
                     episode_reward: float = 0.0,
                     episode_length: int = 0,
                     success_rate: float = 0.0,
                     attention_weights: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, Any]:
        """
        执行一个训练步骤
        
        Args:
            loss: 损失值
            episode_reward: 回合奖励
            episode_length: 回合长度
            success_rate: 成功率
            attention_weights: 注意力权重
            
        Returns:
            step_stats: 步骤统计信息
        """
        self.step_count += 1
        step_stats = {'step': self.step_count}
        
        # 1. 数值稳定性检查
        loss = self.numerical_stabilizer.stabilize_tensor(loss, "loss")
        
        # 2. 计算正则化损失
        reg_losses = self.regularizer.compute_regularization_loss(
            self.model, attention_weights
        )
        total_loss = loss + reg_losses['total_regularization']
        
        # 3. 梯度优化
        grad_stats = self.gradient_optimizer.optimize_step(total_loss)
        step_stats.update(grad_stats)
        
        # 4. 稳定化梯度
        grad_stability_stats = self.numerical_stabilizer.stabilize_gradients(self.model)
        step_stats.update(grad_stability_stats)
        
        # 5. 异常检测
        anomalies = self.anomaly_detector.detect_anomalies(
            self.step_count, total_loss.item(), grad_stats['grad_norm'], 
            episode_reward, self.model
        )
        
        # 6. 处理异常
        recovery_success = True
        if anomalies:
            self.stability_stats['total_anomalies'] += len(anomalies)
            
            for anomaly in anomalies:
                success = self.recovery_manager.handle_anomaly(
                    anomaly, self.model, self.optimizer
                )
                if success:
                    self.stability_stats['successful_recoveries'] += 1
                recovery_success = recovery_success and success
        
        # 7. 检查点保存
        if self.checkpoint_manager.should_save(self.step_count):
            metrics = {
                'episode_reward': episode_reward,
                'loss': total_loss.item(),
                'success_rate': success_rate
            }
            
            # 判断是否为最佳检查点
            is_best = (episode_reward > getattr(self, '_best_reward', float('-inf')))
            if is_best:
                self._best_reward = episode_reward
            
            checkpoint_path = self.checkpoint_manager.save_checkpoint(
                self.step_count, self.model, self.optimizer, metrics, is_best
            )
            self.stability_stats['checkpoints_saved'] += 1
            step_stats['checkpoint_saved'] = checkpoint_path
        
        # 8. 创建训练指标
        training_metrics = TrainingMetrics(
            step=self.step_count,
            epoch=self.epoch_count,
            timestamp=time.time(),
            total_loss=total_loss.item(),
            policy_loss=loss.item(),
            value_loss=0.0,  # 需要从外部传入
            entropy_loss=0.0,  # 需要从外部传入
            regularization_loss=reg_losses['total_regularization'].item(),
            attention_penalty=reg_losses.get('total_attention_penalty', torch.tensor(0.0)).item(),
            grad_norm=grad_stats['grad_norm'],
            grad_clipped=grad_stats['grad_clipped'],
            learning_rate=self.optimizer.param_groups[0]['lr'],
            episode_reward=episode_reward,
            episode_length=episode_length,
            success_rate=success_rate,
            task_attention_entropy=0.0,  # 需要计算
            collaboration_attention_entropy=0.0,  # 需要计算
            attention_diversity=0.0  # 需要计算
        )
        
        # 9. 监控记录
        if self.step_count % self.config.monitor_frequency == 0:
            self.monitor.log_metrics(training_metrics)
        
        # 10. 更新统计
        if grad_stats['grad_clipped']:
            self.stability_stats['gradient_clips'] += 1
        
        if grad_stats.get('lr_reduced', False):
            self.stability_stats['lr_reductions'] += 1
        
        # 11. 添加稳定性统计
        step_stats.update({
            'regularization_loss': reg_losses['total_regularization'].item(),
            'anomalies_detected': len(anomalies),
            'recovery_success': recovery_success,
            'model_health_score': self._compute_model_health_score()
        })
        
        return step_stats
    
    def _compute_model_health_score(self) -> float:
        """计算模型健康分数"""
        health_report = self.numerical_stabilizer.check_model_health(self.model)
        return health_report['health_score']
    
    def epoch_end(self):
        """回合结束处理"""
        self.epoch_count += 1
        
        # 记录回合统计
        logging.info(f"Epoch {self.epoch_count} completed. Step: {self.step_count}")
    
    def get_stability_summary(self) -> Dict[str, Any]:
        """获取稳定性摘要"""
        elapsed_time = time.time() - self.training_start_time
        
        # 基础统计
        summary = {
            'training_time': elapsed_time,
            'total_steps': self.step_count,
            'total_epochs': self.epoch_count,
            'steps_per_second': self.step_count / max(elapsed_time, 1),
            'stability_stats': self.stability_stats.copy()
        }
        
        # 组件统计
        summary['gradient_optimizer_stats'] = self.gradient_optimizer.get_comprehensive_stats()
        summary['recovery_stats'] = self.recovery_manager.get_recovery_stats()
        summary['monitoring_stats'] = self.monitor.get_monitoring_summary()
        
        # 模型健康状态
        summary['model_health'] = self.numerical_stabilizer.check_model_health(self.model)
        
        # 计算稳定性分数
        total_anomalies = self.stability_stats['total_anomalies']
        successful_recoveries = self.stability_stats['successful_recoveries']
        
        if total_anomalies > 0:
            recovery_rate = successful_recoveries / total_anomalies
            anomaly_rate = total_anomalies / max(self.step_count, 1)
            stability_score = recovery_rate * (1 - min(anomaly_rate, 1.0))
        else:
            stability_score = 1.0
        
        summary['stability_score'] = stability_score
        
        return summary
    
    def save_stability_report(self, filepath: Optional[str] = None):
        """保存稳定性报告"""
        if filepath is None:
            filepath = Path(self.config.log_dir) / f"stability_report_step_{self.step_count}.json"
        
        summary = self.get_stability_summary()
        
        import json
        with open(filepath, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logging.info(f"Stability report saved: {filepath}")
    
    def emergency_save(self):
        """紧急保存"""
        try:
            # 保存检查点
            emergency_metrics = {
                'step': self.step_count,
                'emergency_save': True,
                'timestamp': time.time()
            }
            
            checkpoint_path = self.checkpoint_manager.save_checkpoint(
                self.step_count, self.model, self.optimizer, emergency_metrics
            )
            
            # 保存稳定性报告
            self.save_stability_report()
            
            logging.info(f"Emergency save completed: {checkpoint_path}")
            
        except Exception as e:
            logging.error(f"Emergency save failed: {e}")
    
    def close(self):
        """关闭稳定性管理器"""
        try:
            # 最终保存
            self.save_stability_report()
            
            # 关闭监控器
            self.monitor.close()
            
            # 记录最终统计
            final_summary = self.get_stability_summary()
            logging.info(f"Training stability manager closed. Final summary: {final_summary}")
            
        except Exception as e:
            logging.error(f"Error closing stability manager: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if exc_type is not None:
            logging.error(f"Exception occurred during training: {exc_type.__name__}: {exc_val}")
            self.emergency_save()
        
        self.close()
        
        # 不抑制异常
        return False
