"""
训练监控器
集成到MAPPO训练过程中的实时监控系统
"""

import time
import psutil
import torch
import numpy as np
from typing import Dict, List, Any, Optional, Callable
from collections import deque
import threading
from datetime import datetime

from .metrics_collector import MetricsCollector, TrainingMetrics, AttentionMetrics, SystemMetrics
from .basic_visualizer import BasicVisualizer


class TrainingMonitor:
    """
    训练监控器
    实时监控训练过程并收集各种指标
    """
    
    def __init__(self, 
                 save_dir: str = "./monitoring_data",
                 update_interval: int = 10,
                 plot_interval: int = 100,
                 enable_system_monitoring: bool = True):
        """
        初始化训练监控器
        
        Args:
            save_dir: 保存目录
            update_interval: 更新间隔（回合数）
            plot_interval: 绘图间隔（回合数）
            enable_system_monitoring: 是否启用系统监控
        """
        self.save_dir = save_dir
        self.update_interval = update_interval
        self.plot_interval = plot_interval
        self.enable_system_monitoring = enable_system_monitoring
        
        # 初始化组件
        self.metrics_collector = MetricsCollector(save_dir=save_dir)
        self.visualizer = BasicVisualizer(
            db_path=str(self.metrics_collector.db_path),
            save_dir=f"{save_dir}/plots"
        )
        
        # 监控状态
        self.is_monitoring = False
        self.start_time = time.time()
        self.last_update_time = time.time()
        self.last_plot_episode = 0
        
        # 性能统计
        self.episode_times = deque(maxlen=100)
        self.step_times = deque(maxlen=1000)
        
        # 系统监控线程
        self.system_monitor_thread = None
        self.stop_system_monitoring = threading.Event()
        
        print(f"✓ 训练监控器初始化完成")
        print(f"  更新间隔: {update_interval} 回合")
        print(f"  绘图间隔: {plot_interval} 回合")
        print(f"  系统监控: {'启用' if enable_system_monitoring else '禁用'}")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            print("⚠️  监控已在运行中")
            return
        
        self.is_monitoring = True
        self.start_time = time.time()
        
        # 启动系统监控线程
        if self.enable_system_monitoring:
            self.stop_system_monitoring.clear()
            self.system_monitor_thread = threading.Thread(
                target=self._system_monitoring_loop,
                daemon=True
            )
            self.system_monitor_thread.start()
        
        print("🚀 训练监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        # 停止系统监控线程
        if self.system_monitor_thread:
            self.stop_system_monitoring.set()
            self.system_monitor_thread.join(timeout=5)
        
        # 保存剩余数据
        self.metrics_collector.close()
        
        print("🛑 训练监控已停止")
    
    def on_episode_start(self, episode: int):
        """回合开始时调用"""
        self.current_episode = episode
        self.episode_start_time = time.time()
    
    def on_episode_end(self, 
                      episode: int,
                      episode_reward: float,
                      episode_length: int,
                      training_info: Dict[str, Any]):
        """
        回合结束时调用
        
        Args:
            episode: 回合数
            episode_reward: 回合奖励
            episode_length: 回合长度
            training_info: 训练信息
        """
        if not self.is_monitoring:
            return
        
        # 计算回合时间
        episode_time = time.time() - self.episode_start_time
        self.episode_times.append(episode_time)
        
        # 收集训练指标
        metrics = TrainingMetrics(
            timestamp=time.time(),
            episode=episode,
            step=training_info.get('total_steps', 0),
            episode_reward=episode_reward,
            episode_length=episode_length,
            policy_loss=training_info.get('policy_loss', 0.0),
            value_loss=training_info.get('value_loss', 0.0),
            entropy_loss=training_info.get('entropy_loss', 0.0),
            total_loss=training_info.get('total_loss', 0.0),
            learning_rate=training_info.get('learning_rate', 0.0),
            grad_norm=training_info.get('grad_norm', 0.0),
            task_completion_rate=training_info.get('task_completion_rate', 0.0),
            collision_count=training_info.get('collision_count', 0),
            avg_task_time=training_info.get('avg_task_time', 0.0),
            num_active_agvs=training_info.get('num_active_agvs', 0),
            coordination_efficiency=training_info.get('coordination_efficiency', 0.0)
        )
        
        self.metrics_collector.collect_training_metrics(metrics)
        
        # 定期更新和绘图
        if episode % self.update_interval == 0:
            self._print_progress_update(episode)
        
        if episode % self.plot_interval == 0 and episode > self.last_plot_episode:
            self._generate_plots(episode)
            self.last_plot_episode = episode
    
    def on_attention_update(self,
                           episode: int,
                           step: int,
                           task_attention_weights: np.ndarray,
                           collaboration_attention_weights: np.ndarray,
                           adaptive_temperatures: np.ndarray):
        """
        注意力权重更新时调用
        
        Args:
            episode: 回合数
            step: 步数
            task_attention_weights: 任务注意力权重
            collaboration_attention_weights: 协作注意力权重
            adaptive_temperatures: 自适应温度
        """
        if not self.is_monitoring:
            return
        
        # 计算注意力统计指标
        task_entropy = self._calculate_entropy(task_attention_weights)
        task_max_weight = float(np.max(task_attention_weights))
        task_sparsity = self._calculate_sparsity(task_attention_weights)
        
        collab_entropy = self._calculate_entropy(collaboration_attention_weights)
        collab_max_weight = float(np.max(collaboration_attention_weights))
        collab_sparsity = self._calculate_sparsity(collaboration_attention_weights)
        
        # 创建注意力指标
        metrics = AttentionMetrics(
            timestamp=time.time(),
            episode=episode,
            step=step,
            task_attention_entropy=task_entropy,
            task_attention_max_weight=task_max_weight,
            task_attention_sparsity=task_sparsity,
            collaboration_attention_entropy=collab_entropy,
            collaboration_attention_max_weight=collab_max_weight,
            collaboration_attention_sparsity=collab_sparsity,
            task_attention_weights=task_attention_weights.tolist(),
            collaboration_attention_weights=collaboration_attention_weights.tolist(),
            adaptive_temperatures=adaptive_temperatures.tolist()
        )
        
        self.metrics_collector.collect_attention_metrics(metrics)
    
    def on_step_end(self, step_time: float):
        """步骤结束时调用"""
        self.step_times.append(step_time)
    
    def _calculate_entropy(self, weights: np.ndarray) -> float:
        """计算注意力权重的熵"""
        # 避免log(0)
        weights = weights + 1e-8
        weights = weights / weights.sum(axis=-1, keepdims=True)
        entropy = -np.sum(weights * np.log(weights), axis=-1)
        return float(np.mean(entropy))
    
    def _calculate_sparsity(self, weights: np.ndarray) -> float:
        """计算注意力权重的稀疏性"""
        # 计算非零权重的比例
        threshold = 0.01  # 小于此值认为是零
        non_zero_ratio = np.mean(weights > threshold)
        sparsity = 1.0 - non_zero_ratio
        return float(sparsity)
    
    def _system_monitoring_loop(self):
        """系统监控循环"""
        while not self.stop_system_monitoring.is_set():
            try:
                # 收集系统指标
                cpu_usage = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                memory_usage = memory.percent
                
                # GPU指标（如果可用）
                gpu_usage = 0.0
                gpu_memory = 0.0
                if torch.cuda.is_available():
                    try:
                        gpu_usage = torch.cuda.utilization()
                        gpu_memory = torch.cuda.memory_usage()
                    except:
                        pass
                
                # 计算性能指标
                fps = 1.0 / np.mean(self.step_times) if self.step_times else 0.0
                samples_per_second = len(self.step_times) / sum(self.step_times) if self.step_times else 0.0
                avg_step_time = np.mean(self.step_times) if self.step_times else 0.0
                
                # 创建系统指标
                metrics = SystemMetrics(
                    timestamp=time.time(),
                    cpu_usage=cpu_usage,
                    memory_usage=memory_usage,
                    gpu_usage=gpu_usage,
                    gpu_memory=gpu_memory,
                    fps=fps,
                    samples_per_second=samples_per_second,
                    training_time_per_step=avg_step_time
                )
                
                self.metrics_collector.collect_system_metrics(metrics)
                
                # 等待下次监控
                self.stop_system_monitoring.wait(timeout=30)  # 30秒间隔
                
            except Exception as e:
                print(f"⚠️  系统监控出错: {e}")
                self.stop_system_monitoring.wait(timeout=30)
    
    def _print_progress_update(self, episode: int):
        """打印进度更新"""
        stats = self.metrics_collector.get_recent_stats()
        runtime = time.time() - self.start_time
        
        print(f"\n📊 训练进度更新 - 回合 {episode}")
        print(f"  运行时间: {runtime/3600:.2f} 小时")
        print(f"  最近平均奖励: {stats['recent_avg_reward']:.2f}")
        print(f"  最近平均损失: {stats['recent_avg_loss']:.4f}")
        print(f"  平均回合时间: {np.mean(self.episode_times):.2f} 秒" if self.episode_times else "  平均回合时间: N/A")
        print(f"  平均步骤时间: {np.mean(self.step_times)*1000:.2f} ms" if self.step_times else "  平均步骤时间: N/A")
    
    def _generate_plots(self, episode: int):
        """生成图表"""
        try:
            print(f"📈 正在生成第 {episode} 回合的监控图表...")
            
            # 生成训练曲线
            self.visualizer.plot_training_curves(save=True)
            
            # 生成注意力分析图（如果有数据）
            attention_df = self.visualizer.load_attention_data()
            if not attention_df.empty:
                self.visualizer.plot_attention_analysis(save=True)
            
            print(f"✓ 监控图表生成完成")
            
        except Exception as e:
            print(f"❌ 生成图表时出错: {e}")
    
    def get_current_stats(self) -> Dict[str, Any]:
        """
        获取当前统计信息
        
        Returns:
            stats: 当前统计信息
        """
        base_stats = self.metrics_collector.get_recent_stats()
        
        # 添加性能统计
        performance_stats = {
            'avg_episode_time': np.mean(self.episode_times) if self.episode_times else 0.0,
            'avg_step_time': np.mean(self.step_times) if self.step_times else 0.0,
            'fps': 1.0 / np.mean(self.step_times) if self.step_times else 0.0,
            'monitoring_status': 'running' if self.is_monitoring else 'stopped'
        }
        
        base_stats.update(performance_stats)
        return base_stats
    
    def generate_report(self) -> Dict[str, Any]:
        """
        生成完整的训练报告
        
        Returns:
            report: 训练报告
        """
        # 基础报告
        report = self.visualizer.generate_training_report()
        
        # 添加监控统计
        current_stats = self.get_current_stats()
        report['监控统计'] = current_stats
        
        # 添加系统信息
        report['系统信息'] = {
            'CPU核心数': psutil.cpu_count(),
            '总内存': f"{psutil.virtual_memory().total / (1024**3):.1f} GB",
            'GPU可用': torch.cuda.is_available(),
            'GPU数量': torch.cuda.device_count() if torch.cuda.is_available() else 0
        }
        
        return report
    
    def export_data(self, filename: Optional[str] = None) -> str:
        """
        导出监控数据
        
        Args:
            filename: 文件名（可选）
            
        Returns:
            filepath: 导出文件路径
        """
        return self.metrics_collector.export_to_json(filename)
    
    def create_callback(self) -> Callable:
        """
        创建用于MAPPO训练的回调函数
        
        Returns:
            callback: 回调函数
        """
        def training_callback(info: Dict[str, Any]):
            """训练回调函数"""
            if 'episode' in info:
                if 'episode_start' in info:
                    self.on_episode_start(info['episode'])
                elif 'episode_end' in info:
                    self.on_episode_end(
                        episode=info['episode'],
                        episode_reward=info.get('episode_reward', 0.0),
                        episode_length=info.get('episode_length', 0),
                        training_info=info.get('training_info', {})
                    )
            
            if 'attention_update' in info:
                self.on_attention_update(
                    episode=info.get('episode', 0),
                    step=info.get('step', 0),
                    task_attention_weights=info.get('task_attention_weights', np.array([])),
                    collaboration_attention_weights=info.get('collaboration_attention_weights', np.array([])),
                    adaptive_temperatures=info.get('adaptive_temperatures', np.array([]))
                )
            
            if 'step_time' in info:
                self.on_step_end(info['step_time'])
        
        return training_callback
