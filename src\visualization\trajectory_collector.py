"""
AGV轨迹数据收集器
收集和存储AGV移动轨迹、任务执行过程等数据
"""

import numpy as np
import json
import pickle
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import os

from ..environment.agv_entity import AGVEntity, AGVStatus
from ..environment.task_manager import Task, TaskStatus


@dataclass
class TrajectoryPoint:
    """轨迹点数据结构"""
    timestamp: float
    agv_id: int
    position: Tuple[int, int]
    status: str
    current_load: int
    target_task: Optional[int]
    action: Optional[str]
    reward: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class TaskEvent:
    """任务事件数据结构"""
    timestamp: float
    task_id: int
    event_type: str  # 'created', 'assigned', 'pickup_started', 'pickup_completed', 'delivery_started', 'delivery_completed'
    agv_id: Optional[int]
    position: Optional[Tuple[int, int]]
    details: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class CollisionEvent:
    """碰撞事件数据结构"""
    timestamp: float
    agv1_id: int
    agv2_id: int
    position1: Tuple[int, int]
    position2: Tuple[int, int]
    collision_type: str  # 'near_miss', 'collision', 'deadlock'
    resolved: bool
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


class TrajectoryCollector:
    """AGV轨迹数据收集器"""
    
    def __init__(self, max_episodes: int = 1000, save_dir: str = "./trajectory_data"):
        """
        初始化轨迹收集器
        
        Args:
            max_episodes: 最大保存的回合数
            save_dir: 数据保存目录
        """
        self.max_episodes = max_episodes
        self.save_dir = save_dir
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 当前回合数据
        self.current_episode = 0
        self.current_step = 0
        self.episode_start_time = None
        
        # 数据存储
        self.trajectories: Dict[int, List[TrajectoryPoint]] = {}  # agv_id -> trajectory_points
        self.task_events: List[TaskEvent] = []
        self.collision_events: List[CollisionEvent] = []
        self.episode_metadata: Dict[str, Any] = {}
        
        # 历史数据索引
        self.episode_index: List[Dict[str, Any]] = []
        
        print(f"✓ 轨迹收集器初始化完成，数据保存目录: {save_dir}")
    
    def start_episode(self, episode_id: int, metadata: Optional[Dict[str, Any]] = None):
        """
        开始新回合
        
        Args:
            episode_id: 回合ID
            metadata: 回合元数据
        """
        self.current_episode = episode_id
        self.current_step = 0
        self.episode_start_time = datetime.now().timestamp()
        
        # 清空当前回合数据
        self.trajectories.clear()
        self.task_events.clear()
        self.collision_events.clear()
        
        # 设置回合元数据
        self.episode_metadata = {
            'episode_id': episode_id,
            'start_time': self.episode_start_time,
            'metadata': metadata or {}
        }
        
        print(f"开始收集回合 {episode_id} 的轨迹数据")
    
    def record_step(self, agvs: List[AGVEntity], actions: Dict[int, str], 
                   rewards: Dict[int, float], step_time: Optional[float] = None):
        """
        记录单步数据
        
        Args:
            agvs: AGV实体列表
            actions: AGV动作字典
            rewards: AGV奖励字典
            step_time: 步骤时间戳
        """
        if step_time is None:
            step_time = datetime.now().timestamp()
        
        self.current_step += 1
        
        # 记录每个AGV的轨迹点
        for agv in agvs:
            agv_id = agv.agv_id
            
            # 初始化AGV轨迹列表
            if agv_id not in self.trajectories:
                self.trajectories[agv_id] = []
            
            # 创建轨迹点
            trajectory_point = TrajectoryPoint(
                timestamp=step_time,
                agv_id=agv_id,
                position=agv.position,
                status=agv.status.name if hasattr(agv.status, 'name') else str(agv.status),
                current_load=agv.current_load,
                target_task=getattr(agv, 'target_task', None),
                action=actions.get(agv_id),
                reward=rewards.get(agv_id, 0.0)
            )
            
            self.trajectories[agv_id].append(trajectory_point)
    
    def record_task_event(self, task: Task, event_type: str, agv_id: Optional[int] = None, 
                         position: Optional[Tuple[int, int]] = None, details: Optional[Dict] = None):
        """
        记录任务事件
        
        Args:
            task: 任务对象
            event_type: 事件类型
            agv_id: 相关AGV ID
            position: 事件位置
            details: 事件详情
        """
        event = TaskEvent(
            timestamp=datetime.now().timestamp(),
            task_id=task.task_id,
            event_type=event_type,
            agv_id=agv_id,
            position=position,
            details=details or {}
        )
        
        self.task_events.append(event)
    
    def record_collision_event(self, agv1: AGVEntity, agv2: AGVEntity, 
                              collision_type: str = 'near_miss', resolved: bool = True):
        """
        记录碰撞事件
        
        Args:
            agv1: AGV1实体
            agv2: AGV2实体
            collision_type: 碰撞类型
            resolved: 是否已解决
        """
        event = CollisionEvent(
            timestamp=datetime.now().timestamp(),
            agv1_id=agv1.agv_id,
            agv2_id=agv2.agv_id,
            position1=agv1.position,
            position2=agv2.position,
            collision_type=collision_type,
            resolved=resolved
        )
        
        self.collision_events.append(event)
    
    def end_episode(self, final_metadata: Optional[Dict[str, Any]] = None):
        """
        结束当前回合并保存数据
        
        Args:
            final_metadata: 最终元数据
        """
        end_time = datetime.now().timestamp()
        
        # 更新回合元数据
        self.episode_metadata.update({
            'end_time': end_time,
            'duration': end_time - self.episode_start_time,
            'total_steps': self.current_step,
            'num_agvs': len(self.trajectories),
            'num_task_events': len(self.task_events),
            'num_collision_events': len(self.collision_events),
            'final_metadata': final_metadata or {}
        })
        
        # 保存回合数据
        self._save_episode_data()
        
        # 更新索引
        self._update_episode_index()
        
        print(f"✓ 回合 {self.current_episode} 数据保存完成，总步数: {self.current_step}")
    
    def _save_episode_data(self):
        """保存回合数据到文件"""
        episode_data = {
            'metadata': self.episode_metadata,
            'trajectories': {agv_id: [point.to_dict() for point in points] 
                           for agv_id, points in self.trajectories.items()},
            'task_events': [event.to_dict() for event in self.task_events],
            'collision_events': [event.to_dict() for event in self.collision_events]
        }
        
        # 保存为JSON格式（便于读取）
        json_file = os.path.join(self.save_dir, f"episode_{self.current_episode}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(episode_data, f, indent=2, ensure_ascii=False)
        
        # 保存为pickle格式（便于Python处理）
        pickle_file = os.path.join(self.save_dir, f"episode_{self.current_episode}.pkl")
        with open(pickle_file, 'wb') as f:
            pickle.dump(episode_data, f)
    
    def _update_episode_index(self):
        """更新回合索引"""
        index_entry = {
            'episode_id': self.current_episode,
            'start_time': self.episode_metadata['start_time'],
            'end_time': self.episode_metadata['end_time'],
            'duration': self.episode_metadata['duration'],
            'total_steps': self.episode_metadata['total_steps'],
            'num_agvs': self.episode_metadata['num_agvs'],
            'data_files': {
                'json': f"episode_{self.current_episode}.json",
                'pickle': f"episode_{self.current_episode}.pkl"
            }
        }
        
        self.episode_index.append(index_entry)
        
        # 保存索引文件
        index_file = os.path.join(self.save_dir, "episode_index.json")
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(self.episode_index, f, indent=2, ensure_ascii=False)
        
        # 清理旧数据（如果超过最大回合数）
        if len(self.episode_index) > self.max_episodes:
            self._cleanup_old_episodes()
    
    def _cleanup_old_episodes(self):
        """清理旧的回合数据"""
        while len(self.episode_index) > self.max_episodes:
            old_episode = self.episode_index.pop(0)
            
            # 删除数据文件
            for file_type, filename in old_episode['data_files'].items():
                file_path = os.path.join(self.save_dir, filename)
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            print(f"清理旧回合数据: {old_episode['episode_id']}")
    
    def load_episode_data(self, episode_id: int) -> Optional[Dict[str, Any]]:
        """
        加载指定回合的数据
        
        Args:
            episode_id: 回合ID
            
        Returns:
            episode_data: 回合数据字典
        """
        pickle_file = os.path.join(self.save_dir, f"episode_{episode_id}.pkl")
        
        if os.path.exists(pickle_file):
            with open(pickle_file, 'rb') as f:
                return pickle.load(f)
        
        json_file = os.path.join(self.save_dir, f"episode_{episode_id}.json")
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        print(f"未找到回合 {episode_id} 的数据文件")
        return None
    
    def get_episode_list(self) -> List[Dict[str, Any]]:
        """
        获取所有回合的列表
        
        Returns:
            episode_list: 回合信息列表
        """
        return self.episode_index.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取收集统计信息
        
        Returns:
            statistics: 统计信息字典
        """
        if not self.episode_index:
            return {'total_episodes': 0}
        
        total_steps = sum(ep['total_steps'] for ep in self.episode_index)
        total_duration = sum(ep['duration'] for ep in self.episode_index)
        avg_steps = total_steps / len(self.episode_index)
        avg_duration = total_duration / len(self.episode_index)
        
        return {
            'total_episodes': len(self.episode_index),
            'total_steps': total_steps,
            'total_duration': total_duration,
            'avg_steps_per_episode': avg_steps,
            'avg_duration_per_episode': avg_duration,
            'data_size_mb': self._calculate_data_size()
        }
    
    def _calculate_data_size(self) -> float:
        """计算数据文件总大小（MB）"""
        total_size = 0
        for filename in os.listdir(self.save_dir):
            file_path = os.path.join(self.save_dir, filename)
            if os.path.isfile(file_path):
                total_size += os.path.getsize(file_path)
        
        return total_size / (1024 * 1024)  # 转换为MB
