"""
日志系统模块
提供统一的日志记录和监控功能
"""

import os
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

import torch
from torch.utils.tensorboard import SummaryWriter


class Logger:
    """统一日志记录器"""

    def __init__(self,
                 log_dir: str = "logs",
                 experiment_name: str = None,
                 use_tensorboard: bool = True,
                 use_wandb: bool = False,
                 log_level: str = "INFO"):
        """
        初始化日志记录器

        Args:
            log_dir: 日志目录
            experiment_name: 实验名称
            use_tensorboard: 是否使用TensorBoard
            use_wandb: 是否使用WandB
            log_level: 日志级别
        """
        self.log_dir = Path(log_dir)
        self.experiment_name = experiment_name or f"exp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.use_tensorboard = use_tensorboard
        self.use_wandb = use_wandb

        # 创建实验目录
        self.exp_dir = self.log_dir / self.experiment_name
        self.exp_dir.mkdir(parents=True, exist_ok=True)

        # 设置文件日志
        self._setup_file_logger(log_level)

        # 设置TensorBoard
        if self.use_tensorboard:
            self.tb_writer = SummaryWriter(log_dir=str(self.exp_dir / "tensorboard"))
        else:
            self.tb_writer = None

        # 设置WandB
        if self.use_wandb:
            try:
                import wandb
                wandb.init(
                    project="mappo-agv-scheduling",
                    name=self.experiment_name,
                    dir=str(self.exp_dir)
                )
                self.wandb = wandb
            except ImportError:
                self.logger.warning("WandB not installed, skipping WandB logging")
                self.wandb = None
        else:
            self.wandb = None

        # 记录开始时间
        self.start_time = time.time()
        self.step_count = 0

        self.info(f"Logger initialized for experiment: {self.experiment_name}")

    def _setup_file_logger(self, log_level: str):
        """设置文件日志记录器"""
        self.logger = logging.getLogger(self.experiment_name)
        self.logger.setLevel(getattr(logging, log_level.upper()))

        # 清除已有的处理器
        self.logger.handlers.clear()

        # 文件处理器
        file_handler = logging.FileHandler(
            self.exp_dir / "training.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def log_scalar(self, tag: str, value: float, step: Optional[int] = None):
        """记录标量值"""
        if step is None:
            step = self.step_count

        # TensorBoard记录
        if self.tb_writer:
            self.tb_writer.add_scalar(tag, value, step)

        # WandB记录
        if self.wandb:
            self.wandb.log({tag: value}, step=step)

    def log_scalars(self, tag_scalar_dict: Dict[str, float], step: Optional[int] = None):
        """批量记录标量值"""
        if step is None:
            step = self.step_count

        for tag, value in tag_scalar_dict.items():
            self.log_scalar(tag, value, step)

    def log_histogram(self, tag: str, values: torch.Tensor, step: Optional[int] = None):
        """记录直方图"""
        if step is None:
            step = self.step_count

        if self.tb_writer:
            self.tb_writer.add_histogram(tag, values, step)

    def log_image(self, tag: str, img_tensor: torch.Tensor, step: Optional[int] = None):
        """记录图像"""
        if step is None:
            step = self.step_count

        if self.tb_writer:
            self.tb_writer.add_image(tag, img_tensor, step)

    def log_text(self, tag: str, text: str, step: Optional[int] = None):
        """记录文本"""
        if step is None:
            step = self.step_count

        if self.tb_writer:
            self.tb_writer.add_text(tag, text, step)

    def log_hyperparams(self, hparam_dict: Dict[str, Any], metric_dict: Dict[str, float]):
        """记录超参数"""
        if self.tb_writer:
            self.tb_writer.add_hparams(hparam_dict, metric_dict)

        if self.wandb:
            self.wandb.config.update(hparam_dict)

    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)

    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)

    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)

    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)

    def step(self):
        """增加步数计数"""
        self.step_count += 1

    def get_elapsed_time(self) -> float:
        """获取运行时间"""
        return time.time() - self.start_time

    def close(self):
        """关闭日志记录器"""
        if self.tb_writer:
            self.tb_writer.close()

        if self.wandb:
            self.wandb.finish()

        self.info(f"Logger closed. Total elapsed time: {self.get_elapsed_time():.2f}s")


def get_logger(name: str = "default", **kwargs) -> Logger:
    """获取日志记录器实例"""
    return Logger(experiment_name=name, **kwargs)