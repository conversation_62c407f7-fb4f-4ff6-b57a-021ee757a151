"""
课程学习管理器
实现三阶段渐进式训练策略，包括基础技能学习、协作技能发展和复杂场景掌握
"""

import os
import json
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from ..mappo.marllib_integration import MARLlibAGVTrainer
from config.mappo_config import get_curriculum_config


@dataclass
class CurriculumStage:
    """课程学习阶段配置"""
    name: str
    description: str
    num_agvs: int
    num_tasks: int
    max_episode_steps: int
    map_complexity: float  # 0.0-1.0
    task_complexity: float  # 0.0-1.0
    collaboration_requirement: float  # 0.0-1.0
    success_threshold: float  # 成功率阈值
    performance_threshold: float  # 性能阈值
    min_episodes: int  # 最少训练回合数
    max_episodes: int  # 最多训练回合数
    evaluation_episodes: int  # 评估回合数


class CurriculumManager:
    """
    课程学习管理器
    管理三阶段渐进式训练策略
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化课程学习管理器
        
        Args:
            config: 课程学习配置
        """
        self.config = config
        self.logger = self._setup_logger()
        
        # 定义三阶段课程
        self.stages = self._define_curriculum_stages()
        self.current_stage_idx = 0
        self.current_stage = self.stages[0]
        
        # 训练状态
        self.stage_history = []
        self.performance_history = []
        self.transition_history = []
        
        # 创建训练器
        self.trainer = None
        self.current_checkpoint = None
        
        # 性能监控
        self.evaluation_buffer = []
        self.performance_window = config.get('performance_window', 10)
        
        self.logger.info(f"课程学习管理器初始化完成，共{len(self.stages)}个阶段")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('CurriculumManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _define_curriculum_stages(self) -> List[CurriculumStage]:
        """定义三阶段课程学习策略"""
        stages = [
            # 阶段1：基础技能学习
            CurriculumStage(
                name="stage1_basic_skills",
                description="基础技能学习：单AGV导航和简单任务执行",
                num_agvs=2,
                num_tasks=3,
                max_episode_steps=200,
                map_complexity=0.3,
                task_complexity=0.2,
                collaboration_requirement=0.1,
                success_threshold=0.7,
                performance_threshold=400.0,
                min_episodes=100,
                max_episodes=500,
                evaluation_episodes=20
            ),
            
            # 阶段2：协作技能发展
            CurriculumStage(
                name="stage2_collaboration",
                description="协作技能发展：多AGV协调和中等复杂度任务",
                num_agvs=3,
                num_tasks=5,
                max_episode_steps=300,
                map_complexity=0.6,
                task_complexity=0.5,
                collaboration_requirement=0.6,
                success_threshold=0.75,
                performance_threshold=600.0,
                min_episodes=150,
                max_episodes=800,
                evaluation_episodes=30
            ),
            
            # 阶段3：复杂场景掌握
            CurriculumStage(
                name="stage3_complex_scenarios",
                description="复杂场景掌握：大规模多AGV协同和高复杂度任务",
                num_agvs=4,
                num_tasks=8,
                max_episode_steps=500,
                map_complexity=1.0,
                task_complexity=0.8,
                collaboration_requirement=0.9,
                success_threshold=0.8,
                performance_threshold=800.0,
                min_episodes=200,
                max_episodes=1000,
                evaluation_episodes=50
            )
        ]
        
        return stages
    
    def start_curriculum_training(self, output_dir: str = "./curriculum_results") -> Dict[str, Any]:
        """
        开始课程学习训练
        
        Args:
            output_dir: 输出目录
            
        Returns:
            training_results: 训练结果
        """
        self.logger.info("=" * 80)
        self.logger.info("开始三阶段课程学习训练")
        self.logger.info("=" * 80)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        training_results = {
            'start_time': datetime.now().isoformat(),
            'stages': [],
            'total_episodes': 0,
            'total_time': 0,
            'final_performance': 0,
            'success': False
        }
        
        try:
            # 逐阶段训练
            for stage_idx, stage in enumerate(self.stages):
                self.current_stage_idx = stage_idx
                self.current_stage = stage
                
                self.logger.info(f"\n{'='*60}")
                self.logger.info(f"开始阶段 {stage_idx + 1}: {stage.name}")
                self.logger.info(f"描述: {stage.description}")
                self.logger.info(f"配置: {stage.num_agvs}AGV, {stage.num_tasks}任务, {stage.max_episode_steps}步")
                self.logger.info(f"{'='*60}")
                
                # 训练当前阶段
                stage_result = self._train_stage(stage, output_dir)
                training_results['stages'].append(stage_result)
                training_results['total_episodes'] += stage_result['episodes_trained']
                
                # 检查是否成功完成阶段
                if not stage_result['success']:
                    self.logger.warning(f"阶段 {stage_idx + 1} 训练失败，停止课程学习")
                    break
                
                self.logger.info(f"✅ 阶段 {stage_idx + 1} 训练成功完成")
                
                # 保存阶段结果
                self._save_stage_results(stage_result, output_dir, stage_idx)
            
            # 计算最终结果
            if training_results['stages'] and training_results['stages'][-1]['success']:
                training_results['success'] = True
                training_results['final_performance'] = training_results['stages'][-1]['final_performance']
            
            training_results['end_time'] = datetime.now().isoformat()
            
            # 保存完整结果
            self._save_curriculum_results(training_results, output_dir)
            
            self.logger.info("\n" + "=" * 80)
            if training_results['success']:
                self.logger.info("🎉 课程学习训练成功完成！")
                self.logger.info(f"总训练回合: {training_results['total_episodes']}")
                self.logger.info(f"最终性能: {training_results['final_performance']:.2f}")
            else:
                self.logger.info("❌ 课程学习训练未完全成功")
            self.logger.info("=" * 80)
            
            return training_results
            
        except Exception as e:
            self.logger.error(f"课程学习训练过程中出现错误: {e}")
            training_results['error'] = str(e)
            training_results['end_time'] = datetime.now().isoformat()
            return training_results
    
    def _train_stage(self, stage: CurriculumStage, output_dir: str) -> Dict[str, Any]:
        """
        训练单个阶段
        
        Args:
            stage: 阶段配置
            output_dir: 输出目录
            
        Returns:
            stage_result: 阶段训练结果
        """
        stage_result = {
            'stage_name': stage.name,
            'start_time': datetime.now().isoformat(),
            'episodes_trained': 0,
            'evaluations': [],
            'best_performance': 0,
            'final_performance': 0,
            'success': False,
            'checkpoint_path': None
        }
        
        try:
            # 创建阶段专用训练器
            stage_config = self._create_stage_config(stage)
            self.trainer = MARLlibAGVTrainer(stage_config)
            
            # 训练循环
            episodes_trained = 0
            consecutive_successes = 0
            best_performance = 0
            
            while episodes_trained < stage.max_episodes:
                # 训练一批回合
                batch_episodes = min(50, stage.max_episodes - episodes_trained)
                
                self.logger.info(f"训练批次: {episodes_trained}-{episodes_trained + batch_episodes}")
                
                # 执行训练
                checkpoint = self.trainer.train(
                    stage=stage.name,
                    num_iterations=batch_episodes // 10,  # 每10回合一个迭代
                    checkpoint_path=self.current_checkpoint,
                    experiment_name=f"curriculum_{stage.name}"
                )
                
                episodes_trained += batch_episodes
                self.current_checkpoint = checkpoint
                
                # 评估性能
                if episodes_trained >= stage.min_episodes and episodes_trained % 50 == 0:
                    evaluation_result = self._evaluate_stage_performance(stage)
                    stage_result['evaluations'].append(evaluation_result)
                    
                    current_performance = evaluation_result['mean_reward']
                    success_rate = evaluation_result['success_rate']
                    
                    self.logger.info(f"评估结果: 性能={current_performance:.2f}, 成功率={success_rate:.2f}")
                    
                    # 更新最佳性能
                    if current_performance > best_performance:
                        best_performance = current_performance
                        stage_result['best_performance'] = best_performance
                        stage_result['checkpoint_path'] = checkpoint
                    
                    # 检查阶段完成条件
                    if (current_performance >= stage.performance_threshold and 
                        success_rate >= stage.success_threshold):
                        consecutive_successes += 1
                        if consecutive_successes >= 2:  # 连续两次评估成功
                            stage_result['success'] = True
                            stage_result['final_performance'] = current_performance
                            break
                    else:
                        consecutive_successes = 0
            
            stage_result['episodes_trained'] = episodes_trained
            stage_result['end_time'] = datetime.now().isoformat()
            
            # 如果达到最大回合数但未成功，检查是否接近目标
            if not stage_result['success'] and best_performance >= stage.performance_threshold * 0.9:
                self.logger.info(f"阶段接近成功标准，允许继续到下一阶段")
                stage_result['success'] = True
                stage_result['final_performance'] = best_performance
            
            return stage_result
            
        except Exception as e:
            self.logger.error(f"阶段 {stage.name} 训练失败: {e}")
            stage_result['error'] = str(e)
            stage_result['end_time'] = datetime.now().isoformat()
            return stage_result
        
        finally:
            if self.trainer:
                self.trainer.close()
    
    def _create_stage_config(self, stage: CurriculumStage) -> Dict[str, Any]:
        """
        为阶段创建训练配置
        
        Args:
            stage: 阶段配置
            
        Returns:
            config: 训练配置
        """
        # 获取基础配置
        base_config = get_curriculum_config(stage.name.split('_')[0])
        
        # 根据阶段调整配置
        stage_config = base_config.to_dict()
        
        # 更新环境配置
        stage_config.update({
            'num_agvs': stage.num_agvs,
            'num_tasks': stage.num_tasks,
            'max_episode_steps': stage.max_episode_steps,
            'target_reward': stage.performance_threshold,
            'evaluation_episodes': stage.evaluation_episodes
        })
        
        return stage_config
    
    def _evaluate_stage_performance(self, stage: CurriculumStage) -> Dict[str, float]:
        """
        评估阶段性能
        
        Args:
            stage: 阶段配置
            
        Returns:
            evaluation_result: 评估结果
        """
        if not self.trainer or not self.current_checkpoint:
            return {'mean_reward': 0, 'success_rate': 0, 'mean_length': 0}
        
        try:
            # 使用训练器评估
            results = self.trainer.evaluate(
                checkpoint_path=self.current_checkpoint,
                num_episodes=stage.evaluation_episodes,
                render=False
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"评估失败: {e}")
            return {'mean_reward': 0, 'success_rate': 0, 'mean_length': 0}
    
    def _save_stage_results(self, stage_result: Dict[str, Any], output_dir: str, stage_idx: int):
        """保存阶段结果"""
        filename = f"stage_{stage_idx + 1}_results.json"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(stage_result, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"阶段结果已保存: {filepath}")
    
    def _save_curriculum_results(self, results: Dict[str, Any], output_dir: str):
        """保存完整课程学习结果"""
        filename = "curriculum_training_results.json"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"课程学习结果已保存: {filepath}")
    
    def get_current_stage_info(self) -> Dict[str, Any]:
        """获取当前阶段信息"""
        return {
            'stage_index': self.current_stage_idx,
            'stage_name': self.current_stage.name,
            'stage_config': asdict(self.current_stage),
            'total_stages': len(self.stages)
        }
    
    def get_training_progress(self) -> Dict[str, Any]:
        """获取训练进度"""
        return {
            'current_stage': self.current_stage_idx + 1,
            'total_stages': len(self.stages),
            'progress_percentage': (self.current_stage_idx / len(self.stages)) * 100,
            'stage_history': self.stage_history,
            'performance_history': self.performance_history
        }
