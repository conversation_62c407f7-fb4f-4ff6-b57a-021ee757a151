"""
动作空间与奖励函数管理器
集成动作空间设计和奖励函数的统一管理接口
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass

from .action_space import HierarchicalActionSpace, ActionMaskManager, ActionInfo
from .reward_function import MultiDimensionalRewardFunction, RewardWeights, RewardInfo
from .agv_entity import AGVEntity
from .task_manager import Task


@dataclass
class ActionRewardConfig:
    """动作奖励配置"""
    map_width: int = 26
    map_height: int = 10
    reward_weights: Optional[RewardWeights] = None
    enable_action_masking: bool = True
    enable_reward_shaping: bool = True
    reward_normalization: bool = True


class ActionRewardManager:
    """
    动作空间与奖励函数管理器
    提供统一的动作空间管理和奖励计算接口
    """
    
    def __init__(self, config: Optional[ActionRewardConfig] = None):
        """
        初始化动作奖励管理器
        
        Args:
            config: 配置参数
        """
        self.config = config or ActionRewardConfig()
        
        # 初始化动作空间
        self.action_space = HierarchicalActionSpace()
        
        # 初始化动作掩码管理器
        self.mask_manager = ActionMaskManager(
            action_space=self.action_space,
            map_width=self.config.map_width,
            map_height=self.config.map_height
        )
        
        # 初始化奖励函数
        self.reward_function = MultiDimensionalRewardFunction(
            weights=self.config.reward_weights
        )
        
        # 统计信息
        self.action_statistics = {}
        self.reward_statistics = {}
        self.episode_count = 0
        
    def get_action_space_size(self) -> int:
        """获取动作空间大小"""
        return self.action_space.get_total_actions()
    
    def get_action_info(self, action_id: int) -> Optional[ActionInfo]:
        """获取动作信息"""
        return self.action_space.get_action_info(action_id)
    
    def generate_action_mask(self,
                           agv: AGVEntity,
                           other_agvs: List[AGVEntity],
                           obstacles: Optional[Set[Tuple[int, int]]] = None) -> np.ndarray:
        """
        生成动作掩码
        
        Args:
            agv: 当前AGV
            other_agvs: 其他AGV列表
            obstacles: 障碍物位置集合
            
        Returns:
            action_mask: 动作掩码数组
        """
        if not self.config.enable_action_masking:
            # 如果禁用动作掩码，返回全1数组
            return np.ones(self.get_action_space_size(), dtype=np.float32)
        
        mask = self.mask_manager.generate_action_mask(agv, other_agvs, obstacles)
        
        # 更新动作掩码统计
        self._update_action_mask_statistics(agv.agv_id, mask)
        
        return mask
    
    def compute_reward(self,
                      agv: AGVEntity,
                      action: int,
                      prev_state: Dict,
                      current_state: Dict,
                      other_agvs: List[AGVEntity],
                      completed_tasks: List[Task],
                      failed_tasks: List[Task]) -> RewardInfo:
        """
        计算奖励
        
        Args:
            agv: 当前AGV
            action: 执行的动作
            prev_state: 前一状态
            current_state: 当前状态
            other_agvs: 其他AGV列表
            completed_tasks: 完成的任务列表
            failed_tasks: 失败的任务列表
            
        Returns:
            reward_info: 奖励信息
        """
        reward_info = self.reward_function.compute_reward(
            agv=agv,
            action=action,
            prev_state=prev_state,
            current_state=current_state,
            other_agvs=other_agvs,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks
        )
        
        # 奖励塑形
        if self.config.enable_reward_shaping:
            reward_info = self._apply_reward_shaping(reward_info, agv, action)
        
        # 更新奖励统计
        self._update_reward_statistics(agv.agv_id, reward_info)
        
        return reward_info
    
    def validate_action(self, agv: AGVEntity, action: int, action_mask: np.ndarray) -> bool:
        """
        验证动作是否有效
        
        Args:
            agv: AGV实体
            action: 动作ID
            action_mask: 动作掩码
            
        Returns:
            is_valid: 动作是否有效
        """
        # 检查动作ID是否在有效范围内
        if not (0 <= action < self.get_action_space_size()):
            return False
        
        # 检查动作是否被掩码允许
        if self.config.enable_action_masking and action_mask[action] == 0:
            return False
        
        # 检查动作是否在动作空间中定义
        if not self.action_space.is_valid_action(action):
            return False
        
        return True
    
    def get_valid_actions(self, action_mask: np.ndarray) -> List[int]:
        """
        获取有效动作列表
        
        Args:
            action_mask: 动作掩码
            
        Returns:
            valid_actions: 有效动作ID列表
        """
        return [i for i in range(len(action_mask)) if action_mask[i] > 0]
    
    def sample_random_action(self, action_mask: np.ndarray) -> int:
        """
        从有效动作中随机采样
        
        Args:
            action_mask: 动作掩码
            
        Returns:
            action: 随机动作ID
        """
        valid_actions = self.get_valid_actions(action_mask)
        if not valid_actions:
            # 如果没有有效动作，返回等待动作
            return 11  # WaitAction.WAIT.value
        
        return np.random.choice(valid_actions)
    
    def _apply_reward_shaping(self, reward_info: RewardInfo, agv: AGVEntity, action: int) -> RewardInfo:
        """
        应用奖励塑形
        
        Args:
            reward_info: 原始奖励信息
            agv: AGV实体
            action: 动作ID
            
        Returns:
            shaped_reward_info: 塑形后的奖励信息
        """
        # 基于历史表现的奖励塑形
        shaped_reward = reward_info.total_reward
        
        # 动态调整奖励权重
        if hasattr(agv, 'performance_history'):
            performance_factor = self._calculate_performance_factor(agv)
            shaped_reward *= performance_factor
        
        # 基于动作频率的奖励塑形
        action_frequency = self._get_action_frequency(agv.agv_id, action)
        if action_frequency > 0.5:  # 如果某个动作使用过于频繁
            shaped_reward *= 0.9  # 轻微惩罚
        
        # 更新奖励信息
        reward_info.total_reward = shaped_reward
        if self.config.reward_normalization:
            reward_info.normalized_reward = self.reward_function._normalize_reward(shaped_reward)
        
        return reward_info
    
    def _calculate_performance_factor(self, agv: AGVEntity) -> float:
        """计算性能因子"""
        # 简化实现，基于AGV的历史表现调整奖励
        if hasattr(agv, 'success_rate'):
            if agv.success_rate > 0.8:
                return 1.1  # 高性能AGV获得奖励加成
            elif agv.success_rate < 0.3:
                return 0.9  # 低性能AGV获得奖励减少
        return 1.0
    
    def _get_action_frequency(self, agv_id: int, action: int) -> float:
        """获取动作使用频率"""
        if agv_id not in self.action_statistics:
            return 0.0
        
        agv_stats = self.action_statistics[agv_id]
        total_actions = sum(agv_stats.get('action_counts', {}).values())
        action_count = agv_stats.get('action_counts', {}).get(action, 0)
        
        return action_count / max(total_actions, 1)
    
    def _update_action_mask_statistics(self, agv_id: int, action_mask: np.ndarray):
        """更新动作掩码统计"""
        if agv_id not in self.action_statistics:
            self.action_statistics[agv_id] = {
                'action_counts': {},
                'mask_statistics': {
                    'total_masks': 0,
                    'average_valid_actions': 0.0,
                    'mask_entropy': 0.0
                }
            }
        
        stats = self.action_statistics[agv_id]['mask_statistics']
        stats['total_masks'] += 1
        
        # 计算平均有效动作数
        valid_actions = np.sum(action_mask)
        stats['average_valid_actions'] = (
            (stats['average_valid_actions'] * (stats['total_masks'] - 1) + valid_actions) /
            stats['total_masks']
        )
        
        # 计算掩码熵
        if valid_actions > 0:
            normalized_mask = action_mask / valid_actions
            entropy = -np.sum(normalized_mask * np.log(normalized_mask + 1e-8))
            stats['mask_entropy'] = (
                (stats['mask_entropy'] * (stats['total_masks'] - 1) + entropy) /
                stats['total_masks']
            )
    
    def _update_reward_statistics(self, agv_id: int, reward_info: RewardInfo):
        """更新奖励统计"""
        if agv_id not in self.reward_statistics:
            self.reward_statistics[agv_id] = {
                'total_rewards': [],
                'component_rewards': {},
                'episode_rewards': []
            }
        
        stats = self.reward_statistics[agv_id]
        stats['total_rewards'].append(reward_info.total_reward)
        
        # 更新组件奖励统计
        for component, value in reward_info.component_rewards.items():
            if component not in stats['component_rewards']:
                stats['component_rewards'][component] = []
            stats['component_rewards'][component].append(value)
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return {
            'action_statistics': self.action_statistics,
            'reward_statistics': self.reward_statistics,
            'reward_function_stats': self.reward_function.get_reward_statistics(),
            'episode_count': self.episode_count,
            'action_space_size': self.get_action_space_size()
        }
    
    def reset_episode(self):
        """重置回合"""
        self.episode_count += 1
        
        # 清理过期的统计信息（保留最近100个回合的数据）
        if self.episode_count % 100 == 0:
            self._cleanup_statistics()
    
    def _cleanup_statistics(self):
        """清理统计信息"""
        # 保留最近的统计数据，清理过期数据
        for agv_id in self.reward_statistics:
            stats = self.reward_statistics[agv_id]
            if len(stats['total_rewards']) > 1000:
                stats['total_rewards'] = stats['total_rewards'][-1000:]
            
            for component in stats['component_rewards']:
                if len(stats['component_rewards'][component]) > 1000:
                    stats['component_rewards'][component] = stats['component_rewards'][component][-1000:]
    
    def save_config(self, filepath: str):
        """保存配置"""
        import json
        config_dict = {
            'map_width': self.config.map_width,
            'map_height': self.config.map_height,
            'enable_action_masking': self.config.enable_action_masking,
            'enable_reward_shaping': self.config.enable_reward_shaping,
            'reward_normalization': self.config.reward_normalization
        }
        
        if self.config.reward_weights:
            config_dict['reward_weights'] = {
                'task_completion': self.config.reward_weights.task_completion,
                'efficiency': self.config.reward_weights.efficiency,
                'collaboration': self.config.reward_weights.collaboration,
                'safety': self.config.reward_weights.safety,
                'energy': self.config.reward_weights.energy,
                'exploration': self.config.reward_weights.exploration
            }
        
        with open(filepath, 'w') as f:
            json.dump(config_dict, f, indent=2)
    
    @classmethod
    def load_config(cls, filepath: str) -> 'ActionRewardManager':
        """加载配置"""
        import json
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        
        reward_weights = None
        if 'reward_weights' in config_dict:
            reward_weights = RewardWeights(**config_dict['reward_weights'])
        
        config = ActionRewardConfig(
            map_width=config_dict.get('map_width', 26),
            map_height=config_dict.get('map_height', 10),
            reward_weights=reward_weights,
            enable_action_masking=config_dict.get('enable_action_masking', True),
            enable_reward_shaping=config_dict.get('enable_reward_shaping', True),
            reward_normalization=config_dict.get('reward_normalization', True)
        )
        
        return cls(config)
