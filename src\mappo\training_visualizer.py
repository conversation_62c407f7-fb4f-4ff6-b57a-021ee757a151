"""
训练过程实时可视化模块
为MAPPO多AGV协同调度系统提供实时训练指标可视化
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, deque
from datetime import datetime
import threading
import queue
from scipy import interpolate
from scipy.signal import savgol_filter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号


class TrainingVisualizer:
    """训练过程实时可视化器"""
    
    def __init__(self, output_dir: str, update_interval: int = 10):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录
            update_interval: 更新间隔（每多少个episode更新一次）
        """
        self.output_dir = output_dir
        self.update_interval = update_interval
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 数据存储
        self.training_data = defaultdict(list)  # 训练指标数据
        self.agv_data = defaultdict(list)       # AGV性能数据
        self.episode_numbers = []               # episode编号
        
        # 数据缓存（用于移动平均）
        self.data_cache = defaultdict(lambda: deque(maxlen=50))  # 最多保存50个数据点用于移动平均
        
        # 图表配置
        self.figure_size = (15, 10)
        self.dpi = 100
        
        # 异步更新队列
        self.update_queue = queue.Queue()
        self.update_thread = None
        self.stop_event = threading.Event()
        
        print(f"✓ 训练可视化器初始化完成，输出目录: {output_dir}")
    
    def add_training_data(self, episode: int, metrics: Dict):
        """
        添加训练指标数据
        
        Args:
            episode: episode编号
            metrics: 训练指标字典
        """
        # 提取训练指标
        actor_loss = self._extract_metric(metrics, ['info', 'learner', 'default_policy', 'learner_stats', 'policy_loss'], 0.0)
        critic_loss = self._extract_metric(metrics, ['info', 'learner', 'default_policy', 'learner_stats', 'vf_loss'], 0.0)
        entropy = self._extract_metric(metrics, ['info', 'learner', 'default_policy', 'learner_stats', 'entropy'], 0.0)
        episode_reward = self._extract_metric(metrics, ['env_runners', 'episode_reward_mean'], 0.0)
        
        # 存储数据
        self.training_data['actor_loss'].append(abs(actor_loss))  # 取绝对值便于显示
        self.training_data['critic_loss'].append(critic_loss)
        self.training_data['entropy'].append(entropy)
        self.training_data['episode_reward'].append(episode_reward)
        
        # 更新缓存
        self.data_cache['actor_loss'].append(abs(actor_loss))
        self.data_cache['critic_loss'].append(critic_loss)
        self.data_cache['entropy'].append(entropy)
        self.data_cache['episode_reward'].append(episode_reward)
        
        if episode not in self.episode_numbers:
            self.episode_numbers.append(episode)
    
    def add_agv_data(self, episode: int, agv_metrics: Dict):
        """
        添加AGV性能数据
        
        Args:
            episode: episode编号
            agv_metrics: AGV性能指标字典
        """
        # 提取AGV指标
        task_completion_rate = agv_metrics.get('task_completion_rate', 0.0) * 100  # 转换为百分比
        load_utilization = agv_metrics.get('load_utilization', 0.0) * 100  # 转换为百分比
        collision_count = agv_metrics.get('collision_count', 0)
        avg_path_length = agv_metrics.get('avg_path_length', 0.0)
        
        # 存储数据
        self.agv_data['task_completion_rate'].append(task_completion_rate)
        self.agv_data['load_utilization'].append(load_utilization)
        self.agv_data['collision_count'].append(collision_count)
        self.agv_data['avg_path_length'].append(avg_path_length)
        
        # 更新缓存
        self.data_cache['task_completion_rate'].append(task_completion_rate)
        self.data_cache['load_utilization'].append(load_utilization)
        self.data_cache['collision_count'].append(collision_count)
        self.data_cache['avg_path_length'].append(avg_path_length)
    
    def _extract_metric(self, data: Dict, path: List[str], default_value=0.0):
        """
        从嵌套字典中提取指标值

        Args:
            data: 数据字典
            path: 键路径列表
            default_value: 默认值

        Returns:
            提取的值或默认值
        """
        try:
            current = data
            for key in path:
                current = current[key]
            return float(current) if current is not None else default_value
        except (KeyError, TypeError, ValueError):
            return default_value

    def _calculate_moving_average(self, data: List[float], window_size: int = 20) -> List[float]:
        """
        计算加权移动平均（更平滑）

        Args:
            data: 数据列表
            window_size: 窗口大小

        Returns:
            移动平均值列表
        """
        if len(data) < 3:
            return data.copy()

        # 使用指数加权移动平均
        alpha = 2.0 / (window_size + 1)
        moving_avg = [data[0]]

        for i in range(1, len(data)):
            ema = alpha * data[i] + (1 - alpha) * moving_avg[-1]
            moving_avg.append(ema)

        return moving_avg

    def _calculate_smooth_curve(self, x_data: List[int], y_data: List[float]) -> Tuple[List[float], List[float]]:
        """
        计算平滑曲线（使用样条插值和Savitzky-Golay滤波）

        Args:
            x_data: X轴数据
            y_data: Y轴数据

        Returns:
            平滑的X和Y坐标
        """
        if len(x_data) < 5:
            return x_data.copy(), y_data.copy()

        try:
            # 首先使用Savitzky-Golay滤波器平滑数据
            window_length = min(len(y_data), 11)  # 窗口长度必须是奇数
            if window_length % 2 == 0:
                window_length -= 1
            if window_length < 3:
                window_length = 3

            polyorder = min(3, window_length - 1)
            y_smooth = savgol_filter(y_data, window_length, polyorder)

            # 然后使用样条插值进一步平滑
            if len(x_data) >= 4:
                # 创建更密集的x点用于插值
                x_new = np.linspace(min(x_data), max(x_data), len(x_data) * 3)

                # 使用三次样条插值
                tck = interpolate.splrep(x_data, y_smooth, s=0, k=min(3, len(x_data)-1))
                y_new = interpolate.splev(x_new, tck, der=0)

                return x_new.tolist(), y_new.tolist()
            else:
                return x_data.copy(), y_smooth.tolist()

        except (ValueError, TypeError) as e:
            # 如果插值失败，返回原始数据
            return x_data.copy(), y_data.copy()

    def should_update(self, episode: int) -> bool:
        """
        检查是否应该更新图表

        Args:
            episode: 当前episode编号

        Returns:
            是否应该更新
        """
        return episode > 0 and episode % self.update_interval == 0

    def update_charts(self, episode: int):
        """
        更新所有图表

        Args:
            episode: 当前episode编号
        """
        if len(self.episode_numbers) == 0:
            return

        try:
            # 更新训练指标图表
            self._update_training_metrics_chart(episode)

            # 更新AGV性能图表
            self._update_agv_performance_chart(episode)

            print(f"✓ 图表已更新 (Episode {episode})")

        except Exception as e:
            print(f"❌ 更新图表时出错: {e}")

    def _update_training_metrics_chart(self, episode: int):
        """
        更新训练指标图表（只保存一个固定文件）

        Args:
            episode: 当前episode编号
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
        fig.suptitle(f'训练指标 (Episode {episode})', fontsize=16, fontweight='bold')

        # 定义子图配置
        metrics_config = [
            ('actor_loss', 'Actor Loss (策略损失)', axes[0, 0], 'red'),
            ('critic_loss', 'Critic Loss (价值函数损失)', axes[0, 1], 'blue'),
            ('entropy', 'Entropy (熵值)', axes[1, 0], 'green'),
            ('episode_reward', 'Episode Reward (回合奖励)', axes[1, 1], 'orange')
        ]

        for metric_key, title, ax, color in metrics_config:
            self._plot_metric_with_smooth_fitting(
                ax, self.episode_numbers, self.training_data[metric_key],
                title, 'Episode', metric_key, color
            )

        plt.tight_layout()

        # 只保存一个固定文件名的图表
        filepath = os.path.join(self.output_dir, "training_metrics.png")
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()

    def _update_agv_performance_chart(self, episode: int):
        """
        更新AGV性能图表（只保存一个固定文件）

        Args:
            episode: 当前episode编号
        """
        fig, axes = plt.subplots(2, 2, figsize=self.figure_size, dpi=self.dpi)
        fig.suptitle(f'AGV性能指标 (Episode {episode})', fontsize=16, fontweight='bold')

        # 定义子图配置
        metrics_config = [
            ('task_completion_rate', 'AGV任务完成率 (%)', axes[0, 0], 'purple'),
            ('load_utilization', '载重利用率 (%)', axes[0, 1], 'brown'),
            ('collision_count', '碰撞次数 (每episode)', axes[1, 0], 'red'),
            ('avg_path_length', '平均路径长度 (步数)', axes[1, 1], 'navy')
        ]

        for metric_key, title, ax, color in metrics_config:
            if metric_key in self.agv_data and len(self.agv_data[metric_key]) > 0:
                self._plot_metric_with_smooth_fitting(
                    ax, self.episode_numbers[-len(self.agv_data[metric_key]):],
                    self.agv_data[metric_key], title, 'Episode', metric_key, color
                )
            else:
                # 如果没有数据，显示空图表
                ax.set_title(title)
                ax.set_xlabel('Episode')
                ax.set_ylabel(metric_key)
                ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)

        plt.tight_layout()

        # 只保存一个固定文件名的图表
        filepath = os.path.join(self.output_dir, "agv_performance.png")
        plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
        plt.close()

    def _plot_metric_with_smooth_fitting(self, ax, x_data: List[int], y_data: List[float],
                                         title: str, xlabel: str, ylabel: str, color: str):
        """
        绘制带平滑拟合曲线的指标图

        Args:
            ax: matplotlib轴对象
            x_data: X轴数据
            y_data: Y轴数据
            title: 图表标题
            xlabel: X轴标签
            ylabel: Y轴标签
            color: 颜色
        """
        if len(x_data) == 0 or len(y_data) == 0:
            ax.set_title(title)
            ax.set_xlabel(xlabel)
            ax.set_ylabel(ylabel)
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            return

        # 确保数据长度一致
        min_len = min(len(x_data), len(y_data))
        x_data = x_data[:min_len]
        y_data = y_data[:min_len]

        # 绘制散点图（更小更透明）
        ax.scatter(x_data, y_data, alpha=0.4, color=color, s=15, label='原始数据', zorder=1)

        # 绘制指数加权移动平均线
        if len(y_data) >= 3:
            moving_avg = self._calculate_moving_average(y_data, window_size=min(20, len(y_data)//2))
            ax.plot(x_data, moving_avg, color=color, alpha=0.7, linewidth=2, label='指数移动平均', zorder=2)

        # 绘制平滑拟合曲线
        if len(x_data) >= 5:
            x_smooth, y_smooth = self._calculate_smooth_curve(x_data, y_data)
            ax.plot(x_smooth, y_smooth, color=color, alpha=0.9, linewidth=3,
                   linestyle='-', label='平滑拟合曲线', zorder=3)

        # 设置图表属性
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.legend(fontsize=8, loc='best')
        ax.grid(True, alpha=0.3)

        # 设置坐标轴范围
        if len(y_data) > 1:
            y_range = max(y_data) - min(y_data)
            if y_range > 0:
                ax.set_ylim(min(y_data) - y_range * 0.1, max(y_data) + y_range * 0.1)

    def close(self):
        """
        关闭可视化器，清理资源
        """
        self.stop_event.set()
        if self.update_thread and self.update_thread.is_alive():
            self.update_thread.join(timeout=1.0)

        print("✓ 训练可视化器已关闭")
