"""
MAML (Model-Agnostic Meta-Learning) 框架实现
专门针对多AGV协同调度系统的元学习框架
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass
from collections import OrderedDict
import copy

from ..mappo.attention_enhanced_mappo import AttentionEnhancedMAPPOModel


@dataclass
class MAMLConfig:
    """MAML配置类"""
    
    # 元学习参数
    meta_lr: float = 1e-3  # 元学习率
    inner_lr: float = 1e-2  # 内层学习率
    inner_steps: int = 5  # 内层更新步数
    meta_batch_size: int = 16  # 元批次大小
    
    # 任务采样参数
    num_tasks_per_batch: int = 8  # 每批次任务数
    support_shots: int = 10  # 支持集样本数
    query_shots: int = 15  # 查询集样本数
    
    # 网络参数
    feature_dim: int = 64
    hidden_dim: int = 256
    num_heads: int = 8
    dropout: float = 0.1
    
    # 适应参数
    adaptation_steps: int = 3  # 快速适应步数
    adaptation_lr: float = 5e-3  # 适应学习率
    
    # 正则化参数
    l2_reg: float = 1e-4  # L2正则化
    gradient_clip: float = 1.0  # 梯度裁剪
    
    # 训练参数
    max_meta_iterations: int = 1000
    evaluation_interval: int = 50
    patience: int = 100  # 早停耐心值


class MAMLFramework(nn.Module):
    """
    MAML元学习框架
    基于双层注意力机制的MAPPO模型实现元学习
    """
    
    def __init__(self, config: MAMLConfig, obs_space, action_space):
        """
        初始化MAML框架
        
        Args:
            config: MAML配置
            obs_space: 观察空间
            action_space: 动作空间
        """
        super().__init__()
        
        self.config = config
        self.obs_space = obs_space
        self.action_space = action_space
        
        # 创建基础模型（元模型）
        model_config = {
            "custom_model_config": {
                "hidden_dim": config.hidden_dim,
                "feature_dim": config.feature_dim,
                "num_heads": config.num_heads,
                "dropout": config.dropout
            }
        }
        
        self.meta_model = AttentionEnhancedMAPPOModel(
            obs_space=obs_space,
            action_space=action_space,
            num_outputs=action_space.n,
            model_config=model_config,
            name="meta_model"
        )
        
        # 元优化器
        self.meta_optimizer = torch.optim.Adam(
            self.meta_model.parameters(),
            lr=config.meta_lr,
            weight_decay=config.l2_reg
        )
        
        # 统计信息
        self.meta_iteration = 0
        self.best_meta_loss = float('inf')
        self.patience_counter = 0
        
        # 存储元学习历史
        self.meta_loss_history = []
        self.adaptation_performance_history = []
        
    def create_task_model(self) -> AttentionEnhancedMAPPOModel:
        """
        创建任务特定模型（复制元模型）
        
        Returns:
            task_model: 任务特定模型
        """
        model_config = {
            "custom_model_config": {
                "hidden_dim": self.config.hidden_dim,
                "feature_dim": self.config.feature_dim,
                "num_heads": self.config.num_heads,
                "dropout": self.config.dropout
            }
        }
        
        task_model = AttentionEnhancedMAPPOModel(
            obs_space=self.obs_space,
            action_space=self.action_space,
            num_outputs=self.action_space.n,
            model_config=model_config,
            name="task_model"
        )
        
        # 复制元模型参数
        task_model.load_state_dict(self.meta_model.state_dict())
        
        return task_model
    
    def inner_loop_update(self, 
                         task_model: AttentionEnhancedMAPPOModel,
                         support_data: Dict[str, torch.Tensor]) -> AttentionEnhancedMAPPOModel:
        """
        内层循环更新（任务特定适应）
        
        Args:
            task_model: 任务模型
            support_data: 支持集数据
            
        Returns:
            updated_model: 更新后的模型
        """
        # 创建任务特定优化器
        task_optimizer = torch.optim.SGD(
            task_model.parameters(),
            lr=self.config.inner_lr
        )
        
        # 内层更新步骤
        for step in range(self.config.inner_steps):
            # 前向传播
            obs = support_data['observations']
            actions = support_data['actions']
            rewards = support_data['rewards']
            values = support_data['values']
            advantages = support_data['advantages']
            
            # 计算策略损失
            logits, _ = task_model.forward({'obs': obs}, [], None)
            log_probs = F.log_softmax(logits, dim=-1)
            action_log_probs = log_probs.gather(1, actions.unsqueeze(-1)).squeeze(-1)
            
            # PPO损失
            ratio = torch.exp(action_log_probs - support_data['old_log_probs'])
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1 - self.config.gradient_clip, 1 + self.config.gradient_clip) * advantages
            policy_loss = -torch.min(surr1, surr2).mean()
            
            # 价值损失
            predicted_values = task_model.value_function()
            value_loss = F.mse_loss(predicted_values, values)
            
            # 总损失
            total_loss = policy_loss + 0.5 * value_loss
            
            # 反向传播和更新
            task_optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(task_model.parameters(), self.config.gradient_clip)
            task_optimizer.step()
        
        return task_model
    
    def compute_meta_loss(self,
                         task_model: AttentionEnhancedMAPPOModel,
                         query_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        计算元损失（在查询集上）
        
        Args:
            task_model: 适应后的任务模型
            query_data: 查询集数据
            
        Returns:
            meta_loss: 元损失
        """
        # 在查询集上评估适应后的模型
        obs = query_data['observations']
        actions = query_data['actions']
        rewards = query_data['rewards']
        values = query_data['values']
        advantages = query_data['advantages']
        
        # 前向传播
        logits, _ = task_model.forward({'obs': obs}, [], None)
        log_probs = F.log_softmax(logits, dim=-1)
        action_log_probs = log_probs.gather(1, actions.unsqueeze(-1)).squeeze(-1)
        
        # PPO损失
        ratio = torch.exp(action_log_probs - query_data['old_log_probs'])
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.config.gradient_clip, 1 + self.config.gradient_clip) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 价值损失
        predicted_values = task_model.value_function()
        value_loss = F.mse_loss(predicted_values, values)
        
        # 总元损失
        meta_loss = policy_loss + 0.5 * value_loss
        
        return meta_loss
    
    def meta_update(self, task_batch: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        元更新步骤
        
        Args:
            task_batch: 任务批次数据
            
        Returns:
            metrics: 训练指标
        """
        meta_losses = []
        adaptation_performances = []
        
        # 对每个任务进行内层更新和元损失计算
        for task_data in task_batch:
            # 创建任务模型
            task_model = self.create_task_model()
            
            # 内层更新（在支持集上）
            adapted_model = self.inner_loop_update(task_model, task_data['support'])
            
            # 计算元损失（在查询集上）
            meta_loss = self.compute_meta_loss(adapted_model, task_data['query'])
            meta_losses.append(meta_loss)
            
            # 记录适应性能
            with torch.no_grad():
                # 计算适应前后的性能差异
                original_loss = self.compute_meta_loss(task_model, task_data['query'])
                adaptation_improvement = original_loss - meta_loss
                adaptation_performances.append(adaptation_improvement.item())
        
        # 平均元损失
        avg_meta_loss = torch.stack(meta_losses).mean()
        
        # 元梯度更新
        self.meta_optimizer.zero_grad()
        avg_meta_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.meta_model.parameters(), self.config.gradient_clip)
        self.meta_optimizer.step()
        
        # 更新统计信息
        self.meta_iteration += 1
        self.meta_loss_history.append(avg_meta_loss.item())
        avg_adaptation_performance = np.mean(adaptation_performances)
        self.adaptation_performance_history.append(avg_adaptation_performance)
        
        # 早停检查
        if avg_meta_loss.item() < self.best_meta_loss:
            self.best_meta_loss = avg_meta_loss.item()
            self.patience_counter = 0
        else:
            self.patience_counter += 1
        
        return {
            'meta_loss': avg_meta_loss.item(),
            'adaptation_performance': avg_adaptation_performance,
            'meta_iteration': self.meta_iteration,
            'patience_counter': self.patience_counter,
            'best_meta_loss': self.best_meta_loss
        }
    
    def fast_adapt(self, 
                   new_task_data: Dict[str, torch.Tensor],
                   adaptation_steps: Optional[int] = None) -> AttentionEnhancedMAPPOModel:
        """
        快速适应新任务
        
        Args:
            new_task_data: 新任务数据
            adaptation_steps: 适应步数
            
        Returns:
            adapted_model: 适应后的模型
        """
        if adaptation_steps is None:
            adaptation_steps = self.config.adaptation_steps
        
        # 创建任务模型
        adapted_model = self.create_task_model()
        
        # 创建适应优化器
        adapt_optimizer = torch.optim.SGD(
            adapted_model.parameters(),
            lr=self.config.adaptation_lr
        )
        
        # 快速适应步骤
        for step in range(adaptation_steps):
            # 前向传播
            obs = new_task_data['observations']
            actions = new_task_data['actions']
            rewards = new_task_data['rewards']
            values = new_task_data['values']
            advantages = new_task_data['advantages']
            
            logits, _ = adapted_model.forward({'obs': obs}, [], None)
            log_probs = F.log_softmax(logits, dim=-1)
            action_log_probs = log_probs.gather(1, actions.unsqueeze(-1)).squeeze(-1)
            
            # 计算损失
            ratio = torch.exp(action_log_probs - new_task_data['old_log_probs'])
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1 - self.config.gradient_clip, 1 + self.config.gradient_clip) * advantages
            policy_loss = -torch.min(surr1, surr2).mean()
            
            predicted_values = adapted_model.value_function()
            value_loss = F.mse_loss(predicted_values, values)
            
            total_loss = policy_loss + 0.5 * value_loss
            
            # 更新
            adapt_optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(adapted_model.parameters(), self.config.gradient_clip)
            adapt_optimizer.step()
        
        return adapted_model
    
    def save_meta_model(self, path: str):
        """保存元模型"""
        torch.save({
            'meta_model_state_dict': self.meta_model.state_dict(),
            'meta_optimizer_state_dict': self.meta_optimizer.state_dict(),
            'config': self.config,
            'meta_iteration': self.meta_iteration,
            'best_meta_loss': self.best_meta_loss,
            'meta_loss_history': self.meta_loss_history,
            'adaptation_performance_history': self.adaptation_performance_history
        }, path)
    
    def load_meta_model(self, path: str):
        """加载元模型"""
        checkpoint = torch.load(path)
        self.meta_model.load_state_dict(checkpoint['meta_model_state_dict'])
        self.meta_optimizer.load_state_dict(checkpoint['meta_optimizer_state_dict'])
        self.meta_iteration = checkpoint['meta_iteration']
        self.best_meta_loss = checkpoint['best_meta_loss']
        self.meta_loss_history = checkpoint['meta_loss_history']
        self.adaptation_performance_history = checkpoint['adaptation_performance_history']
    
    def get_meta_statistics(self) -> Dict[str, Any]:
        """获取元学习统计信息"""
        return {
            'meta_iteration': self.meta_iteration,
            'best_meta_loss': self.best_meta_loss,
            'current_meta_loss': self.meta_loss_history[-1] if self.meta_loss_history else None,
            'avg_adaptation_performance': np.mean(self.adaptation_performance_history) if self.adaptation_performance_history else 0,
            'meta_loss_trend': np.polyfit(range(len(self.meta_loss_history)), self.meta_loss_history, 1)[0] if len(self.meta_loss_history) > 1 else 0,
            'patience_counter': self.patience_counter,
            'convergence_status': 'converged' if self.patience_counter >= self.config.patience else 'training'
        }
