"""
任务分布生成器
为元学习生成多样化的AGV调度任务
"""

import numpy as np
import random
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum
import copy

from ..mappo.simple_agv_env import SimpleAGVEnvironment, SimpleAGVEntity, SimpleTask


class EnvironmentVariation(Enum):
    """环境变化类型"""
    LAYOUT_CHANGE = "layout_change"  # 布局变化
    AGV_COUNT_CHANGE = "agv_count_change"  # AGV数量变化
    TASK_DISTRIBUTION_CHANGE = "task_distribution_change"  # 任务分布变化
    CAPACITY_CHANGE = "capacity_change"  # 容量变化
    SPEED_CHANGE = "speed_change"  # 速度变化
    OBSTACLE_CHANGE = "obstacle_change"  # 障碍物变化


@dataclass
class TaskVariationConfig:
    """任务变化配置"""
    
    # AGV变化范围
    min_agvs: int = 2
    max_agvs: int = 6
    
    # 任务变化范围
    min_tasks: int = 3
    max_tasks: int = 12
    
    # 地图变化范围
    min_width: int = 15
    max_width: int = 30
    min_height: int = 8
    max_height: int = 12
    
    # 容量变化范围
    min_capacity: int = 15
    max_capacity: int = 35
    
    # 任务密度变化
    min_task_density: float = 0.1
    max_task_density: float = 0.8
    
    # 障碍物密度变化
    min_obstacle_density: float = 0.0
    max_obstacle_density: float = 0.3
    
    # 变化概率
    layout_change_prob: float = 0.3
    agv_count_change_prob: float = 0.4
    task_distribution_change_prob: float = 0.5
    capacity_change_prob: float = 0.2
    obstacle_change_prob: float = 0.3


class TaskDistributionGenerator:
    """
    任务分布生成器
    生成多样化的AGV调度任务用于元学习
    """
    
    def __init__(self, config: TaskVariationConfig):
        """
        初始化任务分布生成器
        
        Args:
            config: 任务变化配置
        """
        self.config = config
        self.base_env_config = {
            'num_agvs': 3,
            'num_tasks': 6,
            'max_episode_steps': 200,
            'map_width': 25,
            'map_height': 10
        }
        
        # 任务模板库
        self.task_templates = self._create_task_templates()
        
        # 环境变化历史
        self.variation_history = []
        
    def _create_task_templates(self) -> List[Dict[str, Any]]:
        """创建任务模板库"""
        templates = []
        
        # 基础模板
        templates.append({
            'name': 'basic_warehouse',
            'description': '基础仓储任务',
            'agv_count': 3,
            'task_count': 6,
            'task_density': 0.3,
            'complexity': 'low'
        })
        
        # 高密度模板
        templates.append({
            'name': 'high_density',
            'description': '高密度任务分布',
            'agv_count': 4,
            'task_count': 10,
            'task_density': 0.7,
            'complexity': 'medium'
        })
        
        # 大规模模板
        templates.append({
            'name': 'large_scale',
            'description': '大规模仓储环境',
            'agv_count': 6,
            'task_count': 12,
            'task_density': 0.5,
            'complexity': 'high'
        })
        
        # 动态模板
        templates.append({
            'name': 'dynamic_tasks',
            'description': '动态任务生成',
            'agv_count': 3,
            'task_count': 8,
            'task_density': 0.4,
            'complexity': 'medium'
        })
        
        # 协作密集模板
        templates.append({
            'name': 'collaboration_intensive',
            'description': '协作密集型任务',
            'agv_count': 5,
            'task_count': 8,
            'task_density': 0.6,
            'complexity': 'high'
        })
        
        return templates
    
    def generate_task_batch(self, batch_size: int, variation_types: Optional[List[EnvironmentVariation]] = None) -> List[Dict[str, Any]]:
        """
        生成任务批次
        
        Args:
            batch_size: 批次大小
            variation_types: 指定的变化类型
            
        Returns:
            task_batch: 任务批次
        """
        if variation_types is None:
            variation_types = list(EnvironmentVariation)
        
        task_batch = []
        
        for i in range(batch_size):
            # 选择基础模板
            template = random.choice(self.task_templates)
            
            # 生成变化
            variations = self._generate_variations(variation_types)
            
            # 创建任务配置
            task_config = self._apply_variations(template, variations)
            
            # 生成环境实例
            env_config = self._create_env_config(task_config)
            environment = SimpleAGVEnvironment(env_config)
            
            # 生成支持集和查询集数据
            support_data = self._generate_episode_data(environment, self.config.min_tasks)
            query_data = self._generate_episode_data(environment, self.config.min_tasks)
            
            task_batch.append({
                'task_id': f'task_{i}',
                'template': template['name'],
                'variations': variations,
                'config': task_config,
                'environment': environment,
                'support': support_data,
                'query': query_data
            })
            
            # 记录变化历史
            self.variation_history.append({
                'task_id': f'task_{i}',
                'variations': variations,
                'complexity': template['complexity']
            })
        
        return task_batch
    
    def _generate_variations(self, variation_types: List[EnvironmentVariation]) -> Dict[EnvironmentVariation, Any]:
        """
        生成环境变化
        
        Args:
            variation_types: 变化类型列表
            
        Returns:
            variations: 变化字典
        """
        variations = {}
        
        for var_type in variation_types:
            if var_type == EnvironmentVariation.AGV_COUNT_CHANGE:
                if random.random() < self.config.agv_count_change_prob:
                    variations[var_type] = random.randint(self.config.min_agvs, self.config.max_agvs)
            
            elif var_type == EnvironmentVariation.TASK_DISTRIBUTION_CHANGE:
                if random.random() < self.config.task_distribution_change_prob:
                    variations[var_type] = {
                        'task_count': random.randint(self.config.min_tasks, self.config.max_tasks),
                        'task_density': random.uniform(self.config.min_task_density, self.config.max_task_density)
                    }
            
            elif var_type == EnvironmentVariation.LAYOUT_CHANGE:
                if random.random() < self.config.layout_change_prob:
                    variations[var_type] = {
                        'width': random.randint(self.config.min_width, self.config.max_width),
                        'height': random.randint(self.config.min_height, self.config.max_height)
                    }
            
            elif var_type == EnvironmentVariation.CAPACITY_CHANGE:
                if random.random() < self.config.capacity_change_prob:
                    variations[var_type] = random.randint(self.config.min_capacity, self.config.max_capacity)
            
            elif var_type == EnvironmentVariation.OBSTACLE_CHANGE:
                if random.random() < self.config.obstacle_change_prob:
                    variations[var_type] = random.uniform(self.config.min_obstacle_density, self.config.max_obstacle_density)
        
        return variations
    
    def _apply_variations(self, template: Dict[str, Any], variations: Dict[EnvironmentVariation, Any]) -> Dict[str, Any]:
        """
        应用变化到模板
        
        Args:
            template: 基础模板
            variations: 变化字典
            
        Returns:
            task_config: 任务配置
        """
        task_config = copy.deepcopy(template)
        
        # 应用AGV数量变化
        if EnvironmentVariation.AGV_COUNT_CHANGE in variations:
            task_config['agv_count'] = variations[EnvironmentVariation.AGV_COUNT_CHANGE]
        
        # 应用任务分布变化
        if EnvironmentVariation.TASK_DISTRIBUTION_CHANGE in variations:
            task_dist = variations[EnvironmentVariation.TASK_DISTRIBUTION_CHANGE]
            task_config['task_count'] = task_dist['task_count']
            task_config['task_density'] = task_dist['task_density']
        
        # 应用布局变化
        if EnvironmentVariation.LAYOUT_CHANGE in variations:
            layout = variations[EnvironmentVariation.LAYOUT_CHANGE]
            task_config['map_width'] = layout['width']
            task_config['map_height'] = layout['height']
        
        # 应用容量变化
        if EnvironmentVariation.CAPACITY_CHANGE in variations:
            task_config['agv_capacity'] = variations[EnvironmentVariation.CAPACITY_CHANGE]
        
        # 应用障碍物变化
        if EnvironmentVariation.OBSTACLE_CHANGE in variations:
            task_config['obstacle_density'] = variations[EnvironmentVariation.OBSTACLE_CHANGE]
        
        return task_config
    
    def _create_env_config(self, task_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建环境配置
        
        Args:
            task_config: 任务配置
            
        Returns:
            env_config: 环境配置
        """
        env_config = copy.deepcopy(self.base_env_config)
        
        # 更新配置
        env_config['num_agvs'] = task_config.get('agv_count', 3)
        env_config['num_tasks'] = task_config.get('task_count', 6)
        env_config['map_width'] = task_config.get('map_width', 25)
        env_config['map_height'] = task_config.get('map_height', 10)
        
        # 计算最大步数（基于复杂度）
        complexity_multiplier = {
            'low': 1.0,
            'medium': 1.5,
            'high': 2.0
        }
        base_steps = 200
        multiplier = complexity_multiplier.get(task_config.get('complexity', 'medium'), 1.5)
        env_config['max_episode_steps'] = int(base_steps * multiplier)
        
        return env_config
    
    def _generate_episode_data(self, environment: SimpleAGVEnvironment, num_episodes: int) -> Dict[str, Any]:
        """
        生成回合数据
        
        Args:
            environment: 环境实例
            num_episodes: 回合数
            
        Returns:
            episode_data: 回合数据
        """
        observations = []
        actions = []
        rewards = []
        values = []
        advantages = []
        old_log_probs = []
        
        for episode in range(num_episodes):
            # 重置环境
            obs = environment.reset()
            episode_obs = []
            episode_actions = []
            episode_rewards = []
            
            done = False
            step = 0
            max_steps = 50  # 限制每个回合的最大步数
            
            while not done and step < max_steps:
                # 随机动作（用于数据生成）
                action = random.randint(0, 6)
                
                # 执行动作
                agv_actions = {0: self._convert_action_to_string(action)}
                step_result = environment.step(agv_actions)
                
                # 记录数据
                episode_obs.append(self._create_dummy_observation())
                episode_actions.append(action)
                episode_rewards.append(step_result['rewards'].get(0, 0.0))
                
                step += 1
                done = environment.is_episode_complete()
            
            # 添加到总数据
            observations.extend(episode_obs)
            actions.extend(episode_actions)
            rewards.extend(episode_rewards)
            
            # 生成虚拟的价值和优势（用于训练）
            episode_values = [random.uniform(-1, 1) for _ in episode_obs]
            episode_advantages = [random.uniform(-0.5, 0.5) for _ in episode_obs]
            episode_log_probs = [random.uniform(-2, 0) for _ in episode_obs]
            
            values.extend(episode_values)
            advantages.extend(episode_advantages)
            old_log_probs.extend(episode_log_probs)
        
        # 转换为张量
        return {
            'observations': torch.tensor(observations, dtype=torch.float32),
            'actions': torch.tensor(actions, dtype=torch.long),
            'rewards': torch.tensor(rewards, dtype=torch.float32),
            'values': torch.tensor(values, dtype=torch.float32),
            'advantages': torch.tensor(advantages, dtype=torch.float32),
            'old_log_probs': torch.tensor(old_log_probs, dtype=torch.float32)
        }
    
    def _convert_action_to_string(self, action: int) -> str:
        """转换动作索引到字符串"""
        action_mapping = {
            0: 'up', 1: 'down', 2: 'left', 3: 'right',
            4: 'stay', 5: 'load', 6: 'unload'
        }
        return action_mapping.get(action, 'stay')
    
    def _create_dummy_observation(self) -> List[float]:
        """创建虚拟观察（224维）"""
        return [random.uniform(-1, 1) for _ in range(224)]
    
    def get_variation_statistics(self) -> Dict[str, Any]:
        """获取变化统计信息"""
        if not self.variation_history:
            return {}
        
        # 统计各种变化的频率
        variation_counts = {}
        complexity_counts = {'low': 0, 'medium': 0, 'high': 0}
        
        for record in self.variation_history:
            for var_type in record['variations']:
                if var_type not in variation_counts:
                    variation_counts[var_type] = 0
                variation_counts[var_type] += 1
            
            complexity = record.get('complexity', 'medium')
            complexity_counts[complexity] += 1
        
        total_tasks = len(self.variation_history)
        
        return {
            'total_tasks_generated': total_tasks,
            'variation_frequencies': {str(k): v/total_tasks for k, v in variation_counts.items()},
            'complexity_distribution': {k: v/total_tasks for k, v in complexity_counts.items()},
            'average_variations_per_task': sum(len(record['variations']) for record in self.variation_history) / total_tasks
        }


# 导入torch（在文件顶部添加）
import torch
