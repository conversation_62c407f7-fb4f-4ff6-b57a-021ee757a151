# 深度学习框架
torch>=1.12.0
torchvision>=0.13.0
torch-audio>=0.12.0

# 强化学习库
gymnasium>=0.26.0
stable-baselines3>=1.6.0
ray[rllib]>=2.0.0
marllib>=1.0.3

# MARLlib相关依赖
gym==0.20.0
protobuf==3.20.3
dm-tree>=0.1.6

# 数值计算
numpy>=1.21.0
scipy>=1.8.0

# 数据处理
pandas>=1.4.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# 监控和日志
tensorboard>=2.8.0
wandb>=0.12.0
tqdm>=4.64.0

# 配置管理
pyyaml>=6.0
hydra-core>=1.1.0
omegaconf>=2.1.0

# 图像处理
opencv-python>=4.5.0
pillow>=9.0.0

# 网络和通信
requests>=2.27.0

# 测试
pytest>=7.0.0
pytest-cov>=3.0.0

# 代码质量
black>=22.0.0
flake8>=4.0.0
isort>=5.10.0

# 其他工具
psutil>=5.9.0
colorlog>=6.6.0
rich>=12.0.0