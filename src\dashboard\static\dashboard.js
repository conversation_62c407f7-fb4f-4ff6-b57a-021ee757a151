/**
 * AGV训练监控仪表板 JavaScript
 * 处理实时数据更新、图表渲染和用户交互
 */

class AGVDashboard {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.charts = {};
        this.metrics = {
            episodes: [],
            rewards: [],
            losses: [],
            success_rates: []
        };
        
        this.init();
    }
    
    init() {
        console.log('🚀 初始化AGV训练监控仪表板...');
        
        // 初始化WebSocket连接
        this.initWebSocket();
        
        // 初始化图表
        this.initCharts();
        
        // 设置定时器
        this.setupTimers();
        
        // 绑定事件
        this.bindEvents();
        
        console.log('✅ 仪表板初始化完成');
    }
    
    initWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        console.log(`🔌 连接WebSocket: ${wsUrl}`);
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = (event) => {
            console.log('✅ WebSocket连接已建立');
            this.isConnected = true;
            this.updateConnectionStatus(true);
            this.addLog('WebSocket连接已建立');
        };
        
        this.websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleWebSocketMessage(message);
            } catch (error) {
                console.error('❌ 解析WebSocket消息失败:', error);
            }
        };
        
        this.websocket.onclose = (event) => {
            console.log('🔌 WebSocket连接已关闭');
            this.isConnected = false;
            this.updateConnectionStatus(false);
            this.addLog('WebSocket连接已断开');
            
            // 尝试重连
            setTimeout(() => {
                console.log('🔄 尝试重新连接WebSocket...');
                this.initWebSocket();
            }, 5000);
        };
        
        this.websocket.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
            this.addLog('WebSocket连接错误');
        };
    }
    
    handleWebSocketMessage(message) {
        console.log('📨 收到WebSocket消息:', message.type);
        
        switch (message.type) {
            case 'metrics_update':
                this.updateMetrics(message.data);
                break;
            case 'attention_update':
                this.updateAttentionVisualization(message.data);
                break;
            case 'training_control':
                this.handleTrainingControl(message);
                break;
            case 'pong':
                console.log('🏓 收到pong响应');
                break;
            default:
                console.log('❓ 未知消息类型:', message.type);
        }
    }
    
    initCharts() {
        console.log('📊 初始化图表...');
        
        // 奖励曲线图
        const rewardCtx = document.getElementById('rewardChart').getContext('2d');
        this.charts.reward = new Chart(rewardCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '平均奖励',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    }
                }
            }
        });
        
        // 损失曲线图
        const lossCtx = document.getElementById('lossChart').getContext('2d');
        this.charts.loss = new Chart(lossCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '策略损失',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: '价值损失',
                        data: [],
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    }
                }
            }
        });
        
        console.log('✅ 图表初始化完成');
    }
    
    updateMetrics(data) {
        console.log('📈 更新指标数据');
        
        // 更新指标卡片
        if (data.current_episode !== undefined) {
            document.getElementById('currentEpisode').textContent = data.current_episode;
        }
        
        if (data.average_reward !== undefined) {
            document.getElementById('averageReward').textContent = data.average_reward.toFixed(2);
        }
        
        if (data.success_rate !== undefined) {
            document.getElementById('successRate').textContent = `${(data.success_rate * 100).toFixed(1)}%`;
        }
        
        if (data.elapsed_time !== undefined) {
            document.getElementById('elapsedTime').textContent = this.formatTime(data.elapsed_time);
        }
        
        // 更新图表数据
        if (data.episode_rewards && data.episode_rewards.length > 0) {
            this.updateRewardChart(data.episode_rewards);
        }
        
        if (data.losses) {
            this.updateLossChart(data.losses);
        }
        
        // 更新训练状态
        if (data.training_status) {
            this.updateTrainingStatus(data.training_status);
        }
    }
    
    updateRewardChart(rewards) {
        const chart = this.charts.reward;
        const maxPoints = 100; // 最多显示100个点
        
        // 更新数据
        chart.data.labels = rewards.slice(-maxPoints).map((_, index) => index + 1);
        chart.data.datasets[0].data = rewards.slice(-maxPoints);
        
        chart.update('none'); // 无动画更新以提高性能
    }
    
    updateLossChart(losses) {
        const chart = this.charts.loss;
        const maxPoints = 100;
        
        if (losses.policy_loss && losses.value_loss) {
            chart.data.labels = losses.policy_loss.slice(-maxPoints).map((_, index) => index + 1);
            chart.data.datasets[0].data = losses.policy_loss.slice(-maxPoints);
            chart.data.datasets[1].data = losses.value_loss.slice(-maxPoints);
            
            chart.update('none');
        }
    }
    
    updateAttentionVisualization(data) {
        console.log('👁️ 更新注意力可视化');
        
        // 更新任务分配注意力热图
        if (data.task_attention_weights) {
            this.updateTaskAttentionHeatmap(data.task_attention_weights);
        }
        
        // 更新协作感知注意力网络
        if (data.collaboration_attention_weights) {
            this.updateCollaborationNetwork(data.collaboration_attention_weights);
        }
    }
    
    updateTaskAttentionHeatmap(weights) {
        const data = [{
            z: weights,
            type: 'heatmap',
            colorscale: 'Viridis',
            showscale: true
        }];
        
        const layout = {
            title: '任务分配注意力权重',
            xaxis: { title: '任务' },
            yaxis: { title: 'AGV' },
            margin: { t: 50, l: 50, r: 50, b: 50 }
        };
        
        Plotly.newPlot('taskAttentionHeatmap', data, layout, {responsive: true});
    }
    
    updateCollaborationNetwork(weights) {
        // 创建网络图数据
        const nodes = [];
        const edges = [];
        
        // 添加AGV节点
        for (let i = 0; i < weights.length; i++) {
            nodes.push({
                x: Math.cos(2 * Math.PI * i / weights.length),
                y: Math.sin(2 * Math.PI * i / weights.length),
                text: `AGV ${i}`,
                mode: 'markers+text',
                marker: { size: 20, color: 'lightblue' }
            });
        }
        
        // 添加连接边
        for (let i = 0; i < weights.length; i++) {
            for (let j = 0; j < weights[i].length; j++) {
                if (i !== j && weights[i][j] > 0.1) { // 只显示权重大于0.1的连接
                    edges.push({
                        x: [nodes[i].x, nodes[j].x, null],
                        y: [nodes[i].y, nodes[j].y, null],
                        mode: 'lines',
                        line: { width: weights[i][j] * 10, color: 'gray' },
                        hoverinfo: 'none'
                    });
                }
            }
        }
        
        const data = [...edges, ...nodes];
        const layout = {
            title: 'AGV协作关系网络',
            showlegend: false,
            xaxis: { visible: false },
            yaxis: { visible: false },
            margin: { t: 50, l: 50, r: 50, b: 50 }
        };
        
        Plotly.newPlot('collaborationAttentionNetwork', data, layout, {responsive: true});
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connectionStatus');
        if (connected) {
            statusElement.className = 'badge bg-success';
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> 已连接';
        } else {
            statusElement.className = 'badge bg-danger';
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> 连接断开';
        }
    }
    
    updateTrainingStatus(status) {
        const indicator = document.getElementById('trainingStatusIndicator');
        const text = document.getElementById('trainingStatusText');
        
        switch (status) {
            case 'training':
                indicator.className = 'status-indicator status-training';
                text.textContent = '训练中';
                break;
            case 'paused':
                indicator.className = 'status-indicator status-paused';
                text.textContent = '已暂停';
                break;
            case 'stopped':
                indicator.className = 'status-indicator status-stopped';
                text.textContent = '已停止';
                break;
            default:
                indicator.className = 'status-indicator status-stopped';
                text.textContent = '未知状态';
        }
    }
    
    addLog(message) {
        const logsContainer = document.getElementById('systemLogs');
        const timestamp = new Date().toLocaleString();
        
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span>
            <span>${message}</span>
        `;
        
        logsContainer.appendChild(logEntry);
        logsContainer.scrollTop = logsContainer.scrollHeight;
        
        // 限制日志条数
        const maxLogs = 100;
        while (logsContainer.children.length > maxLogs) {
            logsContainer.removeChild(logsContainer.firstChild);
        }
    }
    
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    
    setupTimers() {
        // 定期发送ping保持连接
        setInterval(() => {
            if (this.isConnected) {
                this.sendMessage({ type: 'ping' });
            }
        }, 30000); // 30秒
        
        // 定期请求指标更新
        setInterval(() => {
            if (this.isConnected) {
                this.sendMessage({ type: 'request_metrics' });
            }
        }, 5000); // 5秒
    }
    
    bindEvents() {
        // 页面卸载时关闭WebSocket
        window.addEventListener('beforeunload', () => {
            if (this.websocket) {
                this.websocket.close();
            }
        });
    }
    
    sendMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        }
    }
}

// 控制函数
function startTraining() {
    fetch('/api/control/start_training', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            dashboard.addLog('训练已开始');
            console.log('✅ 训练已开始');
        })
        .catch(error => {
            dashboard.addLog('启动训练失败: ' + error.message);
            console.error('❌ 启动训练失败:', error);
        });
}

function pauseTraining() {
    fetch('/api/control/pause_training', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            dashboard.addLog('训练已暂停');
            console.log('⏸️ 训练已暂停');
        })
        .catch(error => {
            dashboard.addLog('暂停训练失败: ' + error.message);
            console.error('❌ 暂停训练失败:', error);
        });
}

function stopTraining() {
    fetch('/api/control/stop_training', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            dashboard.addLog('训练已停止');
            console.log('🛑 训练已停止');
        })
        .catch(error => {
            dashboard.addLog('停止训练失败: ' + error.message);
            console.error('❌ 停止训练失败:', error);
        });
}

function exportData() {
    dashboard.addLog('正在导出数据...');
    // TODO: 实现数据导出功能
    console.log('📥 导出数据功能待实现');
}

function resetMetrics() {
    dashboard.addLog('正在重置指标...');
    // TODO: 实现指标重置功能
    console.log('🔄 重置指标功能待实现');
}

function clearLogs() {
    const logsContainer = document.getElementById('systemLogs');
    logsContainer.innerHTML = '';
    console.log('🗑️ 日志已清空');
}

// 初始化仪表板
let dashboard;
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new AGVDashboard();
});
