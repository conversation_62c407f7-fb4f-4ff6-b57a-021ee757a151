"""
基础可视化工具
提供训练过程中的基本图表和可视化功能
"""

import matplotlib.pyplot as plt
import matplotlib.style as style
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import sqlite3
import json
from datetime import datetime

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
style.use('seaborn-v0_8')
sns.set_palette("husl")


class BasicVisualizer:
    """
    基础可视化工具
    提供训练指标的基本图表和可视化
    """
    
    def __init__(self, db_path: str, save_dir: str = "./monitoring_plots"):
        """
        初始化可视化工具
        
        Args:
            db_path: 数据库路径
            save_dir: 图表保存目录
        """
        self.db_path = db_path
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # 图表配置
        self.figsize = (12, 8)
        self.dpi = 100
        
        print(f"✓ 基础可视化工具初始化完成")
        print(f"  数据库: {db_path}")
        print(f"  图表保存目录: {self.save_dir}")
    
    def load_training_data(self) -> pd.DataFrame:
        """
        从数据库加载训练数据
        
        Returns:
            df: 训练数据DataFrame
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                df = pd.read_sql_query("""
                    SELECT * FROM training_metrics 
                    ORDER BY timestamp
                """, conn)
                
                # 转换时间戳
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
                
                return df
        except Exception as e:
            print(f"❌ 加载训练数据失败: {e}")
            return pd.DataFrame()
    
    def load_attention_data(self) -> pd.DataFrame:
        """
        从数据库加载注意力数据
        
        Returns:
            df: 注意力数据DataFrame
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                df = pd.read_sql_query("""
                    SELECT * FROM attention_metrics 
                    ORDER BY timestamp
                """, conn)
                
                # 转换时间戳
                df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
                
                return df
        except Exception as e:
            print(f"❌ 加载注意力数据失败: {e}")
            return pd.DataFrame()
    
    def plot_training_curves(self, save: bool = True) -> str:
        """
        绘制训练曲线
        
        Args:
            save: 是否保存图表
            
        Returns:
            filepath: 保存的文件路径
        """
        df = self.load_training_data()
        if df.empty:
            print("❌ 没有训练数据可绘制")
            return ""
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('训练过程监控', fontsize=16, fontweight='bold')
        
        # 奖励曲线
        axes[0, 0].plot(df['episode'], df['episode_reward'], alpha=0.7, linewidth=1)
        if len(df) > 10:
            # 添加移动平均线
            window = min(50, len(df) // 10)
            rolling_mean = df['episode_reward'].rolling(window=window).mean()
            axes[0, 0].plot(df['episode'], rolling_mean, color='red', linewidth=2, label=f'{window}回合移动平均')
            axes[0, 0].legend()
        axes[0, 0].set_title('回合奖励')
        axes[0, 0].set_xlabel('回合数')
        axes[0, 0].set_ylabel('奖励')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 损失曲线
        axes[0, 1].plot(df['episode'], df['policy_loss'], label='策略损失', alpha=0.8)
        axes[0, 1].plot(df['episode'], df['value_loss'], label='价值损失', alpha=0.8)
        axes[0, 1].plot(df['episode'], df['total_loss'], label='总损失', alpha=0.8)
        axes[0, 1].set_title('损失函数')
        axes[0, 1].set_xlabel('回合数')
        axes[0, 1].set_ylabel('损失')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 任务完成率
        axes[1, 0].plot(df['episode'], df['task_completion_rate'] * 100, color='green', alpha=0.8)
        axes[1, 0].set_title('任务完成率')
        axes[1, 0].set_xlabel('回合数')
        axes[1, 0].set_ylabel('完成率 (%)')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_ylim(0, 100)
        
        # 协作效率
        axes[1, 1].plot(df['episode'], df['coordination_efficiency'], color='purple', alpha=0.8)
        axes[1, 1].set_title('协作效率')
        axes[1, 1].set_xlabel('回合数')
        axes[1, 1].set_ylabel('效率')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = self.save_dir / f"training_curves_{timestamp}.png"
            plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            print(f"✓ 训练曲线已保存: {filepath}")
            return str(filepath)
        else:
            plt.show()
            return ""
    
    def plot_attention_analysis(self, save: bool = True) -> str:
        """
        绘制注意力机制分析图
        
        Args:
            save: 是否保存图表
            
        Returns:
            filepath: 保存的文件路径
        """
        df = self.load_attention_data()
        if df.empty:
            print("❌ 没有注意力数据可绘制")
            return ""
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('注意力机制分析', fontsize=16, fontweight='bold')
        
        # 任务注意力熵
        axes[0, 0].plot(df['episode'], df['task_attention_entropy'], color='blue', alpha=0.8)
        axes[0, 0].set_title('任务注意力熵')
        axes[0, 0].set_xlabel('回合数')
        axes[0, 0].set_ylabel('熵值')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 协作注意力熵
        axes[0, 1].plot(df['episode'], df['collaboration_attention_entropy'], color='red', alpha=0.8)
        axes[0, 1].set_title('协作注意力熵')
        axes[0, 1].set_xlabel('回合数')
        axes[0, 1].set_ylabel('熵值')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 注意力稀疏性对比
        axes[1, 0].plot(df['episode'], df['task_attention_sparsity'], label='任务注意力', alpha=0.8)
        axes[1, 0].plot(df['episode'], df['collaboration_attention_sparsity'], label='协作注意力', alpha=0.8)
        axes[1, 0].set_title('注意力稀疏性')
        axes[1, 0].set_xlabel('回合数')
        axes[1, 0].set_ylabel('稀疏性')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 最大注意力权重
        axes[1, 1].plot(df['episode'], df['task_attention_max_weight'], label='任务注意力', alpha=0.8)
        axes[1, 1].plot(df['episode'], df['collaboration_attention_max_weight'], label='协作注意力', alpha=0.8)
        axes[1, 1].set_title('最大注意力权重')
        axes[1, 1].set_xlabel('回合数')
        axes[1, 1].set_ylabel('权重')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = self.save_dir / f"attention_analysis_{timestamp}.png"
            plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            print(f"✓ 注意力分析图已保存: {filepath}")
            return str(filepath)
        else:
            plt.show()
            return ""
    
    def plot_performance_summary(self, save: bool = True) -> str:
        """
        绘制性能总结图
        
        Args:
            save: 是否保存图表
            
        Returns:
            filepath: 保存的文件路径
        """
        df = self.load_training_data()
        if df.empty:
            print("❌ 没有训练数据可绘制")
            return ""
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        fig.suptitle('性能总结报告', fontsize=16, fontweight='bold')
        
        # 奖励分布直方图
        axes[0, 0].hist(df['episode_reward'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(df['episode_reward'].mean(), color='red', linestyle='--', 
                          label=f'平均值: {df["episode_reward"].mean():.2f}')
        axes[0, 0].set_title('奖励分布')
        axes[0, 0].set_xlabel('奖励')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 回合长度分布
        axes[0, 1].hist(df['episode_length'], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].axvline(df['episode_length'].mean(), color='red', linestyle='--',
                          label=f'平均值: {df["episode_length"].mean():.1f}')
        axes[0, 1].set_title('回合长度分布')
        axes[0, 1].set_xlabel('步数')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 学习率变化
        axes[0, 2].plot(df['episode'], df['learning_rate'], color='orange', alpha=0.8)
        axes[0, 2].set_title('学习率变化')
        axes[0, 2].set_xlabel('回合数')
        axes[0, 2].set_ylabel('学习率')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 梯度范数
        axes[1, 0].plot(df['episode'], df['grad_norm'], color='purple', alpha=0.8)
        axes[1, 0].set_title('梯度范数')
        axes[1, 0].set_xlabel('回合数')
        axes[1, 0].set_ylabel('范数')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 碰撞统计
        axes[1, 1].plot(df['episode'], df['collision_count'], color='red', alpha=0.8)
        axes[1, 1].set_title('碰撞次数')
        axes[1, 1].set_xlabel('回合数')
        axes[1, 1].set_ylabel('次数')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 平均任务时间
        axes[1, 2].plot(df['episode'], df['avg_task_time'], color='brown', alpha=0.8)
        axes[1, 2].set_title('平均任务时间')
        axes[1, 2].set_xlabel('回合数')
        axes[1, 2].set_ylabel('时间 (步)')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = self.save_dir / f"performance_summary_{timestamp}.png"
            plt.savefig(filepath, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            print(f"✓ 性能总结图已保存: {filepath}")
            return str(filepath)
        else:
            plt.show()
            return ""
    
    def generate_training_report(self) -> Dict[str, Any]:
        """
        生成训练报告
        
        Returns:
            report: 训练报告字典
        """
        df = self.load_training_data()
        if df.empty:
            return {"error": "没有训练数据"}
        
        attention_df = self.load_attention_data()
        
        report = {
            "生成时间": datetime.now().isoformat(),
            "训练统计": {
                "总回合数": int(df['episode'].max()) if not df.empty else 0,
                "总步数": int(df['step'].max()) if not df.empty else 0,
                "平均奖励": float(df['episode_reward'].mean()) if not df.empty else 0,
                "最高奖励": float(df['episode_reward'].max()) if not df.empty else 0,
                "平均回合长度": float(df['episode_length'].mean()) if not df.empty else 0,
                "平均任务完成率": float(df['task_completion_rate'].mean()) if not df.empty else 0,
                "平均协作效率": float(df['coordination_efficiency'].mean()) if not df.empty else 0
            },
            "损失统计": {
                "平均策略损失": float(df['policy_loss'].mean()) if not df.empty else 0,
                "平均价值损失": float(df['value_loss'].mean()) if not df.empty else 0,
                "平均总损失": float(df['total_loss'].mean()) if not df.empty else 0
            },
            "注意力统计": {}
        }
        
        if not attention_df.empty:
            report["注意力统计"] = {
                "平均任务注意力熵": float(attention_df['task_attention_entropy'].mean()),
                "平均协作注意力熵": float(attention_df['collaboration_attention_entropy'].mean()),
                "平均任务注意力稀疏性": float(attention_df['task_attention_sparsity'].mean()),
                "平均协作注意力稀疏性": float(attention_df['collaboration_attention_sparsity'].mean())
            }
        
        return report
    
    def create_dashboard_plots(self) -> Dict[str, str]:
        """
        创建仪表板用的所有图表
        
        Returns:
            filepaths: 图表文件路径字典
        """
        filepaths = {}
        
        print("正在生成仪表板图表...")
        
        # 生成各种图表
        filepaths['training_curves'] = self.plot_training_curves(save=True)
        filepaths['attention_analysis'] = self.plot_attention_analysis(save=True)
        filepaths['performance_summary'] = self.plot_performance_summary(save=True)
        
        print(f"✓ 仪表板图表生成完成，共 {len(filepaths)} 个图表")
        
        return filepaths
