"""
MAPPO算法配置
基于双层注意力机制的MAPPO配置参数
"""

from dataclasses import dataclass
from typing import Dict, Any, List, Optional


@dataclass
class MAPPOConfig:
    """MAPPO算法配置类"""
    
    # 基础算法参数
    learning_rate: float = 3e-4
    batch_size: int = 256
    num_epochs: int = 10
    clip_param: float = 0.2
    value_loss_coeff: float = 0.5
    entropy_coeff: float = 0.01
    max_grad_norm: float = 0.5
    
    # 注意力机制参数
    attention_loss_coeff: float = 0.1
    attention_regularization: bool = True
    task_attention_weight: float = 1.0
    collaboration_attention_weight: float = 1.0
    
    # 网络架构参数
    hidden_dim: int = 256
    feature_dim: int = 64
    num_heads: int = 8
    dropout: float = 0.1
    activation: str = "relu"
    layer_norm: bool = True
    
    # 训练参数
    num_workers: int = 4
    num_envs_per_worker: int = 1
    rollout_fragment_length: int = 200
    train_batch_size: int = 4000
    sgd_minibatch_size: int = 256
    
    # GAE参数
    gamma: float = 0.99
    lambda_: float = 0.95
    use_gae: bool = True
    
    # 评估参数
    evaluation_interval: int = 10
    evaluation_num_episodes: int = 10
    evaluation_parallel: bool = True
    
    # 检查点参数
    checkpoint_freq: int = 50
    keep_checkpoints_num: int = 5
    checkpoint_at_end: bool = True
    
    # 停止条件
    max_timesteps: int = 1000000
    max_episodes: int = 10000
    target_reward: float = 1000.0
    patience: int = 100  # 早停耐心值
    
    # 课程学习参数
    curriculum_enabled: bool = True
    curriculum_stages: List[str] = None
    curriculum_transition_threshold: float = 800.0
    curriculum_evaluation_episodes: int = 20
    
    # 日志和监控
    log_level: str = "INFO"
    tensorboard_log: bool = True
    wandb_log: bool = False
    wandb_project: str = "agv_mappo"
    
    # 实验参数
    seed: int = 42
    deterministic: bool = False
    
    def __post_init__(self):
        """初始化后处理"""
        if self.curriculum_stages is None:
            self.curriculum_stages = ["stage1", "stage2", "stage3"]
        
        # 计算SGD小批次大小
        if self.sgd_minibatch_size > self.batch_size:
            self.sgd_minibatch_size = self.batch_size // 4
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            # PPO基础配置
            "framework": "torch",
            "lr": self.learning_rate,
            "train_batch_size": self.train_batch_size,
            "sgd_minibatch_size": self.sgd_minibatch_size,
            "num_sgd_iter": self.num_epochs,
            "clip_param": self.clip_param,
            "vf_loss_coeff": self.value_loss_coeff,
            "entropy_coeff": self.entropy_coeff,
            "grad_clip": self.max_grad_norm,
            
            # GAE配置
            "gamma": self.gamma,
            "lambda": self.lambda_,
            "use_gae": self.use_gae,
            
            # 并行配置
            "num_workers": self.num_workers,
            "num_envs_per_worker": self.num_envs_per_worker,
            "rollout_fragment_length": self.rollout_fragment_length,
            
            # 评估配置
            "evaluation_interval": self.evaluation_interval,
            "evaluation_num_episodes": self.evaluation_num_episodes,
            "evaluation_parallel": self.evaluation_parallel,
            "evaluation_config": {
                "explore": False,
            },
            
            # 模型配置
            "model": {
                "custom_model": "attention_enhanced_mappo",
                "custom_model_config": {
                    "hidden_dim": self.hidden_dim,
                    "feature_dim": self.feature_dim,
                    "num_heads": self.num_heads,
                    "dropout": self.dropout,
                    "activation": self.activation,
                    "layer_norm": self.layer_norm
                }
            },
            
            # 多智能体配置
            "multiagent": {
                "policies_to_train": ["default_policy"],
                "policy_mapping_fn": lambda agent_id: "default_policy",
            },
            
            # 自定义配置
            "custom_config": {
                "attention_loss_coeff": self.attention_loss_coeff,
                "attention_regularization": self.attention_regularization,
                "task_attention_weight": self.task_attention_weight,
                "collaboration_attention_weight": self.collaboration_attention_weight,
                "curriculum_enabled": self.curriculum_enabled,
                "curriculum_transition_threshold": self.curriculum_transition_threshold
            },
            
            # 日志配置
            "log_level": self.log_level,
            "callbacks": None,
            
            # 其他配置
            "seed": self.seed,
            "deterministic_ops": self.deterministic
        }


# 预定义配置
MAPPO_CONFIGS = {
    "default": MAPPOConfig(),
    
    "fast_training": MAPPOConfig(
        learning_rate=5e-4,
        batch_size=128,
        num_epochs=5,
        train_batch_size=2000,
        num_workers=2,
        evaluation_interval=5,
        checkpoint_freq=25
    ),
    
    "high_performance": MAPPOConfig(
        learning_rate=1e-4,
        batch_size=512,
        num_epochs=15,
        train_batch_size=8000,
        num_workers=8,
        hidden_dim=512,
        feature_dim=128,
        num_heads=16,
        max_timesteps=5000000
    ),
    
    "curriculum_learning": MAPPOConfig(
        curriculum_enabled=True,
        curriculum_stages=["stage1", "stage2", "stage3"],
        curriculum_transition_threshold=800.0,
        curriculum_evaluation_episodes=30,
        evaluation_interval=5,
        checkpoint_freq=25
    ),
    
    "debug": MAPPOConfig(
        learning_rate=1e-3,
        batch_size=32,
        num_epochs=2,
        train_batch_size=200,
        num_workers=1,
        hidden_dim=64,
        feature_dim=32,
        num_heads=2,
        evaluation_interval=2,
        checkpoint_freq=5,
        max_timesteps=10000
    )
}


def get_mappo_config(config_name: str = "default") -> MAPPOConfig:
    """
    获取MAPPO配置
    
    Args:
        config_name: 配置名称
        
    Returns:
        config: MAPPO配置对象
    """
    if config_name not in MAPPO_CONFIGS:
        raise ValueError(f"未知的配置名称: {config_name}. 可用配置: {list(MAPPO_CONFIGS.keys())}")
    
    return MAPPO_CONFIGS[config_name]


def create_custom_mappo_config(**kwargs) -> MAPPOConfig:
    """
    创建自定义MAPPO配置
    
    Args:
        **kwargs: 配置参数
        
    Returns:
        config: 自定义MAPPO配置对象
    """
    base_config = MAPPO_CONFIGS["default"]
    
    # 更新配置参数
    for key, value in kwargs.items():
        if hasattr(base_config, key):
            setattr(base_config, key, value)
        else:
            print(f"警告: 未知的配置参数 {key}")
    
    return base_config


# 课程学习配置
CURRICULUM_MAPPO_CONFIGS = {
    "stage1": MAPPOConfig(
        learning_rate=5e-4,
        batch_size=128,
        num_epochs=8,
        train_batch_size=2000,
        evaluation_interval=5,
        target_reward=500.0,
        hidden_dim=128,
        feature_dim=32,
        num_heads=4
    ),
    
    "stage2": MAPPOConfig(
        learning_rate=3e-4,
        batch_size=256,
        num_epochs=10,
        train_batch_size=4000,
        evaluation_interval=8,
        target_reward=750.0,
        hidden_dim=256,
        feature_dim=64,
        num_heads=8
    ),
    
    "stage3": MAPPOConfig(
        learning_rate=1e-4,
        batch_size=512,
        num_epochs=12,
        train_batch_size=6000,
        evaluation_interval=10,
        target_reward=1000.0,
        hidden_dim=512,
        feature_dim=128,
        num_heads=16
    )
}


def get_curriculum_config(stage: str) -> MAPPOConfig:
    """
    获取课程学习配置
    
    Args:
        stage: 学习阶段
        
    Returns:
        config: 对应阶段的MAPPO配置
    """
    if stage not in CURRICULUM_MAPPO_CONFIGS:
        raise ValueError(f"未知的课程学习阶段: {stage}. 可用阶段: {list(CURRICULUM_MAPPO_CONFIGS.keys())}")
    
    return CURRICULUM_MAPPO_CONFIGS[stage]


# 超参数搜索空间
HYPERPARAMETER_SEARCH_SPACE = {
    "learning_rate": [1e-4, 3e-4, 5e-4, 1e-3],
    "batch_size": [128, 256, 512],
    "num_epochs": [5, 10, 15],
    "clip_param": [0.1, 0.2, 0.3],
    "entropy_coeff": [0.001, 0.01, 0.1],
    "hidden_dim": [128, 256, 512],
    "feature_dim": [32, 64, 128],
    "num_heads": [4, 8, 16],
    "attention_loss_coeff": [0.01, 0.1, 0.5]
}


def get_search_space() -> Dict[str, List]:
    """
    获取超参数搜索空间
    
    Returns:
        search_space: 超参数搜索空间字典
    """
    return HYPERPARAMETER_SEARCH_SPACE.copy()
