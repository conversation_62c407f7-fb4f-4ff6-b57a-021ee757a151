"""
注意力机制预训练训练器
实现双层注意力机制的预训练流程
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import os
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from tqdm import tqdm
import matplotlib.pyplot as plt

from .data_generator import AttentionPretrainingDataGenerator, PretrainingBatch, PretrainingScenarioType
from .loss_functions import AttentionPretrainingLoss, CurriculumLossScheduler, PretrainingLossOutput
from ..attention.task_allocation_attention import MultiHeadTaskAllocationAttention
from ..attention.collaboration_attention import HierarchicalCollaborationAttention
from config.env_config import EnvironmentConfig


@dataclass
class PretrainingConfig:
    """预训练配置"""
    # 模型参数
    feature_dim: int = 64
    num_heads: int = 8
    dropout: float = 0.1
    
    # 训练参数
    learning_rate: float = 1e-3
    batch_size: int = 32
    num_epochs: int = 100
    warmup_epochs: int = 10
    
    # 损失权重
    task_allocation_weight: float = 1.0
    collaboration_weight: float = 1.0
    consistency_weight: float = 0.5
    sparsity_weight: float = 0.1
    
    # 课程学习
    curriculum_enabled: bool = True
    curriculum_stages: List[str] = None
    stage_epochs: List[int] = None
    
    # 数据生成
    samples_per_epoch: int = 1000
    validation_split: float = 0.2
    
    # 保存和日志
    save_dir: str = "./pretraining_checkpoints"
    log_interval: int = 10
    save_interval: int = 50
    
    def __post_init__(self):
        if self.curriculum_stages is None:
            self.curriculum_stages = ['easy', 'medium', 'hard']
        if self.stage_epochs is None:
            self.stage_epochs = [30, 40, 30]


class PretrainingDataset(Dataset):
    """预训练数据集"""
    
    def __init__(self, batches: List[PretrainingBatch]):
        """
        初始化数据集
        
        Args:
            batches: 预训练批次列表
        """
        self.batches = batches
        
        # 展开所有样本
        self.samples = []
        for batch in batches:
            batch_size = batch.agv_features.shape[0]
            for i in range(batch_size):
                sample = {
                    'agv_features': batch.agv_features[i],
                    'task_features': batch.task_features[i],
                    'agv_positions': batch.agv_positions[i],
                    'task_positions': batch.task_positions[i],
                    'optimal_task_allocation': batch.optimal_task_allocation[i],
                    'collaboration_targets': batch.collaboration_targets[i],
                    'scenario_type': batch.scenario_types[i],
                    'difficulty_level': batch.difficulty_levels[i]
                }
                self.samples.append(sample)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        return self.samples[idx]


class AttentionPretrainingModel(nn.Module):
    """注意力预训练模型"""
    
    def __init__(self, config: PretrainingConfig):
        """
        初始化预训练模型
        
        Args:
            config: 预训练配置
        """
        super().__init__()
        
        self.config = config
        
        # 任务分配注意力机制
        self.task_attention = MultiHeadTaskAllocationAttention(
            feature_dim=config.feature_dim,
            num_heads=config.num_heads,
            dropout=config.dropout
        )
        
        # 协作感知注意力机制
        self.collaboration_attention = HierarchicalCollaborationAttention(
            feature_dim=config.feature_dim,
            pos_dim=32,
            num_heads=config.num_heads,
            dropout=config.dropout
        )
        
        # 特征投影层
        self.agv_projection = nn.Linear(config.feature_dim, config.feature_dim)
        self.task_projection = nn.Linear(config.feature_dim, config.feature_dim)
    
    def forward(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            batch_data: 批次数据
            
        Returns:
            predictions: 预测结果
        """
        agv_features = batch_data['agv_features']  # [B, N_agv, D]
        task_features = batch_data['task_features']  # [B, N_task, D]
        agv_positions = batch_data['agv_positions']  # [B, N_agv, 2]
        
        batch_size = agv_features.shape[0]
        
        # 特征投影
        agv_features = self.agv_projection(agv_features)
        task_features = self.task_projection(task_features)
        
        # 计算任务分配注意力
        # 计算AGV和任务之间的距离
        agv_pos = agv_positions.unsqueeze(2)  # [B, N_agv, 1, 2]
        task_pos = batch_data['task_positions'].unsqueeze(1)  # [B, 1, N_task, 2]
        distances = torch.norm(agv_pos - task_pos, dim=-1)  # [B, N_agv, N_task]

        # 模拟AGV载重（从特征中提取）
        agv_loads = agv_features[:, :, 2]  # [B, N_agv] 假设第3维是载重

        # 模拟任务权重和优先级（从特征中提取）
        task_weights = task_features[:, :, 5]  # [B, N_task] 假设第6维是权重
        task_priorities = task_features[:, :, 4]  # [B, N_task] 假设第5维是优先级

        task_attention_output = self.task_attention(
            agv_features, task_features, distances, agv_loads, task_weights, task_priorities
        )
        task_attention_weights = task_attention_output.attention_weights
        
        # 计算协作感知注意力
        collaboration_output = self.collaboration_attention(
            agv_features, agv_positions, agv_features  # 使用AGV特征作为第一层输出
        )
        collaboration_weights = collaboration_output.collaboration_weights
        
        return {
            'task_attention_weights': task_attention_weights,
            'collaboration_weights': collaboration_weights,
            'task_attended_features': task_attention_output.attended_features,
            'collaboration_features': collaboration_output.collaborated_features
        }


class AttentionPretrainingTrainer:
    """注意力机制预训练训练器"""
    
    def __init__(self, config: PretrainingConfig):
        """
        初始化预训练训练器
        
        Args:
            config: 预训练配置
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        self.model = AttentionPretrainingModel(config).to(self.device)
        
        # 创建损失函数
        self.criterion = AttentionPretrainingLoss(
            task_allocation_weight=config.task_allocation_weight,
            collaboration_weight=config.collaboration_weight,
            consistency_weight=config.consistency_weight,
            sparsity_weight=config.sparsity_weight
        )
        
        # 创建优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-4
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.num_epochs,
            eta_min=config.learning_rate * 0.01
        )
        
        # 数据生成器
        env_config = EnvironmentConfig()
        self.data_generator = AttentionPretrainingDataGenerator(
            env_config, config.feature_dim
        )
        
        # 课程学习调度器
        if config.curriculum_enabled:
            initial_weights = {
                'task_allocation': 0.5,
                'collaboration': 0.3,
                'consistency': 0.1,
                'sparsity': 0.1
            }
            final_weights = {
                'task_allocation': config.task_allocation_weight,
                'collaboration': config.collaboration_weight,
                'consistency': config.consistency_weight,
                'sparsity': config.sparsity_weight
            }
            self.curriculum_scheduler = CurriculumLossScheduler(
                initial_weights, final_weights, config.warmup_epochs
            )
        
        # 训练历史
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'task_allocation_accuracy': [],
            'collaboration_accuracy': [],
            'attention_entropy': []
        }
        
        # 创建保存目录
        os.makedirs(config.save_dir, exist_ok=True)
    
    def train(self) -> Dict[str, Any]:
        """
        执行预训练
        
        Returns:
            training_results: 训练结果
        """
        print("开始注意力机制预训练...")
        print(f"设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        best_val_loss = float('inf')
        
        if self.config.curriculum_enabled:
            # 课程学习训练
            return self._curriculum_training()
        else:
            # 标准训练
            return self._standard_training()
    
    def _curriculum_training(self) -> Dict[str, Any]:
        """课程学习训练"""
        print("\n开始课程学习训练...")
        
        total_epochs = 0
        stage_results = {}
        
        for stage_idx, (stage, epochs) in enumerate(zip(self.config.curriculum_stages, self.config.stage_epochs)):
            print(f"\n{'='*50}")
            print(f"课程学习阶段 {stage_idx + 1}: {stage}")
            print(f"训练轮数: {epochs}")
            print(f"{'='*50}")
            
            # 生成当前阶段的数据
            train_batches = self.data_generator.generate_curriculum_data(
                stage=stage,
                batch_size=self.config.batch_size,
                num_batches=self.config.samples_per_epoch // self.config.batch_size
            )
            
            val_batches = self.data_generator.generate_curriculum_data(
                stage=stage,
                batch_size=self.config.batch_size,
                num_batches=int(len(train_batches) * self.config.validation_split)
            )
            
            # 训练当前阶段
            stage_result = self._train_stage(
                train_batches, val_batches, epochs, stage, total_epochs
            )
            
            stage_results[stage] = stage_result
            total_epochs += epochs
            
            # 保存阶段检查点
            self._save_checkpoint(f"stage_{stage}", total_epochs)
        
        return {
            'stage_results': stage_results,
            'total_epochs': total_epochs,
            'training_history': self.training_history,
            'final_model_path': self._save_final_model()
        }

    def _train_stage(self,
                    train_batches: List[PretrainingBatch],
                    val_batches: List[PretrainingBatch],
                    epochs: int,
                    stage_name: str,
                    epoch_offset: int = 0) -> Dict[str, Any]:
        """
        训练单个阶段

        Args:
            train_batches: 训练批次
            val_batches: 验证批次
            epochs: 训练轮数
            stage_name: 阶段名称
            epoch_offset: 轮数偏移

        Returns:
            stage_results: 阶段训练结果
        """
        stage_history = {
            'train_loss': [],
            'val_loss': [],
            'task_allocation_accuracy': [],
            'collaboration_accuracy': []
        }

        for epoch in range(epochs):
            global_epoch = epoch_offset + epoch

            # 训练
            train_metrics = self._train_epoch(train_batches, global_epoch)

            # 验证
            val_metrics = self._validate_epoch(val_batches)

            # 记录历史
            stage_history['train_loss'].append(train_metrics['loss'])
            stage_history['val_loss'].append(val_metrics['loss'])
            stage_history['task_allocation_accuracy'].append(val_metrics['task_allocation_accuracy'])
            stage_history['collaboration_accuracy'].append(val_metrics['collaboration_accuracy'])

            # 更新全局历史
            self.training_history['train_loss'].append(train_metrics['loss'])
            self.training_history['val_loss'].append(val_metrics['loss'])
            self.training_history['task_allocation_accuracy'].append(val_metrics['task_allocation_accuracy'])
            self.training_history['collaboration_accuracy'].append(val_metrics['collaboration_accuracy'])
            self.training_history['attention_entropy'].append(val_metrics['attention_entropy'])

            # 日志输出
            if epoch % self.config.log_interval == 0:
                print(f"Epoch {global_epoch:3d} | "
                      f"Train Loss: {train_metrics['loss']:.4f} | "
                      f"Val Loss: {val_metrics['loss']:.4f} | "
                      f"Task Acc: {val_metrics['task_allocation_accuracy']:.3f} | "
                      f"Collab Acc: {val_metrics['collaboration_accuracy']:.3f}")

            # 学习率调度
            self.scheduler.step()

        return stage_history

    def _train_epoch(self, batches: List[PretrainingBatch], epoch: int) -> Dict[str, float]:
        """训练一个轮次"""
        self.model.train()

        total_loss = 0.0
        num_batches = len(batches)

        # 更新课程学习权重
        if self.config.curriculum_enabled and hasattr(self, 'curriculum_scheduler'):
            loss_weights = self.curriculum_scheduler.step()
            self.criterion.task_allocation_weight = loss_weights['task_allocation']
            self.criterion.collaboration_weight = loss_weights['collaboration']
            self.criterion.consistency_weight = loss_weights['consistency']
            self.criterion.sparsity_weight = loss_weights['sparsity']

        for batch in batches:
            batch = batch.to(self.device)

            # 前向传播
            self.optimizer.zero_grad()

            batch_data = {
                'agv_features': batch.agv_features,
                'task_features': batch.task_features,
                'agv_positions': batch.agv_positions,
                'task_positions': batch.task_positions
            }

            predictions = self.model(batch_data)

            # 计算损失
            loss_output = self.criterion(predictions, batch)

            # 反向传播
            loss_output.total_loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            self.optimizer.step()

            total_loss += loss_output.total_loss.item()

        return {'loss': total_loss / num_batches}

    def _validate_epoch(self, batches: List[PretrainingBatch]) -> Dict[str, float]:
        """验证一个轮次"""
        self.model.eval()

        total_loss = 0.0
        total_task_acc = 0.0
        total_collab_acc = 0.0
        total_entropy = 0.0
        num_batches = len(batches)

        with torch.no_grad():
            for batch in batches:
                batch = batch.to(self.device)

                batch_data = {
                    'agv_features': batch.agv_features,
                    'task_features': batch.task_features,
                    'agv_positions': batch.agv_positions,
                    'task_positions': batch.task_positions
                }

                predictions = self.model(batch_data)
                loss_output = self.criterion(predictions, batch)

                total_loss += loss_output.total_loss.item()
                total_task_acc += loss_output.task_allocation_accuracy.item()
                total_collab_acc += loss_output.collaboration_accuracy.item()
                total_entropy += loss_output.attention_entropy.item()

        return {
            'loss': total_loss / num_batches,
            'task_allocation_accuracy': total_task_acc / num_batches,
            'collaboration_accuracy': total_collab_acc / num_batches,
            'attention_entropy': total_entropy / num_batches
        }

    def _save_checkpoint(self, name: str, epoch: int):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'training_history': self.training_history,
            'config': self.config
        }

        checkpoint_path = os.path.join(self.config.save_dir, f"{name}_epoch_{epoch}.pt")
        torch.save(checkpoint, checkpoint_path)
        print(f"检查点已保存: {checkpoint_path}")

    def _save_final_model(self) -> str:
        """保存最终模型"""
        model_path = os.path.join(self.config.save_dir, "final_pretrained_model.pt")
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config
        }, model_path)

        print(f"最终模型已保存: {model_path}")
        return model_path

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.training_history = checkpoint['training_history']

        print(f"检查点已加载: {checkpoint_path}")
        return checkpoint['epoch']

    def plot_training_history(self, save_path: Optional[str] = None):
        """绘制训练历史"""
        try:
            import matplotlib.pyplot as plt

            fig, axes = plt.subplots(2, 2, figsize=(12, 8))

            # 损失曲线
            axes[0, 0].plot(self.training_history['train_loss'], label='Train Loss')
            axes[0, 0].plot(self.training_history['val_loss'], label='Val Loss')
            axes[0, 0].set_title('Training and Validation Loss')
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].legend()
            axes[0, 0].grid(True)

            # 任务分配准确率
            axes[0, 1].plot(self.training_history['task_allocation_accuracy'])
            axes[0, 1].set_title('Task Allocation Accuracy')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Accuracy')
            axes[0, 1].grid(True)

            # 协作准确率
            axes[1, 0].plot(self.training_history['collaboration_accuracy'])
            axes[1, 0].set_title('Collaboration Accuracy')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Accuracy')
            axes[1, 0].grid(True)

            # 注意力熵
            axes[1, 1].plot(self.training_history['attention_entropy'])
            axes[1, 1].set_title('Attention Entropy')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Entropy')
            axes[1, 1].grid(True)

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"训练历史图已保存: {save_path}")

            plt.close()  # 关闭图形以避免显示

        except ImportError:
            print("matplotlib未安装，跳过绘图")
        except Exception as e:
            print(f"绘图时出现错误: {e}")
