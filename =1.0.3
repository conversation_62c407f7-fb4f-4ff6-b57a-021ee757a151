Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple, https://pypi.ngc.nvidia.com
Collecting marllib
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/36/4d/29346a82e74e9c032b0051ced4cdcf0a5b75a3ccc538d24c94f89340bfb8/marllib-1.0.3.tar.gz (171 kB)
     -------------------------------------- 171.1/171.1 kB 1.7 MB/s eta 0:00:00
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
INFO: pip is looking at multiple versions of marllib to determine which version is compatible with other requirements. This could take a while.
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/95/8d/23a93b643d2945b66eb7fcde3a6ddb376bc65df5a8341a86b7d2797c2386/marllib-1.0.2.tar.gz (173 kB)
     ---------------------------------------- 173.9/173.9 kB ? eta 0:00:00
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting gym==0.21.0 (from marllib)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/4b/48/920cea66177b865663fde5a9390a59de0ef3b642ad98106ac1d8717d7005/gym-0.21.0.tar.gz (1.5 MB)
     ---------------------------------------- 1.5/1.5 MB 32.7 MB/s eta 0:00:00
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'error'
