"""
第二层协作感知注意力机制
实现AGV间协作关系建模，包括相对位置编码、层次化协作注意力、自适应温度机制等
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import math

from ..utils.state_representation import StateSpaceManager
from ..environment.agv_entity import AGVEntity
from .task_allocation_attention import AttentionOutput


@dataclass
class CollaborationOutput:
    """协作感知注意力输出"""
    collaboration_weights: torch.Tensor  # 协作注意力权重 [batch_size, num_agvs, num_agvs]
    collaborated_features: torch.Tensor  # 协作特征 [batch_size, num_agvs, feature_dim]
    near_attention: torch.Tensor        # 近距离注意力 [batch_size, num_agvs, num_agvs]
    far_attention: torch.Tensor         # 远距离注意力 [batch_size, num_agvs, num_agvs]
    adaptive_temperatures: torch.Tensor  # 自适应温度 [batch_size, num_agvs]
    relative_positions: torch.Tensor    # 相对位置编码 [batch_size, num_agvs, num_agvs, pos_dim]
    metadata: Dict[str, Any]            # 元数据信息


class RelativePositionalEncoding(nn.Module):
    """相对位置编码模块"""
    
    def __init__(self, pos_dim: int = 32, max_distance: float = 50.0):
        """
        初始化相对位置编码
        
        Args:
            pos_dim: 位置编码维度
            max_distance: 最大距离
        """
        super().__init__()
        self.pos_dim = pos_dim
        self.max_distance = max_distance
        
        # 相对位置编码网络
        self.position_net = nn.Sequential(
            nn.Linear(2, pos_dim // 2),  # 输入: [dx, dy]
            nn.ReLU(),
            nn.Linear(pos_dim // 2, pos_dim),
            nn.LayerNorm(pos_dim)
        )
        
        # 距离编码网络
        self.distance_net = nn.Sequential(
            nn.Linear(1, pos_dim // 4),  # 输入: [distance]
            nn.ReLU(),
            nn.Linear(pos_dim // 4, pos_dim // 4),
            nn.Sigmoid()
        )
        
        # 融合网络
        self.fusion_net = nn.Sequential(
            nn.Linear(pos_dim + pos_dim // 4, pos_dim),
            nn.ReLU(),
            nn.LayerNorm(pos_dim)
        )
    
    def forward(self, agv_positions: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            
        Returns:
            relative_encoding: 相对位置编码 [batch_size, num_agvs, num_agvs, pos_dim]
        """
        batch_size, num_agvs, _ = agv_positions.shape
        
        # 计算相对位置差
        positions_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        positions_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]
        
        relative_pos = positions_i - positions_j  # [batch_size, num_agvs, num_agvs, 2]
        
        # 计算欧几里得距离
        distances = torch.norm(relative_pos, dim=-1, keepdim=True)  # [batch_size, num_agvs, num_agvs, 1]
        distances = torch.clamp(distances, 0, self.max_distance) / self.max_distance
        
        # 位置编码
        pos_encoding = self.position_net(relative_pos)  # [batch_size, num_agvs, num_agvs, pos_dim]
        
        # 距离编码
        dist_encoding = self.distance_net(distances)  # [batch_size, num_agvs, num_agvs, pos_dim//4]
        
        # 融合编码
        combined_encoding = torch.cat([pos_encoding, dist_encoding], dim=-1)
        relative_encoding = self.fusion_net(combined_encoding)
        
        return relative_encoding


class AdaptiveTemperature(nn.Module):
    """自适应温度机制"""
    
    def __init__(self, feature_dim: int = 64, context_dim: int = 16):
        """
        初始化自适应温度机制
        
        Args:
            feature_dim: 特征维度
            context_dim: 上下文维度
        """
        super().__init__()
        self.feature_dim = feature_dim
        self.context_dim = context_dim
        
        # 环境复杂度评估网络
        self.complexity_net = nn.Sequential(
            nn.Linear(3, context_dim),  # 输入: [agv_density, task_density, obstacle_density]
            nn.ReLU(),
            nn.Linear(context_dim, context_dim),
            nn.ReLU()
        )
        
        # 个体状态评估网络
        self.individual_net = nn.Sequential(
            nn.Linear(feature_dim, context_dim),
            nn.ReLU(),
            nn.Linear(context_dim, context_dim),
            nn.ReLU()
        )
        
        # 温度计算网络
        self.temperature_net = nn.Sequential(
            nn.Linear(context_dim * 2, context_dim),
            nn.ReLU(),
            nn.Linear(context_dim, 1),
            nn.Sigmoid()  # 确保温度在(0,1)范围内
        )
        
        # 温度缩放参数
        self.temp_scale = nn.Parameter(torch.tensor(2.0))  # 将(0,1)缩放到(0,2)
        self.temp_offset = nn.Parameter(torch.tensor(0.5))  # 最小温度
    
    def forward(self, agv_features: torch.Tensor, 
                environment_complexity: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            agv_features: AGV特征 [batch_size, num_agvs, feature_dim]
            environment_complexity: 环境复杂度 [batch_size, 3]
            
        Returns:
            temperatures: 自适应温度 [batch_size, num_agvs]
        """
        batch_size, num_agvs, _ = agv_features.shape
        
        # 环境复杂度编码
        complexity_encoding = self.complexity_net(environment_complexity)  # [batch_size, context_dim]
        complexity_encoding = complexity_encoding.unsqueeze(1).expand(-1, num_agvs, -1)
        
        # 个体状态编码
        individual_encoding = self.individual_net(agv_features)  # [batch_size, num_agvs, context_dim]
        
        # 融合编码
        combined_encoding = torch.cat([individual_encoding, complexity_encoding], dim=-1)
        
        # 计算温度
        raw_temperatures = self.temperature_net(combined_encoding).squeeze(-1)  # [batch_size, num_agvs]
        temperatures = raw_temperatures * self.temp_scale + self.temp_offset
        
        return temperatures


class HierarchicalCollaborationAttention(nn.Module):
    """层次化协作注意力机制"""
    
    def __init__(self, feature_dim: int = 64, pos_dim: int = 32, 
                 num_heads: int = 8, dropout: float = 0.1,
                 near_threshold: float = 3.0, far_threshold: float = 10.0):
        """
        初始化层次化协作注意力
        
        Args:
            feature_dim: 特征维度
            pos_dim: 位置编码维度
            num_heads: 注意力头数
            dropout: Dropout比率
            near_threshold: 近距离阈值
            far_threshold: 远距离阈值
        """
        super().__init__()
        assert feature_dim % num_heads == 0
        
        self.feature_dim = feature_dim
        self.pos_dim = pos_dim
        self.num_heads = num_heads
        self.head_dim = feature_dim // num_heads
        self.near_threshold = near_threshold
        self.far_threshold = far_threshold
        self.scale = math.sqrt(self.head_dim)
        
        # 协作状态融合
        self.collaboration_fusion = nn.Sequential(
            nn.Linear(feature_dim + pos_dim, feature_dim),
            nn.ReLU(),
            nn.LayerNorm(feature_dim)
        )
        
        # 近距离协作注意力
        self.near_query = nn.Linear(feature_dim, feature_dim)
        self.near_key = nn.Linear(feature_dim, feature_dim)
        self.near_value = nn.Linear(feature_dim, feature_dim)
        
        # 远距离协作注意力
        self.far_query = nn.Linear(feature_dim, feature_dim)
        self.far_key = nn.Linear(feature_dim, feature_dim)
        self.far_value = nn.Linear(feature_dim, feature_dim)
        
        # 层次融合网络
        self.hierarchy_fusion = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim),
            nn.ReLU(),
            nn.LayerNorm(feature_dim)
        )
        
        # 输出投影
        self.output_projection = nn.Linear(feature_dim, feature_dim)
        
        # Dropout和归一化
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(feature_dim)
        
        # 相对位置编码
        self.relative_encoding = RelativePositionalEncoding(pos_dim)
        
        # 自适应温度
        self.adaptive_temp = AdaptiveTemperature(feature_dim)
    
    def _compute_distance_mask(self, agv_positions: torch.Tensor,
                              threshold: float) -> torch.Tensor:
        """计算距离掩码"""
        batch_size, num_agvs, _ = agv_positions.shape

        # 计算距离矩阵
        positions_i = agv_positions.unsqueeze(2)  # [batch_size, num_agvs, 1, 2]
        positions_j = agv_positions.unsqueeze(1)  # [batch_size, 1, num_agvs, 2]

        distances = torch.norm(positions_i - positions_j, dim=-1)  # [batch_size, num_agvs, num_agvs]

        # 创建距离掩码
        mask = (distances <= threshold).float()

        # 排除自己
        eye_mask = torch.eye(num_agvs, device=agv_positions.device).unsqueeze(0)
        mask = mask * (1 - eye_mask)

        # 确保每个AGV至少有一个可以关注的目标（最近的AGV）
        for b in range(batch_size):
            for i in range(num_agvs):
                if mask[b, i].sum() == 0:  # 如果没有任何AGV在阈值范围内
                    # 找到最近的AGV
                    distances_i = distances[b, i]
                    distances_i[i] = float('inf')  # 排除自己
                    nearest_agv = distances_i.argmin()
                    mask[b, i, nearest_agv] = 1.0

        return mask
    
    def _compute_environment_complexity(self, agv_positions: torch.Tensor,
                                      map_size: Tuple[int, int] = (26, 10)) -> torch.Tensor:
        """计算环境复杂度"""
        batch_size, num_agvs, _ = agv_positions.shape
        map_area = map_size[0] * map_size[1]
        
        # AGV密度
        agv_density = num_agvs / map_area
        
        # 任务密度（假设与AGV数量相关）
        task_density = (num_agvs * 2) / map_area  # 假设任务数是AGV数的2倍
        
        # 障碍物密度（假设固定）
        obstacle_density = 0.3  # 30%的区域有障碍物
        
        complexity = torch.tensor([agv_density, task_density, obstacle_density], 
                                device=agv_positions.device).unsqueeze(0).expand(batch_size, -1)
        
        return complexity
    
    def forward(self, agv_features: torch.Tensor, agv_positions: torch.Tensor,
                task_allocation_output: torch.Tensor) -> CollaborationOutput:
        """
        前向传播
        
        Args:
            agv_features: AGV特征 [batch_size, num_agvs, feature_dim]
            agv_positions: AGV位置 [batch_size, num_agvs, 2]
            task_allocation_output: 第一层输出 [batch_size, num_agvs, feature_dim]
            
        Returns:
            collaboration_output: 协作感知输出
        """
        batch_size, num_agvs, _ = agv_features.shape
        
        # 融合AGV状态和第一层输出
        enhanced_features = agv_features + task_allocation_output
        
        # 计算相对位置编码
        relative_positions = self.relative_encoding(agv_positions)
        
        # 计算平均相对位置编码
        avg_relative_pos = relative_positions.mean(dim=2)  # [batch_size, num_agvs, pos_dim]
        
        # 协作状态融合
        collaboration_features = torch.cat([enhanced_features, avg_relative_pos], dim=-1)
        collaboration_features = self.collaboration_fusion(collaboration_features)
        
        # 计算环境复杂度
        environment_complexity = self._compute_environment_complexity(agv_positions)
        
        # 计算自适应温度
        temperatures = self.adaptive_temp(collaboration_features, environment_complexity)
        
        # 计算距离掩码
        near_mask = self._compute_distance_mask(agv_positions, self.near_threshold)
        far_mask = self._compute_distance_mask(agv_positions, self.far_threshold) - near_mask
        far_mask = torch.clamp(far_mask, 0, 1)  # 确保非负
        
        # 近距离协作注意力
        near_queries = self.near_query(collaboration_features)
        near_keys = self.near_key(collaboration_features)
        near_values = self.near_value(collaboration_features)
        
        near_attention_scores = torch.matmul(near_queries, near_keys.transpose(-2, -1)) / self.scale
        
        # 应用温度调节
        near_attention_scores = near_attention_scores / temperatures.unsqueeze(-1)
        
        # 应用近距离掩码
        # 检查是否有行全为0（即没有近距离邻居）
        row_has_neighbors = near_mask.sum(dim=-1) > 0  # [batch_size, num_agvs]

        # 对于有近距离邻居的行，正常计算注意力
        near_attention_scores_masked = near_attention_scores.masked_fill(near_mask == 0, -1e9)
        near_attention_weights = F.softmax(near_attention_scores_masked, dim=-1)

        # 对于没有近距离邻居的行，将注意力权重设为0
        near_attention_weights = near_attention_weights * row_has_neighbors.unsqueeze(-1).float()

        # 确保掩码区域的权重为0
        near_attention_weights = near_attention_weights * near_mask

        near_attention_weights = self.dropout(near_attention_weights)
        near_output = torch.matmul(near_attention_weights, near_values)
        
        # 远距离协作注意力
        far_queries = self.far_query(collaboration_features)
        far_keys = self.far_key(collaboration_features)
        far_values = self.far_value(collaboration_features)
        
        far_attention_scores = torch.matmul(far_queries, far_keys.transpose(-2, -1)) / self.scale
        
        # 应用温度调节
        far_attention_scores = far_attention_scores / temperatures.unsqueeze(-1)
        
        # 应用远距离掩码
        # 检查是否有行全为0（即没有远距离邻居）
        row_has_far_neighbors = far_mask.sum(dim=-1) > 0  # [batch_size, num_agvs]

        # 对于有远距离邻居的行，正常计算注意力
        far_attention_scores_masked = far_attention_scores.masked_fill(far_mask == 0, -1e9)
        far_attention_weights = F.softmax(far_attention_scores_masked, dim=-1)

        # 对于没有远距离邻居的行，将注意力权重设为0
        far_attention_weights = far_attention_weights * row_has_far_neighbors.unsqueeze(-1).float()

        # 确保掩码区域的权重为0
        far_attention_weights = far_attention_weights * far_mask

        far_attention_weights = self.dropout(far_attention_weights)
        far_output = torch.matmul(far_attention_weights, far_values)
        
        # 层次融合
        hierarchical_output = torch.cat([near_output, far_output], dim=-1)
        collaborated_features = self.hierarchy_fusion(hierarchical_output)
        
        # 输出投影
        collaborated_features = self.output_projection(collaborated_features)
        
        # 残差连接和层归一化
        collaborated_features = self.layer_norm(collaborated_features + enhanced_features)
        
        # 计算综合协作权重（使用加权平均而不是简单平均）
        # 归一化近距离和远距离权重
        near_norm = near_attention_weights / (near_attention_weights.sum(dim=-1, keepdim=True) + 1e-8)
        far_norm = far_attention_weights / (far_attention_weights.sum(dim=-1, keepdim=True) + 1e-8)

        # 使用距离掩码作为权重来融合
        near_weight = 0.6  # 近距离权重更高
        far_weight = 0.4   # 远距离权重较低
        collaboration_weights = near_weight * near_norm + far_weight * far_norm
        
        return CollaborationOutput(
            collaboration_weights=collaboration_weights,
            collaborated_features=collaborated_features,
            near_attention=near_attention_weights,
            far_attention=far_attention_weights,
            adaptive_temperatures=temperatures,
            relative_positions=relative_positions,
            metadata={
                'near_threshold': self.near_threshold,
                'far_threshold': self.far_threshold,
                'num_heads': self.num_heads,
                'feature_dim': self.feature_dim
            }
        )


class CollaborationAttentionManager:
    """协作感知注意力机制管理器"""

    def __init__(self, feature_dim: int = 64, pos_dim: int = 32,
                 num_heads: int = 8, dropout: float = 0.1,
                 near_threshold: float = 3.0, far_threshold: float = 10.0):
        """
        初始化协作注意力管理器

        Args:
            feature_dim: 特征维度
            pos_dim: 位置编码维度
            num_heads: 注意力头数
            dropout: Dropout比率
            near_threshold: 近距离阈值
            far_threshold: 远距离阈值
        """
        self.feature_dim = feature_dim
        self.pos_dim = pos_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.near_threshold = near_threshold
        self.far_threshold = far_threshold

        # 创建协作注意力机制
        self.collaboration_attention = HierarchicalCollaborationAttention(
            feature_dim=feature_dim,
            pos_dim=pos_dim,
            num_heads=num_heads,
            dropout=dropout,
            near_threshold=near_threshold,
            far_threshold=far_threshold
        )

        # 统计信息
        self.stats = {
            'total_calls': 0,
            'avg_near_attention_entropy': 0.0,
            'avg_far_attention_entropy': 0.0,
            'avg_temperature': 0.0,
            'collaboration_intensity': 0.0,
            'spatial_distribution': {
                'near_interactions': 0.0,
                'far_interactions': 0.0,
                'total_interactions': 0.0
            }
        }

    def compute_collaboration(self, agvs: List[AGVEntity],
                            agv_embeddings: torch.Tensor,
                            task_allocation_output: torch.Tensor,
                            state_manager: StateSpaceManager) -> CollaborationOutput:
        """
        计算协作感知注意力

        Args:
            agvs: AGV列表
            agv_embeddings: AGV嵌入特征
            task_allocation_output: 第一层任务分配输出
            state_manager: 状态管理器

        Returns:
            collaboration_output: 协作感知输出
        """
        # 提取AGV位置
        agv_positions = torch.tensor([[agv.position[0], agv.position[1]] for agv in agvs],
                                   dtype=torch.float32)

        # 确保有批次维度
        if agv_embeddings.dim() == 2:
            agv_embeddings = agv_embeddings.unsqueeze(0)
        if agv_positions.dim() == 2:
            agv_positions = agv_positions.unsqueeze(0)
        if task_allocation_output.dim() == 2:
            task_allocation_output = task_allocation_output.unsqueeze(0)

        # 计算协作注意力
        collaboration_output = self.collaboration_attention(
            agv_embeddings, agv_positions, task_allocation_output
        )

        # 更新统计信息
        self._update_stats(collaboration_output)

        return collaboration_output

    def _update_stats(self, collaboration_output: CollaborationOutput):
        """更新统计信息"""
        self.stats['total_calls'] += 1

        # 计算近距离注意力熵
        near_attention = collaboration_output.near_attention.squeeze(0)
        near_entropy = -torch.sum(near_attention * torch.log(near_attention + 1e-8), dim=-1).mean()
        self.stats['avg_near_attention_entropy'] = (
            self.stats['avg_near_attention_entropy'] * (self.stats['total_calls'] - 1) + near_entropy.item()
        ) / self.stats['total_calls']

        # 计算远距离注意力熵
        far_attention = collaboration_output.far_attention.squeeze(0)
        far_entropy = -torch.sum(far_attention * torch.log(far_attention + 1e-8), dim=-1).mean()
        self.stats['avg_far_attention_entropy'] = (
            self.stats['avg_far_attention_entropy'] * (self.stats['total_calls'] - 1) + far_entropy.item()
        ) / self.stats['total_calls']

        # 计算平均温度
        temperatures = collaboration_output.adaptive_temperatures.squeeze(0)
        avg_temp = temperatures.mean().item()
        self.stats['avg_temperature'] = (
            self.stats['avg_temperature'] * (self.stats['total_calls'] - 1) + avg_temp
        ) / self.stats['total_calls']

        # 计算协作强度
        collaboration_weights = collaboration_output.collaboration_weights.squeeze(0)
        collaboration_intensity = collaboration_weights.max(dim=-1)[0].mean().item()
        self.stats['collaboration_intensity'] = (
            self.stats['collaboration_intensity'] * (self.stats['total_calls'] - 1) + collaboration_intensity
        ) / self.stats['total_calls']

        # 计算空间分布统计
        near_interactions = (near_attention > 0.1).float().sum().item()
        far_interactions = (far_attention > 0.1).float().sum().item()
        total_interactions = near_interactions + far_interactions

        self.stats['spatial_distribution']['near_interactions'] = (
            self.stats['spatial_distribution']['near_interactions'] * (self.stats['total_calls'] - 1) + near_interactions
        ) / self.stats['total_calls']

        self.stats['spatial_distribution']['far_interactions'] = (
            self.stats['spatial_distribution']['far_interactions'] * (self.stats['total_calls'] - 1) + far_interactions
        ) / self.stats['total_calls']

        self.stats['spatial_distribution']['total_interactions'] = (
            self.stats['spatial_distribution']['total_interactions'] * (self.stats['total_calls'] - 1) + total_interactions
        ) / self.stats['total_calls']

    def get_collaboration_analysis(self, collaboration_output: CollaborationOutput,
                                 agvs: List[AGVEntity]) -> Dict[str, Any]:
        """
        分析协作输出

        Args:
            collaboration_output: 协作输出
            agvs: AGV列表

        Returns:
            analysis: 分析结果
        """
        collaboration_weights = collaboration_output.collaboration_weights.squeeze(0)
        near_attention = collaboration_output.near_attention.squeeze(0)
        far_attention = collaboration_output.far_attention.squeeze(0)
        temperatures = collaboration_output.adaptive_temperatures.squeeze(0)

        analysis = {
            'collaboration_distribution': {
                'max_collaboration': collaboration_weights.max().item(),
                'min_collaboration': collaboration_weights.min().item(),
                'mean_collaboration': collaboration_weights.mean().item(),
                'std_collaboration': collaboration_weights.std().item()
            },
            'attention_hierarchy': {
                'near_attention_strength': near_attention.mean().item(),
                'far_attention_strength': far_attention.mean().item(),
                'near_far_ratio': (near_attention.mean() / (far_attention.mean() + 1e-8)).item()
            },
            'temperature_analysis': {
                'max_temperature': temperatures.max().item(),
                'min_temperature': temperatures.min().item(),
                'mean_temperature': temperatures.mean().item(),
                'std_temperature': temperatures.std().item()
            },
            'collaboration_pairs': [],
            'spatial_patterns': {
                'near_collaborations': (near_attention > 0.1).sum().item(),
                'far_collaborations': (far_attention > 0.1).sum().item(),
                'total_collaborations': (collaboration_weights > 0.1).sum().item()
            }
        }

        # 找出主要协作对
        for i, agv_i in enumerate(agvs):
            for j, agv_j in enumerate(agvs):
                if i != j:
                    collab_score = collaboration_weights[i, j].item()
                    if collab_score > 0.1:  # 只记录显著的协作关系
                        distance = abs(agv_i.position[0] - agv_j.position[0]) + abs(agv_i.position[1] - agv_j.position[1])
                        analysis['collaboration_pairs'].append({
                            'agv_i_id': agv_i.agv_id,
                            'agv_j_id': agv_j.agv_id,
                            'agv_i_position': agv_i.position,
                            'agv_j_position': agv_j.position,
                            'collaboration_score': collab_score,
                            'near_attention': near_attention[i, j].item(),
                            'far_attention': far_attention[i, j].item(),
                            'distance': distance,
                            'temperature_i': temperatures[i].item(),
                            'temperature_j': temperatures[j].item()
                        })

        # 按协作分数排序
        analysis['collaboration_pairs'].sort(key=lambda x: x['collaboration_score'], reverse=True)

        return analysis

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_calls': 0,
            'avg_near_attention_entropy': 0.0,
            'avg_far_attention_entropy': 0.0,
            'avg_temperature': 0.0,
            'collaboration_intensity': 0.0,
            'spatial_distribution': {
                'near_interactions': 0.0,
                'far_interactions': 0.0,
                'total_interactions': 0.0
            }
        }
