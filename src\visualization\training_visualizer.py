"""
训练过程可视化器
集成到MAPPO训练流程中的实时可视化工具
"""

import os
import time
import threading
from typing import Dict, List, Any, Optional
import torch
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from datetime import datetime

from .attention_visualizer import AttentionVisualizer
from .attention_extractor import AttentionDataExtractor


class TrainingVisualizer:
    """
    训练过程可视化器
    实时监控和可视化MAPPO训练过程中的注意力机制
    """
    
    def __init__(self, 
                 save_dir: str = "./training_visualization",
                 update_interval: int = 10,
                 max_history: int = 1000):
        """
        初始化训练可视化器
        
        Args:
            save_dir: 保存目录
            update_interval: 更新间隔（训练步数）
            max_history: 最大历史记录数
        """
        self.save_dir = save_dir
        self.update_interval = update_interval
        self.max_history = max_history
        
        os.makedirs(save_dir, exist_ok=True)
        
        # 创建子组件
        self.attention_visualizer = AttentionVisualizer(save_dir)
        self.attention_extractor = AttentionDataExtractor()
        
        # 训练数据存储
        self.training_metrics = {
            'episode_rewards': [],
            'episode_lengths': [],
            'policy_loss': [],
            'value_loss': [],
            'attention_entropy': [],
            'collaboration_strength': [],
            'task_completion_rate': [],
            'timestamps': []
        }
        
        # 注意力历史
        self.attention_snapshots = []
        
        # 实时更新标志
        self.is_monitoring = False
        self.monitor_thread = None
        
        print(f"✓ 训练可视化器初始化完成，保存目录: {save_dir}")
    
    def start_monitoring(self):
        """开始实时监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            print("✓ 开始实时监控训练过程")
    
    def stop_monitoring(self):
        """停止实时监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("✓ 停止实时监控")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 生成实时可视化
                self._update_real_time_dashboard()
                time.sleep(5)  # 每5秒更新一次
            except Exception as e:
                print(f"监控循环错误: {e}")
                time.sleep(1)
    
    def log_training_step(self, 
                         step: int,
                         episode_reward: float,
                         episode_length: int,
                         policy_loss: float,
                         value_loss: float,
                         attention_data: Optional[Dict] = None):
        """
        记录训练步骤数据
        
        Args:
            step: 训练步数
            episode_reward: 回合奖励
            episode_length: 回合长度
            policy_loss: 策略损失
            value_loss: 价值损失
            attention_data: 注意力数据
        """
        # 记录基础训练指标
        self.training_metrics['episode_rewards'].append(episode_reward)
        self.training_metrics['episode_lengths'].append(episode_length)
        self.training_metrics['policy_loss'].append(policy_loss)
        self.training_metrics['value_loss'].append(value_loss)
        self.training_metrics['timestamps'].append(datetime.now().isoformat())
        
        # 处理注意力数据
        if attention_data:
            attention_entropy = attention_data.get('attention_entropy', 0)
            collaboration_strength = attention_data.get('collaboration_strength', 0)
            task_completion_rate = attention_data.get('task_completion_rate', 0)
            
            self.training_metrics['attention_entropy'].append(attention_entropy)
            self.training_metrics['collaboration_strength'].append(collaboration_strength)
            self.training_metrics['task_completion_rate'].append(task_completion_rate)
            
            # 保存注意力快照
            if step % self.update_interval == 0:
                snapshot = {
                    'step': step,
                    'timestamp': datetime.now().isoformat(),
                    'attention_data': attention_data
                }
                self.attention_snapshots.append(snapshot)
                
                # 限制历史记录数量
                if len(self.attention_snapshots) > self.max_history:
                    self.attention_snapshots.pop(0)
        else:
            # 填充默认值
            self.training_metrics['attention_entropy'].append(0)
            self.training_metrics['collaboration_strength'].append(0)
            self.training_metrics['task_completion_rate'].append(0)
        
        # 限制历史记录数量
        for key in self.training_metrics:
            if len(self.training_metrics[key]) > self.max_history:
                self.training_metrics[key].pop(0)
        
        # 定期生成可视化
        if step % self.update_interval == 0:
            self._generate_training_visualizations(step)
    
    def _generate_training_visualizations(self, step: int):
        """生成训练可视化"""
        try:
            # 1. 训练指标图表
            self._plot_training_metrics(step)
            
            # 2. 注意力分析图表
            if self.attention_snapshots:
                self._plot_attention_analysis(step)
            
            # 3. 保存训练数据
            self._save_training_data(step)
            
            print(f"✓ 步骤 {step}: 训练可视化已更新")
            
        except Exception as e:
            print(f"生成训练可视化时出错: {e}")
    
    def _plot_training_metrics(self, step: int):
        """绘制训练指标"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'MAPPO训练进度 - 步骤 {step}', fontsize=16, fontweight='bold')
        
        metrics = self.training_metrics
        steps = list(range(len(metrics['episode_rewards'])))
        
        # 回合奖励
        axes[0, 0].plot(steps, metrics['episode_rewards'], 'b-', linewidth=2)
        axes[0, 0].set_title('回合奖励')
        axes[0, 0].set_xlabel('训练步数')
        axes[0, 0].set_ylabel('奖励')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 回合长度
        axes[0, 1].plot(steps, metrics['episode_lengths'], 'g-', linewidth=2)
        axes[0, 1].set_title('回合长度')
        axes[0, 1].set_xlabel('训练步数')
        axes[0, 1].set_ylabel('步数')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 策略损失
        axes[0, 2].plot(steps, metrics['policy_loss'], 'r-', linewidth=2)
        axes[0, 2].set_title('策略损失')
        axes[0, 2].set_xlabel('训练步数')
        axes[0, 2].set_ylabel('损失')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 价值损失
        axes[1, 0].plot(steps, metrics['value_loss'], 'orange', linewidth=2)
        axes[1, 0].set_title('价值损失')
        axes[1, 0].set_xlabel('训练步数')
        axes[1, 0].set_ylabel('损失')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 注意力熵
        axes[1, 1].plot(steps, metrics['attention_entropy'], 'purple', linewidth=2)
        axes[1, 1].set_title('注意力熵')
        axes[1, 1].set_xlabel('训练步数')
        axes[1, 1].set_ylabel('熵值')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 协作强度
        axes[1, 2].plot(steps, metrics['collaboration_strength'], 'cyan', linewidth=2)
        axes[1, 2].set_title('协作强度')
        axes[1, 2].set_xlabel('训练步数')
        axes[1, 2].set_ylabel('强度')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        save_path = os.path.join(self.save_dir, f'training_metrics_step_{step}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_attention_analysis(self, step: int):
        """绘制注意力分析"""
        if not self.attention_snapshots:
            return
        
        # 提取注意力数据
        recent_snapshots = self.attention_snapshots[-50:]  # 最近50个快照
        
        attention_entropies = []
        collaboration_strengths = []
        task_completion_rates = []
        snapshot_steps = []
        
        for snapshot in recent_snapshots:
            data = snapshot['attention_data']
            attention_entropies.append(data.get('attention_entropy', 0))
            collaboration_strengths.append(data.get('collaboration_strength', 0))
            task_completion_rates.append(data.get('task_completion_rate', 0))
            snapshot_steps.append(snapshot['step'])
        
        # 创建图表
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle(f'注意力机制分析 - 步骤 {step}', fontsize=16, fontweight='bold')
        
        # 注意力熵变化
        axes[0].plot(snapshot_steps, attention_entropies, 'b-', linewidth=2, marker='o', markersize=4)
        axes[0].set_title('注意力熵变化')
        axes[0].set_xlabel('训练步数')
        axes[0].set_ylabel('熵值')
        axes[0].grid(True, alpha=0.3)
        
        # 协作强度变化
        axes[1].plot(snapshot_steps, collaboration_strengths, 'g-', linewidth=2, marker='s', markersize=4)
        axes[1].set_title('协作强度变化')
        axes[1].set_xlabel('训练步数')
        axes[1].set_ylabel('强度')
        axes[1].grid(True, alpha=0.3)
        
        # 任务完成率变化
        axes[2].plot(snapshot_steps, task_completion_rates, 'r-', linewidth=2, marker='^', markersize=4)
        axes[2].set_title('任务完成率变化')
        axes[2].set_xlabel('训练步数')
        axes[2].set_ylabel('完成率')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        save_path = os.path.join(self.save_dir, f'attention_analysis_step_{step}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def _update_real_time_dashboard(self):
        """更新实时仪表板"""
        if not self.training_metrics['episode_rewards']:
            return
        
        # 创建交互式仪表板
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=('回合奖励', '策略损失', '注意力熵', 
                          '协作强度', '任务完成率', '训练进度'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}, {"secondary_y": False}]]
        )
        
        steps = list(range(len(self.training_metrics['episode_rewards'])))
        
        # 添加图表
        fig.add_trace(
            go.Scatter(x=steps, y=self.training_metrics['episode_rewards'], 
                      mode='lines', name='回合奖励', line=dict(color='blue')),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(x=steps, y=self.training_metrics['policy_loss'], 
                      mode='lines', name='策略损失', line=dict(color='red')),
            row=1, col=2
        )
        
        fig.add_trace(
            go.Scatter(x=steps, y=self.training_metrics['attention_entropy'], 
                      mode='lines', name='注意力熵', line=dict(color='purple')),
            row=1, col=3
        )
        
        fig.add_trace(
            go.Scatter(x=steps, y=self.training_metrics['collaboration_strength'], 
                      mode='lines', name='协作强度', line=dict(color='green')),
            row=2, col=1
        )
        
        fig.add_trace(
            go.Scatter(x=steps, y=self.training_metrics['task_completion_rate'], 
                      mode='lines', name='任务完成率', line=dict(color='orange')),
            row=2, col=2
        )
        
        # 训练进度指示器
        if steps:
            progress = (steps[-1] / 1000) * 100  # 假设总共1000步
            fig.add_trace(
                go.Indicator(
                    mode="gauge+number",
                    value=progress,
                    title={'text': "训练进度 (%)"},
                    gauge={'axis': {'range': [None, 100]},
                           'bar': {'color': "darkblue"},
                           'steps': [{'range': [0, 50], 'color': "lightgray"},
                                   {'range': [50, 100], 'color': "gray"}],
                           'threshold': {'line': {'color': "red", 'width': 4},
                                       'thickness': 0.75, 'value': 90}}
                ),
                row=2, col=3
            )
        
        # 更新布局
        fig.update_layout(
            title_text="MAPPO训练实时监控仪表板",
            title_x=0.5,
            height=800,
            showlegend=False
        )
        
        # 保存实时仪表板
        dashboard_path = os.path.join(self.save_dir, 'real_time_dashboard.html')
        fig.write_html(dashboard_path)
    
    def _save_training_data(self, step: int):
        """保存训练数据"""
        data = {
            'step': step,
            'timestamp': datetime.now().isoformat(),
            'training_metrics': self.training_metrics,
            'attention_snapshots': self.attention_snapshots[-10:]  # 保存最近10个快照
        }
        
        save_path = os.path.join(self.save_dir, f'training_data_step_{step}.json')
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    def generate_final_report(self, total_steps: int):
        """生成最终训练报告"""
        print(f"\n生成最终训练报告...")
        
        # 创建综合报告
        report = {
            'training_summary': {
                'total_steps': total_steps,
                'total_episodes': len(self.training_metrics['episode_rewards']),
                'final_reward': self.training_metrics['episode_rewards'][-1] if self.training_metrics['episode_rewards'] else 0,
                'average_reward': np.mean(self.training_metrics['episode_rewards']) if self.training_metrics['episode_rewards'] else 0,
                'best_reward': np.max(self.training_metrics['episode_rewards']) if self.training_metrics['episode_rewards'] else 0,
                'final_attention_entropy': self.training_metrics['attention_entropy'][-1] if self.training_metrics['attention_entropy'] else 0,
                'average_collaboration_strength': np.mean(self.training_metrics['collaboration_strength']) if self.training_metrics['collaboration_strength'] else 0
            },
            'training_metrics': self.training_metrics,
            'attention_snapshots': self.attention_snapshots,
            'generation_time': datetime.now().isoformat()
        }
        
        # 保存最终报告
        report_path = os.path.join(self.save_dir, 'final_training_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成最终可视化
        self._plot_training_metrics(total_steps)
        if self.attention_snapshots:
            self._plot_attention_analysis(total_steps)
        
        print(f"✓ 最终训练报告已保存: {report_path}")
        return report_path
    
    def get_training_summary(self) -> Dict[str, Any]:
        """获取训练摘要"""
        if not self.training_metrics['episode_rewards']:
            return {}
        
        return {
            'total_episodes': len(self.training_metrics['episode_rewards']),
            'current_reward': self.training_metrics['episode_rewards'][-1],
            'average_reward': np.mean(self.training_metrics['episode_rewards']),
            'best_reward': np.max(self.training_metrics['episode_rewards']),
            'current_attention_entropy': self.training_metrics['attention_entropy'][-1] if self.training_metrics['attention_entropy'] else 0,
            'current_collaboration_strength': self.training_metrics['collaboration_strength'][-1] if self.training_metrics['collaboration_strength'] else 0,
            'attention_snapshots_count': len(self.attention_snapshots)
        }
