"""
协作效率分析器
专门分析多AGV系统的协作效率和团队表现
"""

import numpy as np
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from collections import defaultdict
import seaborn as sns

from ..visualization.trajectory_collector import TrajectoryCollector


@dataclass
class CollaborationMetrics:
    """协作效率指标"""
    episode_id: int
    
    # 空间协作指标
    spatial_efficiency: float  # 空间利用效率
    path_overlap_ratio: float  # 路径重叠比例
    congestion_level: float    # 拥堵程度
    
    # 时间协作指标
    synchronization_score: float  # 同步性得分
    waiting_efficiency: float     # 等待效率
    task_distribution_balance: float  # 任务分配平衡性
    
    # 冲突协作指标
    conflict_resolution_time: float  # 冲突解决时间
    deadlock_frequency: float        # 死锁频率
    collision_avoidance_score: float # 避碰得分
    
    # 整体协作指标
    team_performance_score: float    # 团队表现得分
    coordination_overhead: float     # 协调开销
    scalability_index: float        # 可扩展性指数


class CollaborationAnalyzer:
    """协作效率分析器"""
    
    def __init__(self, trajectory_collector: Optional[TrajectoryCollector] = None):
        """
        初始化协作分析器
        
        Args:
            trajectory_collector: 轨迹收集器
        """
        self.trajectory_collector = trajectory_collector
        self.collaboration_history: List[CollaborationMetrics] = []
        
        print("✓ 协作效率分析器初始化完成")
    
    def analyze_collaboration(self, episode_data: Dict) -> CollaborationMetrics:
        """
        分析单个回合的协作效率
        
        Args:
            episode_data: 回合数据
            
        Returns:
            collaboration_metrics: 协作效率指标
        """
        episode_id = episode_data['metadata']['episode_id']
        trajectories = episode_data.get('trajectories', {})
        task_events = episode_data.get('task_events', [])
        collision_events = episode_data.get('collision_events', [])
        
        # 空间协作分析
        spatial_metrics = self._analyze_spatial_collaboration(trajectories)
        
        # 时间协作分析
        temporal_metrics = self._analyze_temporal_collaboration(trajectories, task_events)
        
        # 冲突协作分析
        conflict_metrics = self._analyze_conflict_collaboration(collision_events, trajectories)
        
        # 整体协作分析
        overall_metrics = self._analyze_overall_collaboration(
            trajectories, task_events, spatial_metrics, temporal_metrics, conflict_metrics
        )
        
        # 创建协作指标对象
        metrics = CollaborationMetrics(
            episode_id=episode_id,
            spatial_efficiency=spatial_metrics['efficiency'],
            path_overlap_ratio=spatial_metrics['overlap_ratio'],
            congestion_level=spatial_metrics['congestion'],
            synchronization_score=temporal_metrics['synchronization'],
            waiting_efficiency=temporal_metrics['waiting_efficiency'],
            task_distribution_balance=temporal_metrics['task_balance'],
            conflict_resolution_time=conflict_metrics['resolution_time'],
            deadlock_frequency=conflict_metrics['deadlock_freq'],
            collision_avoidance_score=conflict_metrics['avoidance_score'],
            team_performance_score=overall_metrics['team_score'],
            coordination_overhead=overall_metrics['overhead'],
            scalability_index=overall_metrics['scalability']
        )
        
        self.collaboration_history.append(metrics)
        return metrics
    
    def _analyze_spatial_collaboration(self, trajectories: Dict) -> Dict[str, float]:
        """分析空间协作效率"""
        if not trajectories:
            return {'efficiency': 0.0, 'overlap_ratio': 0.0, 'congestion': 0.0}
        
        # 提取所有位置
        all_positions = []
        agv_paths = {}
        
        for agv_id, trajectory in trajectories.items():
            path = [point['position'] for point in trajectory]
            agv_paths[agv_id] = path
            all_positions.extend(path)
        
        # 计算空间利用效率
        unique_positions = set(all_positions)
        total_positions = len(all_positions)
        spatial_efficiency = len(unique_positions) / max(total_positions, 1)
        
        # 计算路径重叠比例
        overlap_count = 0
        total_comparisons = 0
        
        agv_ids = list(agv_paths.keys())
        for i in range(len(agv_ids)):
            for j in range(i + 1, len(agv_ids)):
                path1 = set(agv_paths[agv_ids[i]])
                path2 = set(agv_paths[agv_ids[j]])
                overlap = len(path1.intersection(path2))
                total_length = len(path1.union(path2))
                
                if total_length > 0:
                    overlap_count += overlap / total_length
                    total_comparisons += 1
        
        overlap_ratio = overlap_count / max(total_comparisons, 1)
        
        # 计算拥堵程度
        position_counts = defaultdict(int)
        for pos in all_positions:
            position_counts[pos] += 1
        
        max_count = max(position_counts.values()) if position_counts else 1
        avg_count = np.mean(list(position_counts.values())) if position_counts else 1
        congestion_level = max_count / avg_count
        
        return {
            'efficiency': spatial_efficiency,
            'overlap_ratio': overlap_ratio,
            'congestion': min(congestion_level / 5.0, 1.0)  # 归一化
        }
    
    def _analyze_temporal_collaboration(self, trajectories: Dict, task_events: List) -> Dict[str, float]:
        """分析时间协作效率"""
        if not trajectories:
            return {'synchronization': 0.0, 'waiting_efficiency': 0.0, 'task_balance': 0.0}
        
        # 计算同步性得分
        agv_actions = defaultdict(list)
        for agv_id, trajectory in trajectories.items():
            for point in trajectory:
                agv_actions[agv_id].append(point.get('action', 'stay'))
        
        # 计算动作同步性
        synchronization_scores = []
        max_length = max(len(actions) for actions in agv_actions.values())
        
        for step in range(max_length):
            step_actions = []
            for agv_id, actions in agv_actions.items():
                if step < len(actions):
                    step_actions.append(actions[step])
            
            if len(step_actions) > 1:
                # 计算动作多样性（越多样化，同步性越低）
                unique_actions = len(set(step_actions))
                sync_score = 1.0 - (unique_actions - 1) / max(len(step_actions) - 1, 1)
                synchronization_scores.append(sync_score)
        
        synchronization_score = np.mean(synchronization_scores) if synchronization_scores else 0.0
        
        # 计算等待效率
        total_waiting = 0
        total_steps = 0
        
        for agv_id, trajectory in trajectories.items():
            waiting_steps = sum(1 for point in trajectory 
                              if point.get('action') == 'stay' or point.get('status') == 'WAITING')
            total_waiting += waiting_steps
            total_steps += len(trajectory)
        
        waiting_efficiency = 1.0 - (total_waiting / max(total_steps, 1))
        
        # 计算任务分配平衡性
        agv_task_counts = defaultdict(int)
        for event in task_events:
            if event.get('agv_id') is not None:
                agv_task_counts[event['agv_id']] += 1
        
        if agv_task_counts:
            task_counts = list(agv_task_counts.values())
            mean_tasks = np.mean(task_counts)
            std_tasks = np.std(task_counts)
            task_balance = 1.0 - (std_tasks / max(mean_tasks, 1))
        else:
            task_balance = 1.0
        
        return {
            'synchronization': synchronization_score,
            'waiting_efficiency': waiting_efficiency,
            'task_balance': max(0.0, task_balance)
        }
    
    def _analyze_conflict_collaboration(self, collision_events: List, trajectories: Dict) -> Dict[str, float]:
        """分析冲突协作效率"""
        if not trajectories:
            return {'resolution_time': 0.0, 'deadlock_freq': 0.0, 'avoidance_score': 1.0}
        
        total_steps = sum(len(traj) for traj in trajectories.values())
        
        # 计算冲突解决时间
        conflict_durations = []
        for event in collision_events:
            # 简化实现：假设每个冲突事件持续1个时间步
            conflict_durations.append(1.0)
        
        avg_resolution_time = np.mean(conflict_durations) if conflict_durations else 0.0
        
        # 计算死锁频率
        deadlock_events = [e for e in collision_events if e.get('collision_type') == 'deadlock']
        deadlock_frequency = len(deadlock_events) / max(total_steps, 1)
        
        # 计算避碰得分
        collision_count = len(collision_events)
        avoidance_score = max(0.0, 1.0 - collision_count / max(total_steps / 100, 1))
        
        return {
            'resolution_time': avg_resolution_time,
            'deadlock_freq': deadlock_frequency,
            'avoidance_score': avoidance_score
        }
    
    def _analyze_overall_collaboration(self, trajectories: Dict, task_events: List,
                                     spatial_metrics: Dict, temporal_metrics: Dict,
                                     conflict_metrics: Dict) -> Dict[str, float]:
        """分析整体协作效率"""
        
        # 计算团队表现得分（综合指标）
        team_score = (
            spatial_metrics['efficiency'] * 0.3 +
            temporal_metrics['synchronization'] * 0.2 +
            temporal_metrics['waiting_efficiency'] * 0.2 +
            conflict_metrics['avoidance_score'] * 0.3
        )
        
        # 计算协调开销
        total_actions = sum(len(traj) for traj in trajectories.values())
        coordination_actions = sum(1 for traj in trajectories.values() 
                                 for point in traj 
                                 if point.get('action') == 'stay')
        coordination_overhead = coordination_actions / max(total_actions, 1)
        
        # 计算可扩展性指数
        num_agvs = len(trajectories)
        if num_agvs <= 1:
            scalability_index = 1.0
        else:
            # 基于性能随AGV数量的变化
            efficiency_per_agv = team_score / num_agvs
            scalability_index = min(1.0, efficiency_per_agv * num_agvs / 3.0)  # 假设3个AGV为基准
        
        return {
            'team_score': team_score,
            'overhead': coordination_overhead,
            'scalability': scalability_index
        }
    
    def generate_collaboration_network(self, episode_data: Dict, save_path: Optional[str] = None) -> nx.Graph:
        """
        生成协作网络图
        
        Args:
            episode_data: 回合数据
            save_path: 保存路径
            
        Returns:
            graph: 协作网络图
        """
        trajectories = episode_data.get('trajectories', {})
        collision_events = episode_data.get('collision_events', [])
        
        # 创建网络图
        G = nx.Graph()
        
        # 添加AGV节点
        for agv_id in trajectories.keys():
            G.add_node(f"AGV{agv_id}", type='agv')
        
        # 添加协作边（基于碰撞事件和空间接近）
        collaboration_weights = defaultdict(int)
        
        # 基于碰撞事件添加边
        for event in collision_events:
            agv1_id = f"AGV{event['agv1_id']}"
            agv2_id = f"AGV{event['agv2_id']}"
            collaboration_weights[(agv1_id, agv2_id)] += 1
        
        # 基于空间接近添加边
        agv_positions = {}
        for agv_id, trajectory in trajectories.items():
            positions = [point['position'] for point in trajectory]
            agv_positions[f"AGV{agv_id}"] = positions
        
        # 计算AGV间的空间交互
        agv_ids = list(agv_positions.keys())
        for i in range(len(agv_ids)):
            for j in range(i + 1, len(agv_ids)):
                agv1, agv2 = agv_ids[i], agv_ids[j]
                
                # 计算路径接近度
                proximity_count = 0
                for pos1 in agv_positions[agv1]:
                    for pos2 in agv_positions[agv2]:
                        distance = abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])  # 曼哈顿距离
                        if distance <= 2:  # 接近阈值
                            proximity_count += 1
                
                if proximity_count > 0:
                    collaboration_weights[(agv1, agv2)] += proximity_count
        
        # 添加边到图中
        for (agv1, agv2), weight in collaboration_weights.items():
            G.add_edge(agv1, agv2, weight=weight)
        
        # 可视化网络图
        if save_path:
            plt.figure(figsize=(10, 8))
            pos = nx.spring_layout(G, k=1, iterations=50)
            
            # 绘制节点
            nx.draw_networkx_nodes(G, pos, node_color='lightblue', 
                                 node_size=1000, alpha=0.8)
            
            # 绘制边（权重越大，线越粗）
            edges = G.edges()
            weights = [G[u][v]['weight'] for u, v in edges]
            max_weight = max(weights) if weights else 1
            
            nx.draw_networkx_edges(G, pos, width=[w/max_weight*5 for w in weights],
                                 alpha=0.6, edge_color='gray')
            
            # 绘制标签
            nx.draw_networkx_labels(G, pos, font_size=12, font_weight='bold')
            
            # 添加边权重标签
            edge_labels = nx.get_edge_attributes(G, 'weight')
            nx.draw_networkx_edge_labels(G, pos, edge_labels, font_size=8)
            
            plt.title('AGV协作网络图', fontsize=16, fontweight='bold')
            plt.axis('off')
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✓ 协作网络图已保存: {save_path}")
        
        return G
    
    def analyze_collaboration_patterns(self, episodes_data: List[Dict]) -> Dict[str, Any]:
        """
        分析多个回合的协作模式
        
        Args:
            episodes_data: 多个回合的数据
            
        Returns:
            patterns: 协作模式分析结果
        """
        if not episodes_data:
            return {}
        
        # 收集所有协作指标
        all_metrics = []
        for episode_data in episodes_data:
            metrics = self.analyze_collaboration(episode_data)
            all_metrics.append(metrics)
        
        # 转换为DataFrame进行分析
        df = pd.DataFrame([
            {
                'episode_id': m.episode_id,
                'spatial_efficiency': m.spatial_efficiency,
                'synchronization_score': m.synchronization_score,
                'team_performance_score': m.team_performance_score,
                'collision_avoidance_score': m.collision_avoidance_score,
                'coordination_overhead': m.coordination_overhead
            }
            for m in all_metrics
        ])
        
        # 分析协作模式
        patterns = {
            'trends': {},
            'correlations': {},
            'clusters': {},
            'recommendations': []
        }
        
        # 趋势分析
        for col in ['spatial_efficiency', 'synchronization_score', 'team_performance_score']:
            if col in df.columns:
                slope = np.polyfit(df['episode_id'], df[col], 1)[0]
                patterns['trends'][col] = {
                    'slope': slope,
                    'direction': 'improving' if slope > 0 else 'declining' if slope < 0 else 'stable'
                }
        
        # 相关性分析
        correlation_cols = ['spatial_efficiency', 'synchronization_score', 'team_performance_score', 'coordination_overhead']
        if all(col in df.columns for col in correlation_cols):
            corr_matrix = df[correlation_cols].corr()
            patterns['correlations'] = corr_matrix.to_dict()
        
        # 生成建议
        avg_team_score = df['team_performance_score'].mean()
        avg_overhead = df['coordination_overhead'].mean()
        
        if avg_team_score > 0.8:
            patterns['recommendations'].append("团队协作表现优秀，建议保持当前策略")
        elif avg_team_score > 0.6:
            patterns['recommendations'].append("团队协作表现良好，可进一步优化同步性")
        else:
            patterns['recommendations'].append("团队协作需要改进，建议加强冲突避免和任务分配")
        
        if avg_overhead > 0.3:
            patterns['recommendations'].append("协调开销较高，建议优化决策效率")
        
        return patterns
