"""
报告生成器
自动生成性能分析报告，包括图表、统计分析和建议
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime
import os
from pathlib import Path
import json
from jinja2 import Template

from .performance_analyzer import PerformanceAnalyzer, PerformanceMetrics, ModelComparison


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, output_dir: str = "./reports"):
        """
        初始化报告生成器
        
        Args:
            output_dir: 报告输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
        
        print(f"✓ 报告生成器初始化完成，输出目录: {output_dir}")
    
    def generate_performance_report(self, 
                                  analyzer: PerformanceAnalyzer,
                                  report_name: str = "performance_analysis",
                                  include_charts: bool = True,
                                  include_statistics: bool = True) -> str:
        """
        生成性能分析报告
        
        Args:
            analyzer: 性能分析器
            report_name: 报告名称
            include_charts: 是否包含图表
            include_statistics: 是否包含统计分析
            
        Returns:
            report_path: 报告文件路径
        """
        if not analyzer.performance_history:
            raise ValueError("没有可用的性能数据")
        
        # 创建报告目录
        report_dir = self.output_dir / report_name
        report_dir.mkdir(exist_ok=True)
        
        # 转换数据为DataFrame
        df = self._metrics_to_dataframe(analyzer.performance_history)
        
        # 生成图表
        charts_info = []
        if include_charts:
            charts_info = self._generate_performance_charts(df, report_dir)
        
        # 生成统计分析
        statistics = {}
        if include_statistics:
            statistics = self._generate_statistics(df)
        
        # 生成HTML报告
        report_path = self._generate_html_report(
            df, charts_info, statistics, report_dir, report_name
        )
        
        print(f"✓ 性能分析报告已生成: {report_path}")
        return str(report_path)
    
    def generate_model_comparison_report(self,
                                       comparison: ModelComparison,
                                       report_name: str = "model_comparison") -> str:
        """
        生成模型对比报告
        
        Args:
            comparison: 模型对比结果
            report_name: 报告名称
            
        Returns:
            report_path: 报告文件路径
        """
        # 创建报告目录
        report_dir = self.output_dir / report_name
        report_dir.mkdir(exist_ok=True)
        
        # 生成对比图表
        charts_info = self._generate_comparison_charts(comparison, report_dir)
        
        # 生成HTML报告
        report_path = self._generate_comparison_html_report(
            comparison, charts_info, report_dir, report_name
        )
        
        print(f"✓ 模型对比报告已生成: {report_path}")
        return str(report_path)
    
    def _metrics_to_dataframe(self, metrics_list: List[PerformanceMetrics]) -> pd.DataFrame:
        """将性能指标转换为DataFrame"""
        data = [metric.to_dict() for metric in metrics_list]
        df = pd.DataFrame(data)
        
        # 添加时间列
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
        
        return df
    
    def _generate_performance_charts(self, df: pd.DataFrame, output_dir: Path) -> List[Dict[str, str]]:
        """生成性能图表"""
        charts_info = []
        
        # 1. 回合奖励趋势图
        plt.figure(figsize=(12, 6))
        plt.plot(df['episode_id'], df['episode_reward'], marker='o', linewidth=2, markersize=4)
        plt.title('回合奖励趋势', fontsize=16, fontweight='bold')
        plt.xlabel('回合数', fontsize=12)
        plt.ylabel('回合奖励', fontsize=12)
        plt.grid(True, alpha=0.3)
        chart_path = output_dir / 'episode_reward_trend.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        charts_info.append({
            'title': '回合奖励趋势',
            'path': chart_path.name,
            'description': '显示训练过程中回合奖励的变化趋势'
        })
        
        # 2. 成功率和任务完成率
        plt.figure(figsize=(12, 6))
        plt.plot(df['episode_id'], df['success_rate'], label='成功率', marker='o', linewidth=2)
        plt.plot(df['episode_id'], df['task_completion_rate'], label='任务完成率', marker='s', linewidth=2)
        plt.title('成功率与任务完成率', fontsize=16, fontweight='bold')
        plt.xlabel('回合数', fontsize=12)
        plt.ylabel('比率', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        chart_path = output_dir / 'success_rates.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        charts_info.append({
            'title': '成功率与任务完成率',
            'path': chart_path.name,
            'description': '显示任务执行的成功率和完成率变化'
        })
        
        # 3. 协作效率指标
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 碰撞次数
        axes[0, 0].plot(df['episode_id'], df['collision_count'], color='red', marker='o')
        axes[0, 0].set_title('碰撞次数')
        axes[0, 0].set_xlabel('回合数')
        axes[0, 0].set_ylabel('碰撞次数')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 平均等待时间
        axes[0, 1].plot(df['episode_id'], df['avg_waiting_time'], color='orange', marker='s')
        axes[0, 1].set_title('平均等待时间')
        axes[0, 1].set_xlabel('回合数')
        axes[0, 1].set_ylabel('等待时间')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 协作效率
        axes[1, 0].plot(df['episode_id'], df['coordination_efficiency'], color='green', marker='^')
        axes[1, 0].set_title('协作效率')
        axes[1, 0].set_xlabel('回合数')
        axes[1, 0].set_ylabel('效率')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 推理时间
        axes[1, 1].plot(df['episode_id'], df['inference_time'], color='purple', marker='d')
        axes[1, 1].set_title('推理时间')
        axes[1, 1].set_xlabel('回合数')
        axes[1, 1].set_ylabel('时间 (ms)')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        chart_path = output_dir / 'collaboration_metrics.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        charts_info.append({
            'title': '协作效率指标',
            'path': chart_path.name,
            'description': '显示AGV协作相关的各项性能指标'
        })
        
        # 4. 注意力机制分析
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))
        
        axes[0].plot(df['episode_id'], df['attention_entropy'], color='blue', marker='o')
        axes[0].set_title('注意力熵')
        axes[0].set_xlabel('回合数')
        axes[0].set_ylabel('熵值')
        axes[0].grid(True, alpha=0.3)
        
        axes[1].plot(df['episode_id'], df['attention_sparsity'], color='red', marker='s')
        axes[1].set_title('注意力稀疏性')
        axes[1].set_xlabel('回合数')
        axes[1].set_ylabel('稀疏性')
        axes[1].grid(True, alpha=0.3)
        
        axes[2].plot(df['episode_id'], df['attention_stability'], color='green', marker='^')
        axes[2].set_title('注意力稳定性')
        axes[2].set_xlabel('回合数')
        axes[2].set_ylabel('稳定性')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        chart_path = output_dir / 'attention_analysis.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        charts_info.append({
            'title': '注意力机制分析',
            'path': chart_path.name,
            'description': '显示注意力机制的熵、稀疏性和稳定性指标'
        })
        
        # 5. 性能指标相关性热力图
        plt.figure(figsize=(10, 8))
        correlation_cols = [
            'episode_reward', 'success_rate', 'coordination_efficiency',
            'attention_entropy', 'attention_sparsity', 'inference_time'
        ]
        correlation_matrix = df[correlation_cols].corr()
        
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, fmt='.2f', cbar_kws={'shrink': 0.8})
        plt.title('性能指标相关性分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        chart_path = output_dir / 'correlation_heatmap.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        charts_info.append({
            'title': '性能指标相关性分析',
            'path': chart_path.name,
            'description': '显示各项性能指标之间的相关性'
        })
        
        return charts_info
    
    def _generate_comparison_charts(self, comparison: ModelComparison, output_dir: Path) -> List[Dict[str, str]]:
        """生成模型对比图表"""
        charts_info = []
        
        # 1. 性能指标对比雷达图
        metrics_to_plot = ['episode_reward', 'success_rate', 'coordination_efficiency', 'attention_entropy']
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        angles = np.linspace(0, 2 * np.pi, len(metrics_to_plot), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        for i, model_name in enumerate(comparison.model_names):
            values = [comparison.metrics[metric][i] for metric in metrics_to_plot]
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, 'o-', linewidth=2, label=model_name)
            ax.fill(angles, values, alpha=0.25)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics_to_plot)
        ax.set_title('模型性能对比雷达图', fontsize=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        chart_path = output_dir / 'model_comparison_radar.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        charts_info.append({
            'title': '模型性能对比雷达图',
            'path': chart_path.name,
            'description': '多维度对比不同模型的性能表现'
        })
        
        # 2. 性能指标柱状图对比
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        metrics_to_plot = list(comparison.metrics.keys())[:6]  # 取前6个指标
        
        for i, metric in enumerate(metrics_to_plot):
            values = comparison.metrics[metric]
            bars = axes[i].bar(comparison.model_names, values, alpha=0.7)
            axes[i].set_title(f'{metric}')
            axes[i].set_ylabel('数值')
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                axes[i].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{value:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        chart_path = output_dir / 'model_comparison_bars.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        charts_info.append({
            'title': '模型性能指标对比',
            'path': chart_path.name,
            'description': '各项性能指标的详细数值对比'
        })
        
        return charts_info
    
    def _generate_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """生成统计分析"""
        statistics = {}
        
        # 基础统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        statistics['descriptive'] = df[numeric_cols].describe().to_dict()
        
        # 趋势分析
        statistics['trends'] = {}
        for col in ['episode_reward', 'success_rate', 'coordination_efficiency']:
            if col in df.columns:
                # 计算线性趋势
                x = df['episode_id'].values
                y = df[col].values
                slope = np.polyfit(x, y, 1)[0]
                statistics['trends'][col] = {
                    'slope': slope,
                    'direction': 'improving' if slope > 0 else 'declining' if slope < 0 else 'stable'
                }
        
        # 性能稳定性
        statistics['stability'] = {}
        for col in ['episode_reward', 'coordination_efficiency']:
            if col in df.columns:
                cv = df[col].std() / df[col].mean()  # 变异系数
                statistics['stability'][col] = {
                    'coefficient_of_variation': cv,
                    'stability_level': 'high' if cv < 0.1 else 'medium' if cv < 0.3 else 'low'
                }
        
        return statistics
    
    def _generate_html_report(self, df: pd.DataFrame, charts_info: List[Dict],
                            statistics: Dict, output_dir: Path, report_name: str) -> Path:
        """生成HTML报告"""
        
        # HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report_title }}</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 40px; }
        .section { margin-bottom: 40px; }
        .chart { text-align: center; margin: 20px 0; }
        .chart img { max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px; }
        .stats-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .stats-table th, .stats-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .stats-table th { background-color: #f2f2f2; }
        .summary-box { background-color: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .metric-highlight { color: #2E8B57; font-weight: bold; }
        .trend-up { color: #228B22; }
        .trend-down { color: #DC143C; }
        .trend-stable { color: #4682B4; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ report_title }}</h1>
        <p>生成时间: {{ generation_time }}</p>
        <p>分析回合数: {{ total_episodes }}</p>
    </div>
    
    <div class="section">
        <h2>执行摘要</h2>
        <div class="summary-box">
            <h3>关键性能指标</h3>
            <ul>
                <li>平均回合奖励: <span class="metric-highlight">{{ avg_reward:.2f }}</span></li>
                <li>平均成功率: <span class="metric-highlight">{{ avg_success_rate:.2f }}</span></li>
                <li>平均协作效率: <span class="metric-highlight">{{ avg_coordination:.2f }}</span></li>
                <li>平均推理时间: <span class="metric-highlight">{{ avg_inference_time:.2f }} ms</span></li>
            </ul>
        </div>
    </div>
    
    {% if charts_info %}
    <div class="section">
        <h2>性能图表分析</h2>
        {% for chart in charts_info %}
        <div class="chart">
            <h3>{{ chart.title }}</h3>
            <img src="{{ chart.path }}" alt="{{ chart.title }}">
            <p>{{ chart.description }}</p>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if statistics %}
    <div class="section">
        <h2>统计分析</h2>
        
        {% if statistics.trends %}
        <h3>趋势分析</h3>
        <ul>
        {% for metric, trend in statistics.trends.items() %}
            <li>{{ metric }}: 
                <span class="trend-{{ trend.direction }}">
                    {{ trend.direction }} (斜率: {{ "%.4f"|format(trend.slope) }})
                </span>
            </li>
        {% endfor %}
        </ul>
        {% endif %}
        
        {% if statistics.stability %}
        <h3>性能稳定性</h3>
        <ul>
        {% for metric, stability in statistics.stability.items() %}
            <li>{{ metric }}: {{ stability.stability_level }} 稳定性 (CV: {{ "%.3f"|format(stability.coefficient_of_variation) }})</li>
        {% endfor %}
        </ul>
        {% endif %}
    </div>
    {% endif %}
    
    <div class="section">
        <h2>详细数据</h2>
        <p>完整的性能数据已保存为 CSV 文件: <a href="performance_data.csv">performance_data.csv</a></p>
    </div>
    
    <div class="section">
        <h2>结论与建议</h2>
        <div class="summary-box">
            <h3>主要发现</h3>
            <ul>
                <li>系统在 {{ total_episodes }} 个回合中表现{{ overall_performance }}</li>
                <li>协作效率{{ coordination_trend }}，表明AGV间协调能力{{ coordination_assessment }}</li>
                <li>注意力机制{{ attention_assessment }}，有助于提升决策质量</li>
            </ul>
            
            <h3>改进建议</h3>
            <ul>
                <li>继续优化注意力机制参数以提升稳定性</li>
                <li>考虑增加课程学习的复杂度以提高泛化能力</li>
                <li>监控推理时间，确保实时性要求</li>
            </ul>
        </div>
    </div>
</body>
</html>
        """
        
        # 准备模板数据
        template_data = {
            'report_title': f'{report_name} - 性能分析报告',
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_episodes': len(df),
            'avg_reward': df['episode_reward'].mean(),
            'avg_success_rate': df['success_rate'].mean(),
            'avg_coordination': df['coordination_efficiency'].mean(),
            'avg_inference_time': df['inference_time'].mean(),
            'charts_info': charts_info,
            'statistics': statistics,
            'overall_performance': '良好' if df['success_rate'].mean() > 0.8 else '一般',
            'coordination_trend': '持续改善' if statistics.get('trends', {}).get('coordination_efficiency', {}).get('direction') == 'improving' else '保持稳定',
            'coordination_assessment': '不断提升' if statistics.get('trends', {}).get('coordination_efficiency', {}).get('direction') == 'improving' else '表现稳定',
            'attention_assessment': '运行稳定' if df['attention_stability'].mean() > 0.7 else '需要优化'
        }
        
        # 渲染HTML
        template = Template(html_template)
        html_content = template.render(**template_data)
        
        # 保存HTML文件
        html_path = output_dir / f'{report_name}.html'
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 保存CSV数据
        csv_path = output_dir / 'performance_data.csv'
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        return html_path
    
    def _generate_comparison_html_report(self, comparison: ModelComparison,
                                       charts_info: List[Dict], output_dir: Path,
                                       report_name: str) -> Path:
        """生成模型对比HTML报告"""
        
        # 简化的对比报告模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report_title }}</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 40px; }
        .section { margin-bottom: 40px; }
        .chart { text-align: center; margin: 20px 0; }
        .chart img { max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px; }
        .recommendation { background-color: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ report_title }}</h1>
        <p>生成时间: {{ generation_time }}</p>
        <p>对比模型: {{ model_names|join(', ') }}</p>
    </div>
    
    {% for chart in charts_info %}
    <div class="chart">
        <h3>{{ chart.title }}</h3>
        <img src="{{ chart.path }}" alt="{{ chart.title }}">
        <p>{{ chart.description }}</p>
    </div>
    {% endfor %}
    
    <div class="section">
        <h2>建议与结论</h2>
        {% for recommendation in recommendations %}
        <div class="recommendation">{{ recommendation }}</div>
        {% endfor %}
    </div>
</body>
</html>
        """
        
        template_data = {
            'report_title': f'{report_name} - 模型对比报告',
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'model_names': comparison.model_names,
            'charts_info': charts_info,
            'recommendations': comparison.recommendations
        }
        
        template = Template(html_template)
        html_content = template.render(**template_data)
        
        html_path = output_dir / f'{report_name}.html'
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_path
