"""
快速适应管理器
实现多AGV系统的快速适应机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum
import time

from .maml_framework import MAMLFramework, MAMLConfig
from ..mappo.attention_enhanced_mappo import AttentionEnhancedMAPPOModel


class AdaptationStrategy(Enum):
    """适应策略类型"""
    GRADIENT_BASED = "gradient_based"  # 基于梯度的适应
    ATTENTION_BASED = "attention_based"  # 基于注意力的适应
    HYBRID = "hybrid"  # 混合策略
    FINE_TUNING = "fine_tuning"  # 微调策略


@dataclass
class AdaptationConfig:
    """快速适应配置"""
    
    # 适应策略
    strategy: AdaptationStrategy = AdaptationStrategy.HYBRID
    
    # 适应参数
    adaptation_steps: int = 5
    adaptation_lr: float = 1e-2
    min_adaptation_samples: int = 10
    max_adaptation_samples: int = 50
    
    # 注意力适应参数
    attention_adaptation_weight: float = 0.3
    attention_temperature: float = 1.0
    
    # 性能阈值
    performance_threshold: float = 0.8
    improvement_threshold: float = 0.05
    
    # 时间限制
    max_adaptation_time: float = 30.0  # 秒
    
    # 正则化
    l2_regularization: float = 1e-4
    dropout_rate: float = 0.1


class FastAdaptationManager:
    """
    快速适应管理器
    管理多AGV系统的快速适应过程
    """
    
    def __init__(self, 
                 maml_framework: MAMLFramework,
                 config: AdaptationConfig):
        """
        初始化快速适应管理器
        
        Args:
            maml_framework: MAML框架
            config: 适应配置
        """
        self.maml_framework = maml_framework
        self.config = config
        
        # 适应历史
        self.adaptation_history = []
        
        # 性能监控
        self.performance_tracker = {
            'adaptation_times': [],
            'performance_improvements': [],
            'convergence_steps': [],
            'success_rates': []
        }
        
        # 注意力权重缓存
        self.attention_cache = {}
        
    def adapt_to_new_environment(self, 
                                new_env_data: Dict[str, Any],
                                validation_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        适应新环境
        
        Args:
            new_env_data: 新环境数据
            validation_data: 验证数据
            
        Returns:
            adaptation_result: 适应结果
        """
        start_time = time.time()
        
        # 检测环境变化
        change_analysis = self._analyze_environment_change(new_env_data)
        
        # 选择适应策略
        strategy = self._select_adaptation_strategy(change_analysis)
        
        # 执行适应
        if strategy == AdaptationStrategy.GRADIENT_BASED:
            adapted_model, metrics = self._gradient_based_adaptation(new_env_data)
        elif strategy == AdaptationStrategy.ATTENTION_BASED:
            adapted_model, metrics = self._attention_based_adaptation(new_env_data)
        elif strategy == AdaptationStrategy.HYBRID:
            adapted_model, metrics = self._hybrid_adaptation(new_env_data)
        else:  # FINE_TUNING
            adapted_model, metrics = self._fine_tuning_adaptation(new_env_data)
        
        # 验证适应效果
        if validation_data is not None:
            validation_metrics = self._validate_adaptation(adapted_model, validation_data)
            metrics.update(validation_metrics)
        
        adaptation_time = time.time() - start_time
        
        # 记录适应历史
        adaptation_record = {
            'timestamp': time.time(),
            'strategy': strategy,
            'change_analysis': change_analysis,
            'adaptation_time': adaptation_time,
            'metrics': metrics,
            'success': metrics.get('performance_improvement', 0) > self.config.improvement_threshold
        }
        
        self.adaptation_history.append(adaptation_record)
        self._update_performance_tracker(adaptation_record)
        
        return {
            'adapted_model': adapted_model,
            'strategy_used': strategy,
            'adaptation_time': adaptation_time,
            'performance_metrics': metrics,
            'change_analysis': change_analysis,
            'success': adaptation_record['success']
        }
    
    def _analyze_environment_change(self, new_env_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析环境变化
        
        Args:
            new_env_data: 新环境数据
            
        Returns:
            change_analysis: 变化分析结果
        """
        analysis = {
            'change_magnitude': 0.0,
            'change_types': [],
            'complexity_change': 0.0,
            'attention_pattern_change': 0.0
        }
        
        # 分析数据分布变化
        if 'observations' in new_env_data:
            obs = new_env_data['observations']
            
            # 计算观察分布的变化
            obs_mean = torch.mean(obs, dim=0)
            obs_std = torch.std(obs, dim=0)
            
            # 与历史数据比较（简化版本）
            if hasattr(self, 'reference_obs_stats'):
                mean_diff = torch.norm(obs_mean - self.reference_obs_stats['mean'])
                std_diff = torch.norm(obs_std - self.reference_obs_stats['std'])
                analysis['change_magnitude'] = (mean_diff + std_diff).item()
            else:
                # 首次适应，记录参考统计
                self.reference_obs_stats = {'mean': obs_mean, 'std': obs_std}
                analysis['change_magnitude'] = 0.5  # 中等变化
        
        # 分析动作分布变化
        if 'actions' in new_env_data:
            actions = new_env_data['actions']
            action_dist = torch.bincount(actions, minlength=7).float()
            action_dist = action_dist / action_dist.sum()
            
            if hasattr(self, 'reference_action_dist'):
                action_change = torch.norm(action_dist - self.reference_action_dist)
                analysis['change_magnitude'] += action_change.item()
            else:
                self.reference_action_dist = action_dist
        
        # 确定变化类型
        if analysis['change_magnitude'] > 0.8:
            analysis['change_types'].append('major_distribution_shift')
        elif analysis['change_magnitude'] > 0.4:
            analysis['change_types'].append('moderate_distribution_shift')
        else:
            analysis['change_types'].append('minor_distribution_shift')
        
        # 复杂度变化分析
        if 'rewards' in new_env_data:
            reward_variance = torch.var(new_env_data['rewards'])
            analysis['complexity_change'] = reward_variance.item()
        
        return analysis
    
    def _select_adaptation_strategy(self, change_analysis: Dict[str, Any]) -> AdaptationStrategy:
        """
        选择适应策略
        
        Args:
            change_analysis: 变化分析
            
        Returns:
            strategy: 选择的策略
        """
        change_magnitude = change_analysis['change_magnitude']
        
        if change_magnitude > 0.8:
            # 大变化：使用混合策略
            return AdaptationStrategy.HYBRID
        elif change_magnitude > 0.4:
            # 中等变化：使用基于梯度的适应
            return AdaptationStrategy.GRADIENT_BASED
        else:
            # 小变化：使用基于注意力的适应
            return AdaptationStrategy.ATTENTION_BASED
    
    def _gradient_based_adaptation(self, new_env_data: Dict[str, Any]) -> Tuple[AttentionEnhancedMAPPOModel, Dict[str, float]]:
        """
        基于梯度的适应
        
        Args:
            new_env_data: 新环境数据
            
        Returns:
            adapted_model: 适应后的模型
            metrics: 适应指标
        """
        # 使用MAML框架进行快速适应
        adapted_model = self.maml_framework.fast_adapt(
            new_env_data, 
            adaptation_steps=self.config.adaptation_steps
        )
        
        # 计算适应指标
        initial_loss = self._compute_loss(self.maml_framework.meta_model, new_env_data)
        adapted_loss = self._compute_loss(adapted_model, new_env_data)
        
        metrics = {
            'initial_loss': initial_loss.item(),
            'adapted_loss': adapted_loss.item(),
            'performance_improvement': (initial_loss - adapted_loss).item(),
            'adaptation_steps': self.config.adaptation_steps,
            'strategy': 'gradient_based'
        }
        
        return adapted_model, metrics
    
    def _attention_based_adaptation(self, new_env_data: Dict[str, Any]) -> Tuple[AttentionEnhancedMAPPOModel, Dict[str, float]]:
        """
        基于注意力的适应
        
        Args:
            new_env_data: 新环境数据
            
        Returns:
            adapted_model: 适应后的模型
            metrics: 适应指标
        """
        # 创建适应模型
        adapted_model = self.maml_framework.create_task_model()
        
        # 分析注意力模式
        attention_patterns = self._analyze_attention_patterns(new_env_data)
        
        # 调整注意力权重
        self._adjust_attention_weights(adapted_model, attention_patterns)
        
        # 计算适应指标
        initial_loss = self._compute_loss(self.maml_framework.meta_model, new_env_data)
        adapted_loss = self._compute_loss(adapted_model, new_env_data)
        
        metrics = {
            'initial_loss': initial_loss.item(),
            'adapted_loss': adapted_loss.item(),
            'performance_improvement': (initial_loss - adapted_loss).item(),
            'attention_adjustment_magnitude': attention_patterns.get('adjustment_magnitude', 0.0),
            'strategy': 'attention_based'
        }
        
        return adapted_model, metrics
    
    def _hybrid_adaptation(self, new_env_data: Dict[str, Any]) -> Tuple[AttentionEnhancedMAPPOModel, Dict[str, float]]:
        """
        混合适应策略
        
        Args:
            new_env_data: 新环境数据
            
        Returns:
            adapted_model: 适应后的模型
            metrics: 适应指标
        """
        # 先进行注意力适应
        attention_model, attention_metrics = self._attention_based_adaptation(new_env_data)
        
        # 再进行梯度适应
        # 将注意力适应的结果作为起点
        self.maml_framework.meta_model.load_state_dict(attention_model.state_dict())
        gradient_model, gradient_metrics = self._gradient_based_adaptation(new_env_data)
        
        # 合并指标
        metrics = {
            'initial_loss': attention_metrics['initial_loss'],
            'attention_adapted_loss': attention_metrics['adapted_loss'],
            'final_adapted_loss': gradient_metrics['adapted_loss'],
            'total_performance_improvement': attention_metrics['initial_loss'] - gradient_metrics['adapted_loss'],
            'attention_improvement': attention_metrics['performance_improvement'],
            'gradient_improvement': gradient_metrics['performance_improvement'],
            'strategy': 'hybrid'
        }
        
        return gradient_model, metrics
    
    def _fine_tuning_adaptation(self, new_env_data: Dict[str, Any]) -> Tuple[AttentionEnhancedMAPPOModel, Dict[str, float]]:
        """
        微调适应策略
        
        Args:
            new_env_data: 新环境数据
            
        Returns:
            adapted_model: 适应后的模型
            metrics: 适应指标
        """
        # 创建模型副本
        adapted_model = self.maml_framework.create_task_model()
        
        # 创建优化器（较小的学习率）
        optimizer = torch.optim.Adam(
            adapted_model.parameters(),
            lr=self.config.adaptation_lr * 0.1,  # 更小的学习率
            weight_decay=self.config.l2_regularization
        )
        
        initial_loss = self._compute_loss(adapted_model, new_env_data)
        
        # 微调步骤
        for step in range(self.config.adaptation_steps * 2):  # 更多步骤
            loss = self._compute_loss(adapted_model, new_env_data)
            
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(adapted_model.parameters(), 1.0)
            optimizer.step()
        
        final_loss = self._compute_loss(adapted_model, new_env_data)
        
        metrics = {
            'initial_loss': initial_loss.item(),
            'adapted_loss': final_loss.item(),
            'performance_improvement': (initial_loss - final_loss).item(),
            'adaptation_steps': self.config.adaptation_steps * 2,
            'strategy': 'fine_tuning'
        }
        
        return adapted_model, metrics
    
    def _compute_loss(self, model: AttentionEnhancedMAPPOModel, data: Dict[str, Any]) -> torch.Tensor:
        """计算模型损失"""
        obs = data['observations']
        actions = data['actions']
        advantages = data['advantages']
        values = data['values']
        
        # 前向传播
        logits, _ = model.forward({'obs': obs}, [], None)
        log_probs = F.log_softmax(logits, dim=-1)
        action_log_probs = log_probs.gather(1, actions.unsqueeze(-1)).squeeze(-1)
        
        # PPO损失
        old_log_probs = data.get('old_log_probs', action_log_probs.detach())
        ratio = torch.exp(action_log_probs - old_log_probs)
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 0.8, 1.2) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 价值损失
        predicted_values = model.value_function()
        value_loss = F.mse_loss(predicted_values, values)
        
        return policy_loss + 0.5 * value_loss
    
    def _analyze_attention_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析注意力模式"""
        # 简化的注意力模式分析
        obs = data['observations']
        batch_size = obs.shape[0]
        
        # 计算观察的注意力分布
        attention_scores = torch.softmax(obs[:, :64], dim=-1)  # 使用前64维作为注意力分数
        
        patterns = {
            'attention_entropy': -torch.sum(attention_scores * torch.log(attention_scores + 1e-8), dim=-1).mean().item(),
            'attention_concentration': torch.max(attention_scores, dim=-1)[0].mean().item(),
            'adjustment_magnitude': 0.1  # 简化的调整幅度
        }
        
        return patterns
    
    def _adjust_attention_weights(self, model: AttentionEnhancedMAPPOModel, patterns: Dict[str, Any]):
        """调整注意力权重"""
        # 简化的注意力权重调整
        adjustment_factor = patterns.get('adjustment_magnitude', 0.1)
        
        # 对注意力层进行微调
        for name, param in model.named_parameters():
            if 'attention' in name and param.requires_grad:
                # 添加小的随机扰动
                noise = torch.randn_like(param) * adjustment_factor * 0.01
                param.data += noise
    
    def _validate_adaptation(self, model: AttentionEnhancedMAPPOModel, validation_data: Dict[str, Any]) -> Dict[str, float]:
        """验证适应效果"""
        with torch.no_grad():
            validation_loss = self._compute_loss(model, validation_data)
            
            # 计算准确率（简化版本）
            obs = validation_data['observations']
            actions = validation_data['actions']
            
            logits, _ = model.forward({'obs': obs}, [], None)
            predicted_actions = torch.argmax(logits, dim=-1)
            accuracy = (predicted_actions == actions).float().mean()
            
        return {
            'validation_loss': validation_loss.item(),
            'validation_accuracy': accuracy.item()
        }
    
    def _update_performance_tracker(self, adaptation_record: Dict[str, Any]):
        """更新性能跟踪器"""
        self.performance_tracker['adaptation_times'].append(adaptation_record['adaptation_time'])
        self.performance_tracker['performance_improvements'].append(
            adaptation_record['metrics'].get('performance_improvement', 0)
        )
        self.performance_tracker['success_rates'].append(1.0 if adaptation_record['success'] else 0.0)
    
    def get_adaptation_statistics(self) -> Dict[str, Any]:
        """获取适应统计信息"""
        if not self.adaptation_history:
            return {}
        
        tracker = self.performance_tracker
        
        return {
            'total_adaptations': len(self.adaptation_history),
            'average_adaptation_time': np.mean(tracker['adaptation_times']) if tracker['adaptation_times'] else 0,
            'average_performance_improvement': np.mean(tracker['performance_improvements']) if tracker['performance_improvements'] else 0,
            'success_rate': np.mean(tracker['success_rates']) if tracker['success_rates'] else 0,
            'strategy_distribution': self._get_strategy_distribution(),
            'recent_performance_trend': self._get_recent_trend()
        }
    
    def _get_strategy_distribution(self) -> Dict[str, float]:
        """获取策略分布"""
        strategies = [record['strategy'] for record in self.adaptation_history]
        strategy_counts = {}
        for strategy in strategies:
            strategy_name = strategy.value if hasattr(strategy, 'value') else str(strategy)
            strategy_counts[strategy_name] = strategy_counts.get(strategy_name, 0) + 1
        
        total = len(strategies)
        return {k: v/total for k, v in strategy_counts.items()}
    
    def _get_recent_trend(self) -> str:
        """获取最近的性能趋势"""
        if len(self.performance_tracker['performance_improvements']) < 5:
            return 'insufficient_data'
        
        recent_improvements = self.performance_tracker['performance_improvements'][-5:]
        trend = np.polyfit(range(5), recent_improvements, 1)[0]
        
        if trend > 0.01:
            return 'improving'
        elif trend < -0.01:
            return 'declining'
        else:
            return 'stable'
