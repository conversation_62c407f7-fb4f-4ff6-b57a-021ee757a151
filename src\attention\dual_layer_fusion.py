"""
双层注意力融合与集成
实现第一层任务分配注意力和第二层协作感知注意力的融合机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import math

from ..utils.state_representation import StateSpaceManager
from ..environment.agv_entity import AGVEntity
from ..environment.task_manager import Task
from .task_allocation_attention import AttentionOutput, TaskAllocationAttentionManager
from .collaboration_attention import CollaborationOutput, CollaborationAttentionManager


@dataclass
class DualLayerOutput:
    """双层注意力融合输出"""
    fused_features: torch.Tensor           # 融合特征 [batch_size, num_agvs, feature_dim]
    gate_weights: torch.Tensor             # 门控权重 [batch_size, num_agvs, feature_dim]
    task_attention_weights: torch.Tensor   # 第一层注意力权重 [batch_size, num_agvs, num_tasks]
    collaboration_weights: torch.Tensor    # 第二层协作权重 [batch_size, num_agvs, num_agvs]
    enhanced_features: torch.Tensor        # 增强特征 [batch_size, num_agvs, feature_dim]
    attention_scores: torch.Tensor         # 综合注意力分数 [batch_size, num_agvs, feature_dim]
    temporal_consistency: torch.Tensor     # 时序一致性分数 [batch_size, num_agvs]
    metadata: Dict[str, Any]               # 元数据信息


class GatedFusion(nn.Module):
    """门控融合机制"""
    
    def __init__(self, feature_dim: int = 64, gate_dim: int = 32):
        """
        初始化门控融合机制
        
        Args:
            feature_dim: 特征维度
            gate_dim: 门控网络隐藏维度
        """
        super().__init__()
        self.feature_dim = feature_dim
        self.gate_dim = gate_dim
        
        # 门控网络
        self.gate_net = nn.Sequential(
            nn.Linear(feature_dim * 2, gate_dim),  # 输入: [layer1_output; layer2_output]
            nn.ReLU(),
            nn.Linear(gate_dim, gate_dim),
            nn.ReLU(),
            nn.Linear(gate_dim, feature_dim),
            nn.Sigmoid()  # 输出门控权重 [0,1]
        )
        
        # 特征对齐网络（确保两层输出维度一致）
        self.layer1_projection = nn.Linear(feature_dim, feature_dim)
        self.layer2_projection = nn.Linear(feature_dim, feature_dim)
        
        # 融合后的特征增强
        self.fusion_enhancement = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.ReLU(),
            nn.LayerNorm(feature_dim)
        )
    
    def forward(self, layer1_output: torch.Tensor, 
                layer2_output: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            layer1_output: 第一层输出 [batch_size, num_agvs, feature_dim]
            layer2_output: 第二层输出 [batch_size, num_agvs, feature_dim]
            
        Returns:
            fused_features: 融合特征 [batch_size, num_agvs, feature_dim]
            gate_weights: 门控权重 [batch_size, num_agvs, feature_dim]
        """
        # 特征对齐
        layer1_aligned = self.layer1_projection(layer1_output)
        layer2_aligned = self.layer2_projection(layer2_output)
        
        # 拼接特征用于门控计算
        concatenated = torch.cat([layer1_aligned, layer2_aligned], dim=-1)
        
        # 计算门控权重
        gate_weights = self.gate_net(concatenated)
        
        # 门控融合
        fused_features = gate_weights * layer1_aligned + (1 - gate_weights) * layer2_aligned
        
        # 特征增强
        fused_features = self.fusion_enhancement(fused_features)
        
        return fused_features, gate_weights


class ResidualConnection(nn.Module):
    """残差连接和层归一化"""
    
    def __init__(self, feature_dim: int = 64, dropout: float = 0.1):
        """
        初始化残差连接
        
        Args:
            feature_dim: 特征维度
            dropout: Dropout比率
        """
        super().__init__()
        self.feature_dim = feature_dim
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(feature_dim)
        
        # 输入特征投影（如果需要维度匹配）
        self.input_projection = nn.Linear(feature_dim, feature_dim)
    
    def forward(self, fused_features: torch.Tensor, 
                input_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            fused_features: 融合特征 [batch_size, num_agvs, feature_dim]
            input_features: 输入特征 [batch_size, num_agvs, feature_dim]
            
        Returns:
            output: 残差连接后的输出 [batch_size, num_agvs, feature_dim]
        """
        # 投影输入特征
        projected_input = self.input_projection(input_features)
        
        # 残差连接
        residual_output = fused_features + projected_input
        
        # Dropout和层归一化
        output = self.layer_norm(self.dropout(residual_output))
        
        return output


class TemporalConsistency(nn.Module):
    """时序一致性约束"""
    
    def __init__(self, feature_dim: int = 64, memory_size: int = 5):
        """
        初始化时序一致性模块
        
        Args:
            feature_dim: 特征维度
            memory_size: 历史记忆大小
        """
        super().__init__()
        self.feature_dim = feature_dim
        self.memory_size = memory_size
        
        # 时序特征提取
        self.temporal_encoder = nn.LSTM(
            input_size=feature_dim,
            hidden_size=feature_dim // 2,
            num_layers=2,
            batch_first=True,
            dropout=0.1
        )
        
        # 一致性评分网络
        self.consistency_net = nn.Sequential(
            nn.Linear(feature_dim // 2, feature_dim // 4),
            nn.ReLU(),
            nn.Linear(feature_dim // 4, 1),
            nn.Sigmoid()
        )
        
        # 历史特征缓存
        self.register_buffer('feature_history', torch.zeros(1, 1, memory_size, feature_dim))
        self.register_buffer('history_pointer', torch.zeros(1, dtype=torch.long))
    
    def forward(self, current_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            current_features: 当前特征 [batch_size, num_agvs, feature_dim]
            
        Returns:
            consistency_scores: 一致性分数 [batch_size, num_agvs]
            temporal_features: 时序增强特征 [batch_size, num_agvs, feature_dim]
        """
        batch_size, num_agvs, _ = current_features.shape
        
        # 更新特征历史
        self._update_history(current_features)
        
        # 获取历史特征序列
        history_features = self.feature_history[:batch_size, :num_agvs, :, :]
        
        # 时序编码
        temporal_output, _ = self.temporal_encoder(
            history_features.view(batch_size * num_agvs, self.memory_size, self.feature_dim)
        )
        
        # 取最后一个时间步的输出
        temporal_features = temporal_output[:, -1, :].view(batch_size, num_agvs, -1)
        
        # 计算一致性分数
        consistency_scores = self.consistency_net(temporal_features).squeeze(-1)
        
        # 扩展时序特征到原始维度
        temporal_features_expanded = torch.cat([
            temporal_features, 
            torch.zeros(batch_size, num_agvs, self.feature_dim - temporal_features.size(-1), 
                       device=temporal_features.device)
        ], dim=-1)
        
        return consistency_scores, temporal_features_expanded
    
    def _update_history(self, current_features: torch.Tensor):
        """更新特征历史"""
        batch_size, num_agvs, _ = current_features.shape
        
        # 确保缓存大小匹配
        if self.feature_history.size(0) < batch_size or self.feature_history.size(1) < num_agvs:
            new_history = torch.zeros(
                max(batch_size, self.feature_history.size(0)),
                max(num_agvs, self.feature_history.size(1)),
                self.memory_size,
                self.feature_dim,
                device=self.feature_history.device
            )
            new_history[:self.feature_history.size(0), :self.feature_history.size(1)] = self.feature_history
            self.feature_history = new_history
        
        # 更新历史
        pointer = self.history_pointer.item()
        self.feature_history[:batch_size, :num_agvs, pointer] = current_features
        self.history_pointer[0] = (pointer + 1) % self.memory_size


class AttentionEnhancedFeatureExtractor(nn.Module):
    """注意力增强的特征提取器"""
    
    def __init__(self, feature_dim: int = 64, hidden_dim: int = 128):
        """
        初始化注意力增强特征提取器
        
        Args:
            feature_dim: 特征维度
            hidden_dim: 隐藏层维度
        """
        super().__init__()
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        
        # 多层特征提取网络
        self.feature_extractor = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1),
            
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Dropout(0.1),
            
            nn.Linear(hidden_dim, feature_dim),
            nn.LayerNorm(feature_dim)
        )
        
        # 注意力分数计算
        self.attention_scorer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, feature_dim),
            nn.Softmax(dim=-1)
        )
        
        # 最终输出投影
        self.output_projection = nn.Linear(feature_dim, feature_dim)
    
    def forward(self, fused_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            fused_features: 融合特征 [batch_size, num_agvs, feature_dim]
            
        Returns:
            enhanced_features: 增强特征 [batch_size, num_agvs, feature_dim]
            attention_scores: 注意力分数 [batch_size, num_agvs, feature_dim]
        """
        # 特征提取
        extracted_features = self.feature_extractor(fused_features)
        
        # 计算注意力分数
        attention_scores = self.attention_scorer(extracted_features)
        
        # 注意力加权
        attended_features = extracted_features * attention_scores
        
        # 输出投影
        enhanced_features = self.output_projection(attended_features)
        
        return enhanced_features, attention_scores


class DualLayerAttentionFusion:
    """双层注意力融合管理器"""

    def __init__(self, feature_dim: int = 64, gate_dim: int = 32,
                 hidden_dim: int = 128, dropout: float = 0.1,
                 memory_size: int = 5):
        """
        初始化双层注意力融合管理器

        Args:
            feature_dim: 特征维度
            gate_dim: 门控网络隐藏维度
            hidden_dim: 特征提取器隐藏维度
            dropout: Dropout比率
            memory_size: 时序记忆大小
        """
        self.feature_dim = feature_dim
        self.gate_dim = gate_dim
        self.hidden_dim = hidden_dim
        self.dropout = dropout
        self.memory_size = memory_size

        # 创建融合组件
        self.gated_fusion = GatedFusion(feature_dim, gate_dim)
        self.residual_connection = ResidualConnection(feature_dim, dropout)
        self.temporal_consistency = TemporalConsistency(feature_dim, memory_size)
        self.feature_extractor = AttentionEnhancedFeatureExtractor(feature_dim, hidden_dim)

        # 创建第一层和第二层注意力管理器
        self.task_attention_manager = TaskAllocationAttentionManager(
            feature_dim=feature_dim,
            num_heads=8,
            top_k=5
        )

        self.collaboration_manager = CollaborationAttentionManager(
            feature_dim=feature_dim,
            pos_dim=32,
            num_heads=8,
            near_threshold=3.0,
            far_threshold=10.0
        )

        # 统计信息
        self.stats = {
            'total_fusions': 0,
            'avg_gate_entropy': 0.0,
            'avg_temporal_consistency': 0.0,
            'layer1_contribution': 0.0,
            'layer2_contribution': 0.0,
            'fusion_quality': 0.0
        }

    def compute_dual_layer_attention(self, agvs: List[AGVEntity], tasks: List[Task],
                                   agv_embeddings: torch.Tensor, task_embeddings: torch.Tensor,
                                   state_manager: StateSpaceManager) -> DualLayerOutput:
        """
        计算双层注意力融合

        Args:
            agvs: AGV列表
            tasks: 任务列表
            agv_embeddings: AGV嵌入特征
            task_embeddings: 任务嵌入特征
            state_manager: 状态管理器

        Returns:
            dual_layer_output: 双层注意力融合输出
        """
        # 第一层：任务分配注意力
        task_attention_output = self.task_attention_manager.compute_attention(
            agvs, tasks, agv_embeddings, task_embeddings, state_manager
        )

        # 第二层：协作感知注意力
        collaboration_output = self.collaboration_manager.compute_collaboration(
            agvs, agv_embeddings, task_attention_output.attended_features, state_manager
        )

        # 门控融合
        fused_features, gate_weights = self.gated_fusion(
            task_attention_output.attended_features,
            collaboration_output.collaborated_features
        )

        # 残差连接
        residual_features = self.residual_connection(fused_features, agv_embeddings)

        # 时序一致性
        consistency_scores, temporal_features = self.temporal_consistency(residual_features)

        # 特征增强
        enhanced_features, attention_scores = self.feature_extractor(
            residual_features + temporal_features
        )

        # 更新统计信息
        self._update_stats(gate_weights, consistency_scores, task_attention_output, collaboration_output)

        return DualLayerOutput(
            fused_features=fused_features,
            gate_weights=gate_weights,
            task_attention_weights=task_attention_output.attention_weights,
            collaboration_weights=collaboration_output.collaboration_weights,
            enhanced_features=enhanced_features,
            attention_scores=attention_scores,
            temporal_consistency=consistency_scores,
            metadata={
                'layer1_metadata': task_attention_output.metadata,
                'layer2_metadata': collaboration_output.metadata,
                'fusion_stats': self.stats.copy()
            }
        )

    def _update_stats(self, gate_weights: torch.Tensor, consistency_scores: torch.Tensor,
                     task_output: AttentionOutput, collab_output: CollaborationOutput):
        """更新统计信息"""
        self.stats['total_fusions'] += 1

        # 计算门控熵（衡量融合的均衡性）
        gate_entropy = -torch.sum(gate_weights * torch.log(gate_weights + 1e-8), dim=-1).mean()
        self.stats['avg_gate_entropy'] = (
            self.stats['avg_gate_entropy'] * (self.stats['total_fusions'] - 1) + gate_entropy.item()
        ) / self.stats['total_fusions']

        # 计算平均时序一致性
        avg_consistency = consistency_scores.mean().item()
        self.stats['avg_temporal_consistency'] = (
            self.stats['avg_temporal_consistency'] * (self.stats['total_fusions'] - 1) + avg_consistency
        ) / self.stats['total_fusions']

        # 计算层贡献度
        layer1_contrib = gate_weights.mean().item()
        layer2_contrib = 1 - layer1_contrib

        self.stats['layer1_contribution'] = (
            self.stats['layer1_contribution'] * (self.stats['total_fusions'] - 1) + layer1_contrib
        ) / self.stats['total_fusions']

        self.stats['layer2_contribution'] = (
            self.stats['layer2_contribution'] * (self.stats['total_fusions'] - 1) + layer2_contrib
        ) / self.stats['total_fusions']

        # 计算融合质量（基于注意力权重的方差）
        task_attention_var = task_output.attention_weights.var().item()
        collab_attention_var = collab_output.collaboration_weights.var().item()
        fusion_quality = (task_attention_var + collab_attention_var) / 2

        self.stats['fusion_quality'] = (
            self.stats['fusion_quality'] * (self.stats['total_fusions'] - 1) + fusion_quality
        ) / self.stats['total_fusions']

    def analyze_fusion_patterns(self, dual_output: DualLayerOutput,
                               agvs: List[AGVEntity]) -> Dict[str, Any]:
        """
        分析融合模式

        Args:
            dual_output: 双层注意力输出
            agvs: AGV列表

        Returns:
            analysis: 融合模式分析
        """
        gate_weights = dual_output.gate_weights.squeeze(0)  # [num_agvs, feature_dim]
        consistency_scores = dual_output.temporal_consistency.squeeze(0)  # [num_agvs]

        analysis = {
            'gate_analysis': {
                'mean_gate_weight': gate_weights.mean().item(),
                'gate_weight_std': gate_weights.std().item(),
                'layer1_dominance': (gate_weights > 0.5).float().mean().item(),
                'layer2_dominance': (gate_weights < 0.5).float().mean().item(),
                'balanced_fusion': ((gate_weights > 0.4) & (gate_weights < 0.6)).float().mean().item()
            },
            'temporal_analysis': {
                'mean_consistency': consistency_scores.mean().item(),
                'consistency_std': consistency_scores.std().item(),
                'high_consistency_ratio': (consistency_scores > 0.8).float().mean().item(),
                'low_consistency_ratio': (consistency_scores < 0.3).float().mean().item()
            },
            'agv_specific_patterns': [],
            'fusion_effectiveness': {
                'attention_diversity': dual_output.attention_scores.std().item(),
                'feature_richness': dual_output.enhanced_features.norm(dim=-1).mean().item(),
                'fusion_stability': consistency_scores.mean().item()
            }
        }

        # AGV特定模式分析
        for i, agv in enumerate(agvs):
            agv_gate = gate_weights[i].mean().item()
            agv_consistency = consistency_scores[i].item()

            pattern = {
                'agv_id': agv.agv_id,
                'position': agv.position,
                'status': agv.status.name,
                'gate_preference': 'task_focused' if agv_gate > 0.6 else 'collaboration_focused' if agv_gate < 0.4 else 'balanced',
                'temporal_consistency': agv_consistency,
                'attention_strength': dual_output.attention_scores[0, i].norm().item()
            }

            analysis['agv_specific_patterns'].append(pattern)

        return analysis

    def get_fusion_stats(self) -> Dict[str, Any]:
        """获取融合统计信息"""
        return self.stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_fusions': 0,
            'avg_gate_entropy': 0.0,
            'avg_temporal_consistency': 0.0,
            'layer1_contribution': 0.0,
            'layer2_contribution': 0.0,
            'fusion_quality': 0.0
        }

    def save_fusion_state(self, filepath: str):
        """保存融合状态"""
        state = {
            'gated_fusion_state': self.gated_fusion.state_dict(),
            'residual_connection_state': self.residual_connection.state_dict(),
            'temporal_consistency_state': self.temporal_consistency.state_dict(),
            'feature_extractor_state': self.feature_extractor.state_dict(),
            'stats': self.stats
        }
        torch.save(state, filepath)

    def load_fusion_state(self, filepath: str):
        """加载融合状态"""
        state = torch.load(filepath)
        self.gated_fusion.load_state_dict(state['gated_fusion_state'])
        self.residual_connection.load_state_dict(state['residual_connection_state'])
        self.temporal_consistency.load_state_dict(state['temporal_consistency_state'])
        self.feature_extractor.load_state_dict(state['feature_extractor_state'])
        self.stats = state['stats']
