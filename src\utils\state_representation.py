"""
状态空间与特征提取系统
实现AGV状态表示、任务状态表示、全局状态构建和特征嵌入层
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass

from ..environment.agv_entity import AGVEntity, AGVStatus
from ..environment.task_manager import Task, TaskStatus
from config.env_config import EnvironmentConfig


@dataclass
class StateRepresentation:
    """状态表示基础类"""
    raw_features: List[float]
    embedded_features: Optional[torch.Tensor] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_tensor(self) -> torch.Tensor:
        """转换为张量"""
        if self.embedded_features is not None:
            return self.embedded_features
        return torch.tensor(self.raw_features, dtype=torch.float32)
    
    def to_numpy(self) -> np.ndarray:
        """转换为numpy数组"""
        if self.embedded_features is not None:
            return self.embedded_features.detach().cpu().numpy()
        return np.array(self.raw_features, dtype=np.float32)


class AGVStateExtractor:
    """AGV状态特征提取器"""
    
    def __init__(self, config: EnvironmentConfig):
        """
        初始化AGV状态提取器
        
        Args:
            config: 环境配置
        """
        self.config = config
        self.map_width = config.map_width
        self.map_height = config.map_height
        self.max_capacity = config.agv_capacity
        self.max_queue_length = 4  # 假设最大队列长度为4
        self.max_task_id = config.num_tasks - 1
        
    def extract_features(self, agv: AGVEntity) -> StateRepresentation:
        """
        提取AGV状态特征
        
        Args:
            agv: AGV实体
            
        Returns:
            state: AGV状态表示
        """
        # 位置归一化
        x_norm = agv.position[0] / self.map_width
        y_norm = agv.position[1] / self.map_height
        
        # 载重归一化
        load_norm = agv.current_load / self.max_capacity
        
        # 任务队列长度归一化
        queue_len_norm = len(agv.task_queue) / self.max_queue_length
        
        # 当前目标任务ID归一化
        if agv.current_target is not None:
            target_norm = agv.current_target / self.max_task_id
        else:
            target_norm = -1.0  # 无目标时设为-1
            
        # 空闲状态（二进制）
        idle_status = 1.0 if agv.is_idle() else 0.0
        
        # 构建特征向量 [x_norm, y_norm, load_norm, queue_len_norm, target_norm, idle_status]
        raw_features = [x_norm, y_norm, load_norm, queue_len_norm, target_norm, idle_status]
        
        # 元数据
        metadata = {
            'agv_id': agv.agv_id,
            'position': agv.position,
            'status': agv.status.name,
            'current_load': agv.current_load,
            'queue_length': len(agv.task_queue),
            'current_target': agv.current_target
        }
        
        return StateRepresentation(
            raw_features=raw_features,
            metadata=metadata
        )
    
    def get_feature_dim(self) -> int:
        """获取特征维度"""
        return 6


class TaskStateExtractor:
    """任务状态特征提取器"""
    
    def __init__(self, config: EnvironmentConfig):
        """
        初始化任务状态提取器
        
        Args:
            config: 环境配置
        """
        self.config = config
        self.map_width = config.map_width
        self.map_height = config.map_height
        self.max_weight = 10  # 最大任务重量
        self.min_weight = 5   # 最小任务重量
        
    def extract_features(self, task: Task, all_agvs: List[AGVEntity]) -> StateRepresentation:
        """
        提取任务状态特征
        
        Args:
            task: 任务对象
            all_agvs: 所有AGV列表（用于计算最短距离）
            
        Returns:
            state: 任务状态表示
        """
        # 位置归一化
        x_norm = task.position[0] / self.map_width
        y_norm = task.position[1] / self.map_height
        
        # 重量归一化（5->0, 10->1）
        weight_norm = (task.weight - self.min_weight) / (self.max_weight - self.min_weight)
        
        # 任务状态归一化
        status_norm = task.status.value / len(TaskStatus)
        
        # 计算到所有AGV的最短曼哈顿距离
        min_distance = float('inf')
        if all_agvs:
            for agv in all_agvs:
                distance = abs(task.position[0] - agv.position[0]) + abs(task.position[1] - agv.position[1])
                min_distance = min(min_distance, distance)
            # 归一化距离（假设最大距离为地图对角线长度）
            max_distance = self.map_width + self.map_height
            min_distance_norm = min_distance / max_distance
        else:
            min_distance_norm = 1.0
        
        # 构建特征向量 [x_norm, y_norm, weight_norm, status_norm, min_distance_norm]
        raw_features = [x_norm, y_norm, weight_norm, status_norm, min_distance_norm]
        
        # 元数据
        metadata = {
            'task_id': task.task_id,
            'position': task.position,
            'weight': task.weight,
            'status': task.status.name,
            'assigned_agv': task.assigned_agv,
            'min_distance': min_distance if min_distance != float('inf') else None
        }
        
        return StateRepresentation(
            raw_features=raw_features,
            metadata=metadata
        )
    
    def get_feature_dim(self) -> int:
        """获取特征维度"""
        return 5


class FeatureEmbedding(nn.Module):
    """特征嵌入层"""
    
    def __init__(self, input_dim: int, embed_dim: int = 64):
        """
        初始化特征嵌入层
        
        Args:
            input_dim: 输入特征维度
            embed_dim: 嵌入维度
        """
        super().__init__()
        self.input_dim = input_dim
        self.embed_dim = embed_dim
        
        # 简单的全连接层进行特征嵌入
        self.embedding = nn.Sequential(
            nn.Linear(input_dim, embed_dim),
            nn.ReLU(),
            nn.LayerNorm(embed_dim)
        )
    
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, input_dim] 或 [input_dim]
            
        Returns:
            embedded: 嵌入特征 [batch_size, embed_dim] 或 [embed_dim]
        """
        return self.embedding(features)


class GlobalStateBuilder:
    """全局状态构建器"""
    
    def __init__(self, config: EnvironmentConfig, embed_dim: int = 64):
        """
        初始化全局状态构建器
        
        Args:
            config: 环境配置
            embed_dim: 嵌入维度
        """
        self.config = config
        self.embed_dim = embed_dim
        
        # 初始化特征提取器
        self.agv_extractor = AGVStateExtractor(config)
        self.task_extractor = TaskStateExtractor(config)
        
        # 初始化嵌入层
        self.agv_embedding = FeatureEmbedding(
            self.agv_extractor.get_feature_dim(), 
            embed_dim
        )
        self.task_embedding = FeatureEmbedding(
            self.task_extractor.get_feature_dim(), 
            embed_dim
        )
    
    def build_global_state(self, agvs: List[AGVEntity], tasks: List[Task]) -> torch.Tensor:
        """
        构建全局状态表示
        
        Args:
            agvs: AGV列表
            tasks: 任务列表
            
        Returns:
            global_state: 全局状态张量 [num_entities, embed_dim]
        """
        entity_embeddings = []
        
        # 提取并嵌入AGV状态
        for agv in agvs:
            agv_state = self.agv_extractor.extract_features(agv)
            agv_tensor = torch.tensor(agv_state.raw_features, dtype=torch.float32)
            agv_embedded = self.agv_embedding(agv_tensor)
            entity_embeddings.append(agv_embedded)
        
        # 提取并嵌入任务状态
        for task in tasks:
            task_state = self.task_extractor.extract_features(task, agvs)
            task_tensor = torch.tensor(task_state.raw_features, dtype=torch.float32)
            task_embedded = self.task_embedding(task_tensor)
            entity_embeddings.append(task_embedded)
        
        # 堆叠所有实体的嵌入
        if entity_embeddings:
            global_state = torch.stack(entity_embeddings, dim=0)  # [num_entities, embed_dim]
        else:
            # 如果没有实体，返回空张量
            global_state = torch.empty(0, self.embed_dim)
        
        return global_state
    
    def get_agv_embeddings(self, agvs: List[AGVEntity]) -> torch.Tensor:
        """
        获取AGV嵌入
        
        Args:
            agvs: AGV列表
            
        Returns:
            agv_embeddings: AGV嵌入张量 [num_agvs, embed_dim]
        """
        agv_embeddings = []
        for agv in agvs:
            agv_state = self.agv_extractor.extract_features(agv)
            agv_tensor = torch.tensor(agv_state.raw_features, dtype=torch.float32)
            agv_embedded = self.agv_embedding(agv_tensor)
            agv_embeddings.append(agv_embedded)
        
        if agv_embeddings:
            return torch.stack(agv_embeddings, dim=0)
        else:
            return torch.empty(0, self.embed_dim)
    
    def get_task_embeddings(self, tasks: List[Task], agvs: List[AGVEntity]) -> torch.Tensor:
        """
        获取任务嵌入
        
        Args:
            tasks: 任务列表
            agvs: AGV列表（用于计算距离）
            
        Returns:
            task_embeddings: 任务嵌入张量 [num_tasks, embed_dim]
        """
        task_embeddings = []
        for task in tasks:
            task_state = self.task_extractor.extract_features(task, agvs)
            task_tensor = torch.tensor(task_state.raw_features, dtype=torch.float32)
            task_embedded = self.task_embedding(task_tensor)
            task_embeddings.append(task_embedded)
        
        if task_embeddings:
            return torch.stack(task_embeddings, dim=0)
        else:
            return torch.empty(0, self.embed_dim)


class StateSpaceManager:
    """状态空间管理器"""

    def __init__(self, config: EnvironmentConfig, embed_dim: int = 64):
        """
        初始化状态空间管理器

        Args:
            config: 环境配置
            embed_dim: 嵌入维度
        """
        self.config = config
        self.embed_dim = embed_dim

        # 初始化组件
        self.agv_extractor = AGVStateExtractor(config)
        self.task_extractor = TaskStateExtractor(config)
        self.global_builder = GlobalStateBuilder(config, embed_dim)

        # 状态缓存
        self._last_global_state = None
        self._last_agv_states = {}
        self._last_task_states = {}

    def get_local_observation(self, agv: AGVEntity, visible_tasks: List[Task],
                            other_agvs: List[AGVEntity]) -> Dict[str, torch.Tensor]:
        """
        获取AGV的局部观察

        Args:
            agv: 目标AGV
            visible_tasks: 可见任务列表
            other_agvs: 其他AGV列表

        Returns:
            observation: 局部观察字典
        """
        # AGV自身状态
        agv_state = self.agv_extractor.extract_features(agv)
        agv_embedding = self.global_builder.agv_embedding(
            torch.tensor(agv_state.raw_features, dtype=torch.float32)
        )

        # 可见任务状态
        if visible_tasks:
            task_embeddings = []
            for task in visible_tasks:
                task_state = self.task_extractor.extract_features(task, [agv] + other_agvs)
                task_tensor = torch.tensor(task_state.raw_features, dtype=torch.float32)
                task_embedded = self.global_builder.task_embedding(task_tensor)
                task_embeddings.append(task_embedded)
            visible_task_embeddings = torch.stack(task_embeddings, dim=0)
        else:
            visible_task_embeddings = torch.empty(0, self.embed_dim)

        # 其他AGV状态
        if other_agvs:
            other_agv_embeddings = []
            for other_agv in other_agvs:
                other_state = self.agv_extractor.extract_features(other_agv)
                other_tensor = torch.tensor(other_state.raw_features, dtype=torch.float32)
                other_embedded = self.global_builder.agv_embedding(other_tensor)
                other_agv_embeddings.append(other_embedded)
            other_agv_embeddings = torch.stack(other_agv_embeddings, dim=0)
        else:
            other_agv_embeddings = torch.empty(0, self.embed_dim)

        return {
            'self_state': agv_embedding,
            'visible_tasks': visible_task_embeddings,
            'other_agvs': other_agv_embeddings,
            'raw_self_state': agv_state
        }

    def get_global_observation(self, agvs: List[AGVEntity], tasks: List[Task]) -> torch.Tensor:
        """
        获取全局观察

        Args:
            agvs: 所有AGV列表
            tasks: 所有任务列表

        Returns:
            global_obs: 全局观察张量
        """
        global_state = self.global_builder.build_global_state(agvs, tasks)
        self._last_global_state = global_state
        return global_state

    def get_visible_tasks(self, agv: AGVEntity, all_tasks: List[Task],
                         visibility_radius: int = 5) -> List[Task]:
        """
        获取AGV可见的任务

        Args:
            agv: AGV实体
            all_tasks: 所有任务列表
            visibility_radius: 可见半径

        Returns:
            visible_tasks: 可见任务列表
        """
        visible_tasks = []
        agv_pos = agv.position

        for task in all_tasks:
            # 计算曼哈顿距离
            distance = abs(task.position[0] - agv_pos[0]) + abs(task.position[1] - agv_pos[1])
            if distance <= visibility_radius and task.status == TaskStatus.PENDING:
                visible_tasks.append(task)

        # 按距离排序
        visible_tasks.sort(key=lambda t: abs(t.position[0] - agv_pos[0]) + abs(t.position[1] - agv_pos[1]))

        return visible_tasks

    def validate_state_consistency(self, agvs: List[AGVEntity], tasks: List[Task]) -> bool:
        """
        验证状态一致性

        Args:
            agvs: AGV列表
            tasks: 任务列表

        Returns:
            is_valid: 状态是否一致
        """
        try:
            # 检查AGV状态
            for agv in agvs:
                state = self.agv_extractor.extract_features(agv)
                if len(state.raw_features) != self.agv_extractor.get_feature_dim():
                    return False

                # 检查特征值范围
                features = state.raw_features
                if not (0 <= features[0] <= 1 and 0 <= features[1] <= 1):  # 位置归一化
                    return False
                if not (0 <= features[2] <= 1):  # 载重归一化
                    return False

            # 检查任务状态
            for task in tasks:
                state = self.task_extractor.extract_features(task, agvs)
                if len(state.raw_features) != self.task_extractor.get_feature_dim():
                    return False

                # 检查特征值范围
                features = state.raw_features
                if not (0 <= features[0] <= 1 and 0 <= features[1] <= 1):  # 位置归一化
                    return False
                if not (0 <= features[2] <= 1):  # 重量归一化
                    return False

            return True

        except Exception as e:
            print(f"状态验证失败: {e}")
            return False

    def get_state_statistics(self, agvs: List[AGVEntity], tasks: List[Task]) -> Dict[str, Any]:
        """
        获取状态统计信息

        Args:
            agvs: AGV列表
            tasks: 任务列表

        Returns:
            stats: 统计信息字典
        """
        agv_features = []
        task_features = []

        # 收集AGV特征
        for agv in agvs:
            state = self.agv_extractor.extract_features(agv)
            agv_features.append(state.raw_features)

        # 收集任务特征
        for task in tasks:
            state = self.task_extractor.extract_features(task, agvs)
            task_features.append(state.raw_features)

        stats = {
            'num_agvs': len(agvs),
            'num_tasks': len(tasks),
            'agv_feature_dim': self.agv_extractor.get_feature_dim(),
            'task_feature_dim': self.task_extractor.get_feature_dim(),
            'embed_dim': self.embed_dim,
            'global_state_shape': (len(agvs) + len(tasks), self.embed_dim)
        }

        if agv_features:
            agv_array = np.array(agv_features)
            stats.update({
                'agv_feature_mean': agv_array.mean(axis=0).tolist(),
                'agv_feature_std': agv_array.std(axis=0).tolist(),
                'agv_feature_min': agv_array.min(axis=0).tolist(),
                'agv_feature_max': agv_array.max(axis=0).tolist()
            })

        if task_features:
            task_array = np.array(task_features)
            stats.update({
                'task_feature_mean': task_array.mean(axis=0).tolist(),
                'task_feature_std': task_array.std(axis=0).tolist(),
                'task_feature_min': task_array.min(axis=0).tolist(),
                'task_feature_max': task_array.max(axis=0).tolist()
            })

        return stats

    def reset(self):
        """重置状态管理器"""
        self._last_global_state = None
        self._last_agv_states.clear()
        self._last_task_states.clear()

    def save_state_snapshot(self, agvs: List[AGVEntity], tasks: List[Task],
                          filename: str):
        """
        保存状态快照

        Args:
            agvs: AGV列表
            tasks: 任务列表
            filename: 保存文件名
        """
        snapshot = {
            'global_state': self.get_global_observation(agvs, tasks),
            'agv_states': [self.agv_extractor.extract_features(agv) for agv in agvs],
            'task_states': [self.task_extractor.extract_features(task, agvs) for task in tasks],
            'statistics': self.get_state_statistics(agvs, tasks),
            'config': self.config
        }

        torch.save(snapshot, filename)

    def load_state_snapshot(self, filename: str) -> Dict[str, Any]:
        """
        加载状态快照

        Args:
            filename: 文件名

        Returns:
            snapshot: 状态快照
        """
        return torch.load(filename)
