"""
性能指标模块
定义和计算各种性能评估指标
"""

import numpy as np
from typing import List, Dict, Any, Tuple
from collections import deque
import torch


class MetricsCalculator:
    """性能指标计算器"""

    def __init__(self, window_size: int = 100):
        """
        初始化指标计算器

        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.reset()

    def reset(self):
        """重置所有指标"""
        # 任务执行指标
        self.completed_tasks = deque(maxlen=self.window_size)
        self.total_tasks = deque(maxlen=self.window_size)
        self.completion_times = deque(maxlen=self.window_size)

        # 协作指标
        self.collisions = deque(maxlen=self.window_size)
        self.total_steps = deque(maxlen=self.window_size)
        self.waiting_times = deque(maxlen=self.window_size)

        # AGV负载指标
        self.agv_loads = deque(maxlen=self.window_size)

        # 注意力指标
        self.attention_weights = deque(maxlen=self.window_size)
        self.attention_entropy = deque(maxlen=self.window_size)

        # 奖励指标
        self.episode_rewards = deque(maxlen=self.window_size)
        self.step_rewards = deque(maxlen=self.window_size)

    def update_task_metrics(self, completed: int, total: int, completion_time: float = None):
        """更新任务执行指标"""
        self.completed_tasks.append(completed)
        self.total_tasks.append(total)
        if completion_time is not None:
            self.completion_times.append(completion_time)

    def update_collaboration_metrics(self, collisions: int, steps: int, waiting_time: float = 0):
        """更新协作指标"""
        self.collisions.append(collisions)
        self.total_steps.append(steps)
        self.waiting_times.append(waiting_time)

    def update_load_metrics(self, loads: List[float]):
        """更新AGV负载指标"""
        self.agv_loads.append(loads.copy())

    def update_attention_metrics(self, attention_weights: torch.Tensor):
        """更新注意力指标"""
        if attention_weights is not None:
            weights_np = attention_weights.detach().cpu().numpy()
            self.attention_weights.append(weights_np)

            # 计算注意力熵
            entropy = -np.sum(weights_np * np.log(weights_np + 1e-8))
            self.attention_entropy.append(entropy)

    def update_reward_metrics(self, episode_reward: float, step_reward: float = None):
        """更新奖励指标"""
        self.episode_rewards.append(episode_reward)
        if step_reward is not None:
            self.step_rewards.append(step_reward)

    def get_task_completion_rate(self) -> float:
        """计算任务完成率"""
        if not self.completed_tasks or not self.total_tasks:
            return 0.0

        total_completed = sum(self.completed_tasks)
        total_tasks = sum(self.total_tasks)

        return total_completed / total_tasks if total_tasks > 0 else 0.0

    def get_average_completion_time(self) -> float:
        """计算平均完成时间"""
        if not self.completion_times:
            return 0.0

        return np.mean(self.completion_times)

    def get_collision_rate(self) -> float:
        """计算碰撞率"""
        if not self.collisions or not self.total_steps:
            return 0.0

        total_collisions = sum(self.collisions)
        total_steps = sum(self.total_steps)

        return total_collisions / total_steps if total_steps > 0 else 0.0

    def get_load_balance_index(self) -> float:
        """计算负载均衡指数"""
        if not self.agv_loads:
            return 0.0

        # 计算最近的负载分布
        recent_loads = list(self.agv_loads)[-10:]  # 最近10个记录
        if not recent_loads:
            return 0.0

        # 计算平均负载方差
        load_variances = []
        for loads in recent_loads:
            if len(loads) > 1:
                variance = np.var(loads)
                mean_load = np.mean(loads)
                if mean_load > 0:
                    cv = np.sqrt(variance) / mean_load  # 变异系数
                    balance_index = 1 - cv  # 负载均衡指数
                    load_variances.append(max(0, balance_index))

        return np.mean(load_variances) if load_variances else 0.0

    def get_attention_stability(self) -> float:
        """计算注意力稳定性"""
        if len(self.attention_weights) < 2:
            return 1.0

        # 计算连续时间步间注意力权重的变化
        changes = []
        weights_list = list(self.attention_weights)

        for i in range(1, len(weights_list)):
            prev_weights = weights_list[i-1]
            curr_weights = weights_list[i]

            # 计算L2距离
            change = np.linalg.norm(curr_weights - prev_weights)
            changes.append(change)

        # 稳定性 = 1 - 平均变化量
        avg_change = np.mean(changes)
        stability = max(0, 1 - avg_change)

        return stability

    def get_attention_diversity(self) -> float:
        """计算注意力多样性（熵）"""
        if not self.attention_entropy:
            return 0.0

        return np.mean(self.attention_entropy)

    def get_average_reward(self) -> float:
        """计算平均奖励"""
        if not self.episode_rewards:
            return 0.0

        return np.mean(self.episode_rewards)

    def get_reward_std(self) -> float:
        """计算奖励标准差"""
        if not self.episode_rewards:
            return 0.0

        return np.std(self.episode_rewards)

    def get_all_metrics(self) -> Dict[str, float]:
        """获取所有指标"""
        return {
            # 任务执行效率
            "task_completion_rate": self.get_task_completion_rate(),
            "avg_completion_time": self.get_average_completion_time(),

            # 协作效率
            "collision_rate": self.get_collision_rate(),
            "load_balance_index": self.get_load_balance_index(),

            # 注意力机制效果
            "attention_stability": self.get_attention_stability(),
            "attention_diversity": self.get_attention_diversity(),

            # 奖励指标
            "avg_episode_reward": self.get_average_reward(),
            "reward_std": self.get_reward_std(),

            # 系统吞吐量
            "system_throughput": self.get_task_completion_rate() / max(self.get_average_completion_time(), 1.0)
        }

    def print_metrics(self):
        """打印所有指标"""
        metrics = self.get_all_metrics()

        print("\n" + "="*50)
        print("性能指标报告")
        print("="*50)

        print(f"任务完成率: {metrics['task_completion_rate']:.3f}")
        print(f"平均完成时间: {metrics['avg_completion_time']:.2f}")
        print(f"碰撞率: {metrics['collision_rate']:.4f}")
        print(f"负载均衡指数: {metrics['load_balance_index']:.3f}")
        print(f"注意力稳定性: {metrics['attention_stability']:.3f}")
        print(f"注意力多样性: {metrics['attention_diversity']:.3f}")
        print(f"平均回合奖励: {metrics['avg_episode_reward']:.2f}")
        print(f"奖励标准差: {metrics['reward_std']:.2f}")
        print(f"系统吞吐量: {metrics['system_throughput']:.3f}")

        print("="*50)


def calculate_path_efficiency(actual_path: List[Tuple[int, int]],
                            optimal_path: List[Tuple[int, int]]) -> float:
    """计算路径效率"""
    if not actual_path or not optimal_path:
        return 0.0

    actual_length = len(actual_path)
    optimal_length = len(optimal_path)

    return optimal_length / actual_length if actual_length > 0 else 0.0


def calculate_manhattan_distance(pos1: Tuple[int, int], pos2: Tuple[int, int]) -> int:
    """计算曼哈顿距离"""
    return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])