"""
AGV实体类
管理单个AGV的状态、行为和属性
"""

from typing import Tuple, List, Optional, Dict, Any
from enum import Enum
from collections import deque


class AGVStatus(Enum):
    """AGV状态枚举"""
    IDLE = 0        # 空闲
    MOVING = 1      # 移动中
    LOADING = 2     # 装载中
    UNLOADING = 3   # 卸载中
    WAITING = 4     # 等待中


class AGVEntity:
    """
    AGV实体类

    管理AGV的状态、任务队列、载重等信息
    """

    def __init__(self, agv_id: int, position: Tuple[int, int],
                 capacity: int = 25, speed: float = 1.0):
        """
        初始化AGV实体

        Args:
            agv_id: AGV唯一标识
            position: 初始位置 (x, y)
            capacity: 载重能力
            speed: 移动速度
        """
        self.agv_id = agv_id
        self.position = position
        self.capacity = capacity
        self.speed = speed

        # 状态信息
        self.status = AGVStatus.IDLE
        self.current_load = 0
        self.battery_level = 100.0  # 电池电量（百分比）

        # 任务相关
        self.task_queue = deque()  # 任务队列
        self.current_target = None  # 当前目标任务ID
        self.completed_tasks = []   # 已完成任务列表

        # 历史信息
        self.path_history = [position]  # 路径历史
        self.action_history = []        # 动作历史

        # 性能统计
        self.total_distance = 0
        self.total_waiting_time = 0
        self.collision_count = 0

    def get_state_vector(self, map_width: int, map_height: int) -> List[float]:
        """
        获取AGV状态向量（用于神经网络输入）

        Args:
            map_width: 地图宽度
            map_height: 地图高度

        Returns:
            state_vector: 状态向量 [x_norm, y_norm, load_norm, queue_len_norm, target_norm, status]
        """
        x_norm = self.position[0] / map_width
        y_norm = self.position[1] / map_height
        load_norm = self.current_load / self.capacity
        queue_len_norm = len(self.task_queue) / 4  # 假设最大队列长度为4
        target_norm = self.current_target / 15 if self.current_target is not None else -1  # 假设最大任务ID为15
        status_norm = self.status.value / 4  # 状态归一化

        return [x_norm, y_norm, load_norm, queue_len_norm, target_norm, status_norm]

    def assign_task(self, task_id: int) -> bool:
        """
        分配任务给AGV

        Args:
            task_id: 任务ID

        Returns:
            success: 是否成功分配
        """
        if len(self.task_queue) < 4:  # 最大队列长度限制
            self.task_queue.append(task_id)
            if self.current_target is None:
                self.current_target = self.task_queue.popleft()
            return True
        return False

    def complete_current_task(self, task_weight: int) -> bool:
        """
        完成当前任务

        Args:
            task_weight: 任务重量

        Returns:
            success: 是否成功完成
        """
        if self.current_target is not None:
            # 检查载重能力
            if self.current_load + task_weight <= self.capacity:
                self.completed_tasks.append(self.current_target)
                self.current_load += task_weight

                # 获取下一个任务
                if self.task_queue:
                    self.current_target = self.task_queue.popleft()
                else:
                    self.current_target = None
                    self.status = AGVStatus.IDLE

                return True
        return False

    def unload_cargo(self) -> int:
        """
        卸载货物

        Returns:
            unloaded_weight: 卸载的重量
        """
        unloaded_weight = self.current_load
        self.current_load = 0
        self.status = AGVStatus.IDLE
        return unloaded_weight

    def move_to(self, new_position: Tuple[int, int]) -> float:
        """
        移动到新位置

        Args:
            new_position: 新位置

        Returns:
            distance: 移动距离
        """
        old_position = self.position
        self.position = new_position

        # 计算移动距离
        distance = abs(new_position[0] - old_position[0]) + abs(new_position[1] - old_position[1])
        self.total_distance += distance

        # 更新路径历史
        self.path_history.append(new_position)
        if len(self.path_history) > 100:  # 限制历史长度
            self.path_history.pop(0)

        # 更新状态
        if distance > 0:
            self.status = AGVStatus.MOVING

        return distance

    def wait(self):
        """等待（不移动）"""
        self.status = AGVStatus.WAITING
        self.total_waiting_time += 1

    def get_completed_tasks(self) -> List[int]:
        """获取已完成任务列表"""
        completed = self.completed_tasks.copy()
        self.completed_tasks.clear()  # 清空已完成任务列表
        return completed

    def get_load_ratio(self) -> float:
        """获取载重比例"""
        return self.current_load / self.capacity

    def is_idle(self) -> bool:
        """检查是否空闲"""
        return self.status == AGVStatus.IDLE and self.current_target is None

    def is_overloaded(self, additional_weight: int = 0) -> bool:
        """检查是否超载"""
        return (self.current_load + additional_weight) > self.capacity

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            "agv_id": self.agv_id,
            "total_distance": self.total_distance,
            "total_waiting_time": self.total_waiting_time,
            "collision_count": self.collision_count,
            "current_load": self.current_load,
            "load_ratio": self.get_load_ratio(),
            "tasks_in_queue": len(self.task_queue),
            "status": self.status.name,
            "position": self.position
        }

    def reset(self, position: Tuple[int, int]):
        """重置AGV状态"""
        self.position = position
        self.status = AGVStatus.IDLE
        self.current_load = 0
        self.battery_level = 100.0

        self.task_queue.clear()
        self.current_target = None
        self.completed_tasks.clear()

        self.path_history = [position]
        self.action_history.clear()

        self.total_distance = 0
        self.total_waiting_time = 0
        self.collision_count = 0

    def __str__(self) -> str:
        """字符串表示"""
        return f"AGV{self.agv_id}@{self.position} [{self.status.name}] Load:{self.current_load}/{self.capacity}"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"AGVEntity(id={self.agv_id}, pos={self.position}, "
                f"status={self.status.name}, load={self.current_load}/{self.capacity}, "
                f"target={self.current_target}, queue_len={len(self.task_queue)})")