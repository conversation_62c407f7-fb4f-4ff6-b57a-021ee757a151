"""
层次化动作空间设计
实现多AGV协同调度的层次化动作空间和动作掩码机制
"""

import numpy as np
from enum import Enum
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
import torch

from .agv_entity import AGVEntity, AGVStatus


class ActionType(Enum):
    """动作类型枚举"""
    MOVE = "move"           # 移动动作
    TASK = "task"           # 任务动作
    COORDINATION = "coord"  # 协调动作
    WAIT = "wait"          # 等待动作


class MoveAction(Enum):
    """移动动作枚举"""
    UP = 0
    DOWN = 1
    LEFT = 2
    RIGHT = 3
    STAY = 4


class TaskAction(Enum):
    """任务动作枚举"""
    PICKUP = 5
    DELIVER = 6
    CANCEL_TASK = 7


class CoordinationAction(Enum):
    """协调动作枚举"""
    REQUEST_HELP = 8
    OFFER_HELP = 9
    YIELD_PATH = 10


class WaitAction(Enum):
    """等待动作枚举"""
    WAIT = 11


@dataclass
class ActionInfo:
    """动作信息"""
    action_id: int
    action_type: ActionType
    action_name: str
    description: str
    prerequisites: List[str]
    effects: List[str]


class HierarchicalActionSpace:
    """
    层次化动作空间
    实现多层次的动作组织和管理
    """
    
    def __init__(self):
        """初始化层次化动作空间"""
        self.action_mapping = self._create_action_mapping()
        self.action_hierarchy = self._create_action_hierarchy()
        self.total_actions = len(self.action_mapping)
        
    def _create_action_mapping(self) -> Dict[int, ActionInfo]:
        """创建动作映射"""
        actions = {}
        
        # 移动动作
        actions[MoveAction.UP.value] = ActionInfo(
            action_id=MoveAction.UP.value,
            action_type=ActionType.MOVE,
            action_name="move_up",
            description="向上移动一格",
            prerequisites=["not_at_top_boundary", "no_collision"],
            effects=["position_change", "energy_consumption"]
        )
        
        actions[MoveAction.DOWN.value] = ActionInfo(
            action_id=MoveAction.DOWN.value,
            action_type=ActionType.MOVE,
            action_name="move_down",
            description="向下移动一格",
            prerequisites=["not_at_bottom_boundary", "no_collision"],
            effects=["position_change", "energy_consumption"]
        )
        
        actions[MoveAction.LEFT.value] = ActionInfo(
            action_id=MoveAction.LEFT.value,
            action_type=ActionType.MOVE,
            action_name="move_left",
            description="向左移动一格",
            prerequisites=["not_at_left_boundary", "no_collision"],
            effects=["position_change", "energy_consumption"]
        )
        
        actions[MoveAction.RIGHT.value] = ActionInfo(
            action_id=MoveAction.RIGHT.value,
            action_type=ActionType.MOVE,
            action_name="move_right",
            description="向右移动一格",
            prerequisites=["not_at_right_boundary", "no_collision"],
            effects=["position_change", "energy_consumption"]
        )
        
        actions[MoveAction.STAY.value] = ActionInfo(
            action_id=MoveAction.STAY.value,
            action_type=ActionType.MOVE,
            action_name="stay",
            description="保持当前位置",
            prerequisites=[],
            effects=["no_position_change", "minimal_energy_consumption"]
        )
        
        # 任务动作
        actions[TaskAction.PICKUP.value] = ActionInfo(
            action_id=TaskAction.PICKUP.value,
            action_type=ActionType.TASK,
            action_name="pickup",
            description="拾取货物",
            prerequisites=["at_pickup_location", "has_capacity", "task_assigned"],
            effects=["load_increase", "task_progress"]
        )
        
        actions[TaskAction.DELIVER.value] = ActionInfo(
            action_id=TaskAction.DELIVER.value,
            action_type=ActionType.TASK,
            action_name="deliver",
            description="交付货物",
            prerequisites=["at_delivery_location", "has_load", "task_assigned"],
            effects=["load_decrease", "task_completion", "reward_gain"]
        )
        
        actions[TaskAction.CANCEL_TASK.value] = ActionInfo(
            action_id=TaskAction.CANCEL_TASK.value,
            action_type=ActionType.TASK,
            action_name="cancel_task",
            description="取消当前任务",
            prerequisites=["has_assigned_task"],
            effects=["task_cancellation", "penalty"]
        )
        
        # 协调动作
        actions[CoordinationAction.REQUEST_HELP.value] = ActionInfo(
            action_id=CoordinationAction.REQUEST_HELP.value,
            action_type=ActionType.COORDINATION,
            action_name="request_help",
            description="请求其他AGV协助",
            prerequisites=["has_task", "need_assistance"],
            effects=["coordination_signal", "potential_collaboration"]
        )
        
        actions[CoordinationAction.OFFER_HELP.value] = ActionInfo(
            action_id=CoordinationAction.OFFER_HELP.value,
            action_type=ActionType.COORDINATION,
            action_name="offer_help",
            description="主动提供协助",
            prerequisites=["is_idle_or_low_priority", "nearby_agv_needs_help"],
            effects=["coordination_signal", "potential_collaboration"]
        )
        
        actions[CoordinationAction.YIELD_PATH.value] = ActionInfo(
            action_id=CoordinationAction.YIELD_PATH.value,
            action_type=ActionType.COORDINATION,
            action_name="yield_path",
            description="让出路径给其他AGV",
            prerequisites=["path_conflict", "lower_priority"],
            effects=["path_yielding", "conflict_resolution"]
        )
        
        # 等待动作
        actions[WaitAction.WAIT.value] = ActionInfo(
            action_id=WaitAction.WAIT.value,
            action_type=ActionType.WAIT,
            action_name="wait",
            description="等待一个时间步",
            prerequisites=[],
            effects=["time_consumption", "minimal_energy_consumption"]
        )
        
        return actions
    
    def _create_action_hierarchy(self) -> Dict[ActionType, List[int]]:
        """创建动作层次结构"""
        hierarchy = {}
        
        for action_id, action_info in self.action_mapping.items():
            action_type = action_info.action_type
            if action_type not in hierarchy:
                hierarchy[action_type] = []
            hierarchy[action_type].append(action_id)
        
        return hierarchy
    
    def get_action_info(self, action_id: int) -> Optional[ActionInfo]:
        """获取动作信息"""
        return self.action_mapping.get(action_id)
    
    def get_actions_by_type(self, action_type: ActionType) -> List[int]:
        """根据类型获取动作列表"""
        return self.action_hierarchy.get(action_type, [])
    
    def get_total_actions(self) -> int:
        """获取总动作数"""
        return self.total_actions
    
    def is_valid_action(self, action_id: int) -> bool:
        """检查动作是否有效"""
        return action_id in self.action_mapping


class ActionMaskManager:
    """
    动作掩码管理器
    根据AGV状态和环境约束生成动作掩码
    """
    
    def __init__(self, action_space: HierarchicalActionSpace, map_width: int = 26, map_height: int = 10):
        """
        初始化动作掩码管理器
        
        Args:
            action_space: 层次化动作空间
            map_width: 地图宽度
            map_height: 地图高度
        """
        self.action_space = action_space
        self.map_width = map_width
        self.map_height = map_height
        
    def generate_action_mask(self, 
                           agv: AGVEntity, 
                           other_agvs: List[AGVEntity],
                           obstacles: Set[Tuple[int, int]] = None) -> np.ndarray:
        """
        生成AGV的动作掩码
        
        Args:
            agv: 当前AGV
            other_agvs: 其他AGV列表
            obstacles: 障碍物位置集合
            
        Returns:
            action_mask: 动作掩码数组，1表示可执行，0表示不可执行
        """
        if obstacles is None:
            obstacles = set()
        
        mask = np.zeros(self.action_space.get_total_actions(), dtype=np.float32)
        
        # 检查移动动作
        mask = self._mask_movement_actions(mask, agv, other_agvs, obstacles)
        
        # 检查任务动作
        mask = self._mask_task_actions(mask, agv)
        
        # 检查协调动作
        mask = self._mask_coordination_actions(mask, agv, other_agvs)
        
        # 等待动作总是可用
        mask[WaitAction.WAIT.value] = 1.0
        
        return mask
    
    def _mask_movement_actions(self, 
                             mask: np.ndarray, 
                             agv: AGVEntity,
                             other_agvs: List[AGVEntity],
                             obstacles: Set[Tuple[int, int]]) -> np.ndarray:
        """掩码移动动作"""
        x, y = agv.position
        
        # 获取其他AGV位置
        other_positions = {other_agv.position for other_agv in other_agvs if other_agv.agv_id != agv.agv_id}
        
        # 检查上移
        if y > 0 and (x, y - 1) not in other_positions and (x, y - 1) not in obstacles:
            mask[MoveAction.UP.value] = 1.0
        
        # 检查下移
        if y < self.map_height - 1 and (x, y + 1) not in other_positions and (x, y + 1) not in obstacles:
            mask[MoveAction.DOWN.value] = 1.0
        
        # 检查左移
        if x > 0 and (x - 1, y) not in other_positions and (x - 1, y) not in obstacles:
            mask[MoveAction.LEFT.value] = 1.0
        
        # 检查右移
        if x < self.map_width - 1 and (x + 1, y) not in other_positions and (x + 1, y) not in obstacles:
            mask[MoveAction.RIGHT.value] = 1.0
        
        # 停留总是可用（除非当前位置被占用，但这种情况不应该发生）
        mask[MoveAction.STAY.value] = 1.0
        
        return mask
    
    def _mask_task_actions(self, mask: np.ndarray, agv: AGVEntity) -> np.ndarray:
        """掩码任务动作"""
        # 拾取动作：需要在拾取位置且有容量且有分配的任务
        if (agv.current_target is not None and
            agv.current_load < agv.capacity and
            agv.status in [AGVStatus.IDLE, AGVStatus.MOVING]):
            # 这里需要检查是否在拾取位置，简化处理
            mask[TaskAction.PICKUP.value] = 1.0

        # 交付动作：需要在交付位置且有货物
        if (agv.current_load > 0 and
            agv.current_target is not None):
            # 这里需要检查是否在交付位置，简化处理
            mask[TaskAction.DELIVER.value] = 1.0

        # 取消任务：有分配的任务时可用
        if agv.current_target is not None:
            mask[TaskAction.CANCEL_TASK.value] = 1.0
        
        return mask
    
    def _mask_coordination_actions(self, 
                                 mask: np.ndarray, 
                                 agv: AGVEntity,
                                 other_agvs: List[AGVEntity]) -> np.ndarray:
        """掩码协调动作"""
        # 请求帮助：有任务且需要协助时可用
        if agv.current_target is not None and agv.status != AGVStatus.IDLE:
            mask[CoordinationAction.REQUEST_HELP.value] = 1.0

        # 提供帮助：空闲或低优先级时可用
        if agv.status == AGVStatus.IDLE or agv.current_target is None:
            # 检查是否有其他AGV需要帮助
            for other_agv in other_agvs:
                if (other_agv.agv_id != agv.agv_id and
                    other_agv.current_target is not None and
                    self._calculate_distance(agv.position, other_agv.position) <= 5):
                    mask[CoordinationAction.OFFER_HELP.value] = 1.0
                    break
        
        # 让出路径：有路径冲突时可用
        if self._has_path_conflict(agv, other_agvs):
            mask[CoordinationAction.YIELD_PATH.value] = 1.0
        
        return mask
    
    def _calculate_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """计算曼哈顿距离"""
        return abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
    
    def _has_path_conflict(self, agv: AGVEntity, other_agvs: List[AGVEntity]) -> bool:
        """检查是否有路径冲突"""
        # 简化的路径冲突检测
        for other_agv in other_agvs:
            if (other_agv.agv_id != agv.agv_id and
                self._calculate_distance(agv.position, other_agv.position) <= 2):
                return True
        return False
