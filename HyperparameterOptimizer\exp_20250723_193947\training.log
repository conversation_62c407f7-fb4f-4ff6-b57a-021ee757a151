2025-07-23 19:39:47 - exp_20250723_193947 - INFO - <PERSON><PERSON> initialized for experiment: exp_20250723_193947
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 超参数优化器初始化完成，方法: random, 试验次数: 10
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 开始超参数优化，方法: random
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(128), 'attention_num_heads': np.int64(32), 'attention_dropout': np.float64(0.0), 'attention_pos_dim': np.int64(64), 'hidden_dim': np.int64(256), 'num_layers': np.int64(4), 'activation': np.str_('relu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(5), 'clip_param': np.float64(0.3), 'entropy_coeff': np.float64(0.01), 'value_loss_coeff': np.float64(1.0), 'near_threshold': np.float64(3.0), 'far_threshold': np.float64(10.0), 'temperature_scale': np.float64(5.0)}
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4788
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(256), 'attention_num_heads': np.int64(32), 'attention_dropout': np.float64(0.3), 'attention_pos_dim': np.int64(128), 'hidden_dim': np.int64(256), 'num_layers': np.int64(3), 'activation': np.str_('relu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(10), 'clip_param': np.float64(0.2), 'entropy_coeff': np.float64(0.001), 'value_loss_coeff': np.float64(0.5), 'near_threshold': np.float64(8.0), 'far_threshold': np.float64(10.0), 'temperature_scale': np.float64(2.0)}
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4769
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(32), 'attention_num_heads': np.int64(4), 'attention_dropout': np.float64(0.1), 'attention_pos_dim': np.int64(64), 'hidden_dim': np.int64(256), 'num_layers': np.int64(2), 'activation': np.str_('gelu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(20), 'clip_param': np.float64(0.3), 'entropy_coeff': np.float64(0.1), 'value_loss_coeff': np.float64(0.5), 'near_threshold': np.float64(1.0), 'far_threshold': np.float64(15.0), 'temperature_scale': np.float64(1.0)}
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4840
2025-07-23 19:39:47 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(64), 'attention_num_heads': np.int64(8), 'attention_dropout': np.float64(0.1), 'attention_pos_dim': np.int64(128), 'hidden_dim': np.int64(256), 'num_layers': np.int64(4), 'activation': np.str_('gelu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(10), 'clip_param': np.float64(0.1), 'entropy_coeff': np.float64(0.1), 'value_loss_coeff': np.float64(0.25), 'near_threshold': np.float64(5.0), 'far_threshold': np.float64(8.0), 'temperature_scale': np.float64(2.0)}
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4839
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(32), 'attention_num_heads': np.int64(32), 'attention_dropout': np.float64(0.1), 'attention_pos_dim': np.int64(64), 'hidden_dim': np.int64(256), 'num_layers': np.int64(4), 'activation': np.str_('gelu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(5), 'clip_param': np.float64(0.3), 'entropy_coeff': np.float64(0.1), 'value_loss_coeff': np.float64(0.25), 'near_threshold': np.float64(3.0), 'far_threshold': np.float64(10.0), 'temperature_scale': np.float64(3.0)}
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4813
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(256), 'attention_num_heads': np.int64(16), 'attention_dropout': np.float64(0.3), 'attention_pos_dim': np.int64(128), 'hidden_dim': np.int64(256), 'num_layers': np.int64(3), 'activation': np.str_('gelu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(20), 'clip_param': np.float64(0.2), 'entropy_coeff': np.float64(0.001), 'value_loss_coeff': np.float64(1.0), 'near_threshold': np.float64(3.0), 'far_threshold': np.float64(10.0), 'temperature_scale': np.float64(5.0)}
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4793
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(128), 'attention_num_heads': np.int64(4), 'attention_dropout': np.float64(0.1), 'attention_pos_dim': np.int64(128), 'hidden_dim': np.int64(256), 'num_layers': np.int64(2), 'activation': np.str_('tanh'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(10), 'clip_param': np.float64(0.3), 'entropy_coeff': np.float64(0.01), 'value_loss_coeff': np.float64(0.5), 'near_threshold': np.float64(3.0), 'far_threshold': np.float64(5.0), 'temperature_scale': np.float64(1.0)}
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4842
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(128), 'attention_num_heads': np.int64(32), 'attention_dropout': np.float64(0.0), 'attention_pos_dim': np.int64(128), 'hidden_dim': np.int64(256), 'num_layers': np.int64(4), 'activation': np.str_('relu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(20), 'clip_param': np.float64(0.2), 'entropy_coeff': np.float64(0.01), 'value_loss_coeff': np.float64(0.5), 'near_threshold': np.float64(8.0), 'far_threshold': np.float64(5.0), 'temperature_scale': np.float64(0.5)}
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4874
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(32), 'attention_num_heads': np.int64(32), 'attention_dropout': np.float64(0.0), 'attention_pos_dim': np.int64(32), 'hidden_dim': np.int64(256), 'num_layers': np.int64(3), 'activation': np.str_('tanh'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(10), 'clip_param': np.float64(0.3), 'entropy_coeff': np.float64(0.1), 'value_loss_coeff': np.float64(0.5), 'near_threshold': np.float64(3.0), 'far_threshold': np.float64(5.0), 'temperature_scale': np.float64(5.0)}
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4897
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 开始评估超参数配置: {'attention_feature_dim': np.int64(256), 'attention_num_heads': np.int64(4), 'attention_dropout': np.float64(0.2), 'attention_pos_dim': np.int64(16), 'hidden_dim': np.int64(256), 'num_layers': np.int64(4), 'activation': np.str_('gelu'), 'learning_rate': np.float64(0.0003), 'batch_size': np.int64(256), 'num_epochs': np.int64(20), 'clip_param': np.float64(0.1), 'entropy_coeff': np.float64(0.1), 'value_loss_coeff': np.float64(1.0), 'near_threshold': np.float64(8.0), 'far_threshold': np.float64(8.0), 'temperature_scale': np.float64(1.0)}
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数评估完成，总分: 0.4766
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 随机搜索进度: 10/10
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 超参数优化完成，最佳得分: 0.4897, 用时: 0.71s
2025-07-23 19:39:48 - exp_20250723_193947 - INFO - 优化结果已保存到: ./results/hyperparameter_optimization\hyperopt_random_20250723_193947\optimization_results.json
