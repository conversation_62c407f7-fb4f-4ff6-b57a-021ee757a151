# 基于融合双层注意力机制的MAPPO多AGV协同调度系统

## 项目概述

本项目实现了一个基于融合双层注意力机制的MAPPO（Multi-Agent Proximal Policy Optimization）算法，用于解决多AGV（自动导引车）协同调度优化问题。

### 核心创新点

1. **双层注意力机制**：
   - 第一层：任务分配注意力机制，专门处理AGV与任务之间的匹配关系
   - 第二层：协作感知注意力机制，建模AGV之间的协作交互关系

2. **稀疏化优化策略**：
   - Top-K稀疏注意力，将计算复杂度从O(n²)降低到O(nk)
   - 约束增强机制，融合距离、载重、优先级等多维约束

3. **MAPPO深度融合**：
   - 注意力增强策略网络，将注意力输出融入策略决策
   - 全局价值估计，利用注意力信息改进价值函数

### 技术架构

- **环境模拟**：26×10网格世界仓储环境
- **智能体**：4个同构AGV，载重能力25单位
- **任务配置**：16个运输任务，重量为5或10单位
- **训练策略**：三阶段课程学习 + 注意力预训练

## 项目结构

```
biye1.0/
├── README.md                          # 项目说明文档
├── requirements.txt                   # Python依赖包
├── config/                           # 配置文件目录
│   ├── __init__.py
│   ├── env_config.py                 # 环境配置
│   ├── model_config.py               # 模型配置
│   └── training_config.py            # 训练配置
├── src/                              # 源代码目录
│   ├── __init__.py
│   ├── environment/                  # 环境模块
│   │   ├── __init__.py
│   │   ├── warehouse_env.py          # 仓储环境实现
│   │   ├── agv_entity.py             # AGV实体类
│   │   └── task_manager.py           # 任务管理器
│   ├── attention/                    # 注意力机制模块
│   │   ├── __init__.py
│   │   ├── task_attention.py         # 第一层任务分配注意力
│   │   ├── collab_attention.py       # 第二层协作感知注意力
│   │   └── attention_fusion.py       # 双层注意力融合
│   ├── mappo/                        # MAPPO算法模块
│   │   ├── __init__.py
│   │   ├── policy_network.py         # 策略网络
│   │   ├── value_network.py          # 价值网络
│   │   ├── ppo_trainer.py            # PPO训练器
│   │   └── buffer.py                 # 经验回放缓冲区
│   ├── training/                     # 训练模块
│   │   ├── __init__.py
│   │   ├── curriculum_learning.py    # 课程学习
│   │   ├── attention_pretrain.py     # 注意力预训练
│   │   └── meta_learning.py          # 元学习
│   └── utils/                        # 工具模块
│       ├── __init__.py
│       ├── logger.py                 # 日志系统
│       ├── visualizer.py             # 可视化工具
│       └── metrics.py                # 性能指标
├── experiments/                      # 实验目录
│   ├── __init__.py
│   ├── train.py                      # 主训练脚本
│   ├── evaluate.py                   # 评估脚本
│   └── hyperopt.py                   # 超参数优化
├── logs/                             # 日志目录
├── models/                           # 模型保存目录
├── results/                          # 结果保存目录
└── tests/                            # 测试目录
    ├── __init__.py
    ├── test_environment.py           # 环境测试
    ├── test_attention.py             # 注意力机制测试
    └── test_mappo.py                 # MAPPO算法测试
```

## 环境要求

- Python 3.8+
- PyTorch 1.12+
- NumPy 1.21+
- Matplotlib 3.5+
- TensorBoard 2.8+

## 快速开始

1. **环境安装**：
```bash
pip install -r requirements.txt
```

2. **运行训练**：
```bash
python experiments/train.py
```

3. **查看结果**：
```bash
tensorboard --logdir logs/
```

## 研究成果

本项目预期实现以下技术指标：
- 任务完成率：≥ 92%
- 计算效率提升：≥ 60%
- 碰撞率：≤ 3%
- 协作效率提升：≥ 40%

## 学术贡献

1. 提出了稀疏双层注意力机制，有效降低计算复杂度
2. 实现了注意力机制与MAPPO的深度融合框架
3. 设计了多维度课程学习和预训练策略

## 联系方式

如有问题，请联系项目开发者。