"""
性能监控核心模块
实时收集和存储训练过程中的各种性能指标
"""

import time
import json
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import numpy as np
import pandas as pd
import pickle
import os


@dataclass
class TrainingMetrics:
    """训练指标数据结构"""
    timestamp: float
    episode: int
    step: int
    
    # 基础性能指标
    episode_reward: float
    episode_length: int
    success_rate: float
    completion_rate: float
    
    # 损失指标
    policy_loss: float
    value_loss: float
    entropy_loss: float
    total_loss: float
    
    # 学习指标
    learning_rate: float
    grad_norm: float
    explained_variance: float
    
    # AGV特定指标
    avg_agv_utilization: float
    task_completion_time: float
    collision_count: int
    deadlock_count: int
    
    # 注意力机制指标
    task_attention_entropy: float
    collaboration_attention_entropy: float
    attention_sparsity: float


@dataclass
class AttentionWeights:
    """注意力权重数据结构"""
    timestamp: float
    episode: int
    step: int
    
    # 任务分配注意力权重
    task_attention_weights: np.ndarray  # [num_agvs, num_tasks]
    task_attention_scores: np.ndarray   # [num_agvs, num_tasks]
    
    # 协作感知注意力权重
    collaboration_weights: np.ndarray   # [num_agvs, num_agvs]
    near_attention_weights: np.ndarray  # [num_agvs, num_agvs]
    far_attention_weights: np.ndarray   # [num_agvs, num_agvs]
    
    # 温度参数
    adaptive_temperatures: np.ndarray   # [num_agvs]


@dataclass
class AGVTrajectory:
    """AGV轨迹数据结构"""
    timestamp: float
    episode: int
    step: int
    
    # AGV状态
    agv_positions: List[Tuple[int, int]]  # [(x, y), ...]
    agv_loads: List[int]                  # [load1, load2, ...]
    agv_statuses: List[str]               # ['idle', 'moving', ...]
    agv_targets: List[Optional[int]]      # [task_id, None, ...]
    
    # 任务状态
    task_positions: List[Tuple[Tuple[int, int], Tuple[int, int]]]  # [(pickup, delivery), ...]
    task_statuses: List[str]              # ['pending', 'assigned', ...]
    task_assignments: List[Optional[int]] # [agv_id, None, ...]
    
    # 环境状态
    obstacles: List[Tuple[int, int]]      # [(x, y), ...]
    completed_tasks: int
    total_tasks: int


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, save_dir: str = "./monitoring_data", buffer_size: int = 10000):
        """
        初始化性能监控器
        
        Args:
            save_dir: 数据保存目录
            buffer_size: 内存缓冲区大小
        """
        self.save_dir = save_dir
        self.buffer_size = buffer_size
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 数据缓冲区
        self.training_metrics_buffer = deque(maxlen=buffer_size)
        self.attention_weights_buffer = deque(maxlen=buffer_size)
        self.trajectory_buffer = deque(maxlen=buffer_size)
        
        # 实时统计
        self.current_episode = 0
        self.current_step = 0
        self.start_time = time.time()
        
        # 聚合统计
        self.episode_stats = defaultdict(list)
        self.step_stats = defaultdict(list)
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 监控状态
        self.is_monitoring = False
        
        print(f"✓ 性能监控器初始化完成，数据保存目录: {save_dir}")
    
    def start_monitoring(self):
        """开始监控"""
        with self.lock:
            self.is_monitoring = True
            self.start_time = time.time()
        print("✓ 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        with self.lock:
            self.is_monitoring = False
        print("✓ 性能监控已停止")
    
    def log_training_metrics(self, metrics: Dict[str, Any]):
        """记录训练指标"""
        if not self.is_monitoring:
            return
        
        try:
            # 创建训练指标对象
            training_metrics = TrainingMetrics(
                timestamp=time.time(),
                episode=metrics.get('episode', self.current_episode),
                step=metrics.get('step', self.current_step),
                
                # 基础性能指标
                episode_reward=float(metrics.get('episode_reward', 0.0)),
                episode_length=int(metrics.get('episode_length', 0)),
                success_rate=float(metrics.get('success_rate', 0.0)),
                completion_rate=float(metrics.get('completion_rate', 0.0)),
                
                # 损失指标
                policy_loss=float(metrics.get('policy_loss', 0.0)),
                value_loss=float(metrics.get('value_loss', 0.0)),
                entropy_loss=float(metrics.get('entropy_loss', 0.0)),
                total_loss=float(metrics.get('total_loss', 0.0)),
                
                # 学习指标
                learning_rate=float(metrics.get('learning_rate', 0.0)),
                grad_norm=float(metrics.get('grad_norm', 0.0)),
                explained_variance=float(metrics.get('explained_variance', 0.0)),
                
                # AGV特定指标
                avg_agv_utilization=float(metrics.get('avg_agv_utilization', 0.0)),
                task_completion_time=float(metrics.get('task_completion_time', 0.0)),
                collision_count=int(metrics.get('collision_count', 0)),
                deadlock_count=int(metrics.get('deadlock_count', 0)),
                
                # 注意力机制指标
                task_attention_entropy=float(metrics.get('task_attention_entropy', 0.0)),
                collaboration_attention_entropy=float(metrics.get('collaboration_attention_entropy', 0.0)),
                attention_sparsity=float(metrics.get('attention_sparsity', 0.0))
            )
            
            # 添加到缓冲区
            with self.lock:
                self.training_metrics_buffer.append(training_metrics)
                
                # 更新聚合统计
                for key, value in asdict(training_metrics).items():
                    if isinstance(value, (int, float)):
                        self.episode_stats[key].append(value)
            
            # 更新当前状态
            self.current_episode = training_metrics.episode
            self.current_step = training_metrics.step
            
        except Exception as e:
            print(f"❌ 记录训练指标时出错: {e}")
    
    def log_attention_weights(self, attention_data: Dict[str, Any]):
        """记录注意力权重"""
        if not self.is_monitoring:
            return
        
        try:
            # 创建注意力权重对象
            attention_weights = AttentionWeights(
                timestamp=time.time(),
                episode=attention_data.get('episode', self.current_episode),
                step=attention_data.get('step', self.current_step),
                
                # 任务分配注意力权重
                task_attention_weights=np.array(attention_data.get('task_attention_weights', [])),
                task_attention_scores=np.array(attention_data.get('task_attention_scores', [])),
                
                # 协作感知注意力权重
                collaboration_weights=np.array(attention_data.get('collaboration_weights', [])),
                near_attention_weights=np.array(attention_data.get('near_attention_weights', [])),
                far_attention_weights=np.array(attention_data.get('far_attention_weights', [])),
                
                # 温度参数
                adaptive_temperatures=np.array(attention_data.get('adaptive_temperatures', []))
            )
            
            # 添加到缓冲区
            with self.lock:
                self.attention_weights_buffer.append(attention_weights)
                
        except Exception as e:
            print(f"❌ 记录注意力权重时出错: {e}")
    
    def log_trajectory(self, trajectory_data: Dict[str, Any]):
        """记录AGV轨迹"""
        if not self.is_monitoring:
            return
        
        try:
            # 创建轨迹对象
            trajectory = AGVTrajectory(
                timestamp=time.time(),
                episode=trajectory_data.get('episode', self.current_episode),
                step=trajectory_data.get('step', self.current_step),
                
                # AGV状态
                agv_positions=trajectory_data.get('agv_positions', []),
                agv_loads=trajectory_data.get('agv_loads', []),
                agv_statuses=trajectory_data.get('agv_statuses', []),
                agv_targets=trajectory_data.get('agv_targets', []),
                
                # 任务状态
                task_positions=trajectory_data.get('task_positions', []),
                task_statuses=trajectory_data.get('task_statuses', []),
                task_assignments=trajectory_data.get('task_assignments', []),
                
                # 环境状态
                obstacles=trajectory_data.get('obstacles', []),
                completed_tasks=trajectory_data.get('completed_tasks', 0),
                total_tasks=trajectory_data.get('total_tasks', 0)
            )
            
            # 添加到缓冲区
            with self.lock:
                self.trajectory_buffer.append(trajectory)
                
        except Exception as e:
            print(f"❌ 记录轨迹数据时出错: {e}")
    
    def get_recent_metrics(self, n: int = 100) -> List[TrainingMetrics]:
        """获取最近的训练指标"""
        with self.lock:
            return list(self.training_metrics_buffer)[-n:]
    
    def get_recent_attention_weights(self, n: int = 10) -> List[AttentionWeights]:
        """获取最近的注意力权重"""
        with self.lock:
            return list(self.attention_weights_buffer)[-n:]
    
    def get_recent_trajectories(self, n: int = 10) -> List[AGVTrajectory]:
        """获取最近的轨迹数据"""
        with self.lock:
            return list(self.trajectory_buffer)[-n:]
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        with self.lock:
            if not self.episode_stats:
                return {}
            
            stats = {}
            for key, values in self.episode_stats.items():
                if values and isinstance(values[0], (int, float)):
                    stats[key] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values),
                        'latest': values[-1] if values else 0
                    }
            
            # 添加运行时统计
            stats['runtime'] = {
                'total_time': time.time() - self.start_time,
                'current_episode': self.current_episode,
                'current_step': self.current_step,
                'episodes_per_hour': self.current_episode / ((time.time() - self.start_time) / 3600) if self.current_episode > 0 else 0
            }
            
            return stats
    
    def save_data(self, filename_prefix: str = None):
        """保存数据到文件"""
        if filename_prefix is None:
            filename_prefix = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 保存训练指标
            metrics_file = os.path.join(self.save_dir, f"{filename_prefix}_training_metrics.pkl")
            with open(metrics_file, 'wb') as f:
                pickle.dump(list(self.training_metrics_buffer), f)
            
            # 保存注意力权重
            attention_file = os.path.join(self.save_dir, f"{filename_prefix}_attention_weights.pkl")
            with open(attention_file, 'wb') as f:
                pickle.dump(list(self.attention_weights_buffer), f)
            
            # 保存轨迹数据
            trajectory_file = os.path.join(self.save_dir, f"{filename_prefix}_trajectories.pkl")
            with open(trajectory_file, 'wb') as f:
                pickle.dump(list(self.trajectory_buffer), f)
            
            # 保存汇总统计
            stats_file = os.path.join(self.save_dir, f"{filename_prefix}_summary_stats.json")
            with open(stats_file, 'w') as f:
                json.dump(self.get_summary_stats(), f, indent=2)
            
            print(f"✓ 监控数据已保存，前缀: {filename_prefix}")
            
        except Exception as e:
            print(f"❌ 保存数据时出错: {e}")
    
    def load_data(self, filename_prefix: str):
        """从文件加载数据"""
        try:
            # 加载训练指标
            metrics_file = os.path.join(self.save_dir, f"{filename_prefix}_training_metrics.pkl")
            if os.path.exists(metrics_file):
                with open(metrics_file, 'rb') as f:
                    metrics = pickle.load(f)
                    self.training_metrics_buffer.extend(metrics)
            
            # 加载注意力权重
            attention_file = os.path.join(self.save_dir, f"{filename_prefix}_attention_weights.pkl")
            if os.path.exists(attention_file):
                with open(attention_file, 'rb') as f:
                    attention_weights = pickle.load(f)
                    self.attention_weights_buffer.extend(attention_weights)
            
            # 加载轨迹数据
            trajectory_file = os.path.join(self.save_dir, f"{filename_prefix}_trajectories.pkl")
            if os.path.exists(trajectory_file):
                with open(trajectory_file, 'rb') as f:
                    trajectories = pickle.load(f)
                    self.trajectory_buffer.extend(trajectories)
            
            print(f"✓ 监控数据已加载，前缀: {filename_prefix}")
            
        except Exception as e:
            print(f"❌ 加载数据时出错: {e}")
    
    def clear_buffers(self):
        """清空缓冲区"""
        with self.lock:
            self.training_metrics_buffer.clear()
            self.attention_weights_buffer.clear()
            self.trajectory_buffer.clear()
            self.episode_stats.clear()
            self.step_stats.clear()
        print("✓ 监控数据缓冲区已清空")
