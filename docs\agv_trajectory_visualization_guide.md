# AGV轨迹可视化系统使用指南

## 概述

AGV轨迹可视化系统是一个完整的轨迹数据收集、存储和可视化解决方案，专为多AGV仓储环境设计。系统提供实时监控、离线分析和多样化的可视化功能。

## 系统架构

### 核心组件

1. **TrajectoryCollector** - 轨迹数据收集器
2. **TrajectoryVisualizer** - 轨迹可视化器  
3. **RealTimeTrajectoryMonitor** - 实时轨迹监控器

### 数据结构

- **TrajectoryPoint** - 轨迹点数据
- **TaskEvent** - 任务事件数据
- **CollisionEvent** - 碰撞事件数据

## 快速开始

### 1. 导入模块

```python
from src.visualization import (
    TrajectoryCollector, 
    TrajectoryVisualizer, 
    RealTimeTrajectoryMonitor
)
```

### 2. 基本使用流程

#### 数据收集

```python
# 创建轨迹收集器
collector = TrajectoryCollector(
    max_episodes=100, 
    save_dir="./trajectory_data"
)

# 开始回合
collector.start_episode(episode_id=0, metadata={'env': 'warehouse'})

# 记录步骤数据
for step in range(max_steps):
    # 获取AGV状态、动作和奖励
    actions, rewards = env.step()
    
    # 记录轨迹
    collector.record_step(agvs, actions, rewards)
    
    # 记录任务事件（可选）
    if task_completed:
        collector.record_task_event(task, 'pickup_completed', agv_id, position)
    
    # 记录碰撞事件（可选）
    if collision_detected:
        collector.record_collision_event(agv1, agv2, 'near_miss', True)

# 结束回合
collector.end_episode(final_metadata={'success': True})
```

#### 离线可视化

```python
# 创建可视化器
visualizer = TrajectoryVisualizer(collector=collector)

# 加载回合数据
episode_data = collector.load_episode_data(episode_id=0)

# 静态轨迹图
visualizer.visualize_static_trajectory(
    episode_data, 
    show_full_paths=True,
    show_task_events=True,
    save_path="trajectory_static.png"
)

# 轨迹热力图
visualizer.create_trajectory_heatmap(
    episode_data,
    save_path="trajectory_heatmap.png"
)

# 动态轨迹动画
anim = visualizer.visualize_episode(
    episode_data,
    animation_speed=200,
    save_path="trajectory_animation.gif"
)

# 多回合对比
visualizer.compare_episodes(
    episode_ids=[0, 1, 2],
    save_path="episodes_comparison.png"
)
```

#### 实时监控

```python
# 创建实时监控器
monitor = RealTimeTrajectoryMonitor()

# 启动监控
monitor.start_monitoring("实时AGV轨迹监控")

# 实时更新
for step in range(max_steps):
    # 执行环境步骤
    env.step()
    
    # 更新监控显示
    monitor.update_agv_positions(agvs, step)
    
    # 控制更新频率
    time.sleep(0.1)

# 停止监控
monitor.stop_monitoring()
```

## 高级功能

### 1. 自定义数据收集

```python
# 自定义轨迹点
custom_point = TrajectoryPoint(
    timestamp=time.time(),
    agv_id=1,
    position=(10, 5),
    status='MOVING',
    current_load=15,
    target_task=3,
    action='move_right',
    reward=0.1
)

# 自定义任务事件
task_event = TaskEvent(
    timestamp=time.time(),
    task_id=1,
    event_type='pickup_started',
    agv_id=1,
    position=(5, 3),
    details={'priority': 'high', 'weight': 10}
)
```

### 2. 数据管理

```python
# 获取统计信息
stats = collector.get_statistics()
print(f"总回合数: {stats['total_episodes']}")
print(f"总步数: {stats['total_steps']}")
print(f"数据大小: {stats['data_size_mb']:.2f} MB")

# 获取回合列表
episodes = collector.get_episode_list()
for ep in episodes:
    print(f"回合 {ep['episode_id']}: {ep['total_steps']} 步")

# 加载特定回合
episode_data = collector.load_episode_data(episode_id=5)
```

### 3. 可视化定制

```python
# 自定义地图尺寸
visualizer = TrajectoryVisualizer(
    map_width=30, 
    map_height=15, 
    collector=collector
)

# 自定义实时监控
monitor = RealTimeTrajectoryMonitor(
    map_width=30, 
    map_height=15
)
```

## 数据格式

### 轨迹数据文件结构

```json
{
  "metadata": {
    "episode_id": 0,
    "start_time": 1640995200.0,
    "end_time": 1640995260.0,
    "duration": 60.0,
    "total_steps": 100,
    "num_agvs": 3
  },
  "trajectories": {
    "0": [
      {
        "timestamp": 1640995201.0,
        "agv_id": 0,
        "position": [5, 3],
        "status": "MOVING",
        "current_load": 10,
        "target_task": 1,
        "action": "move_up",
        "reward": 0.1
      }
    ]
  },
  "task_events": [...],
  "collision_events": [...]
}
```

## 性能优化

### 1. 数据存储优化

- 使用pickle格式提高读写速度
- 自动清理旧数据文件
- 支持批量数据处理

### 2. 可视化优化

- 轨迹尾迹长度可调
- 动画帧率可控
- 支持大数据量可视化

### 3. 内存管理

- 自动限制最大回合数
- 增量数据更新
- 及时释放图形资源

## 集成示例

### 与训练循环集成

```python
def train_with_visualization():
    # 初始化组件
    collector = TrajectoryCollector(save_dir="./training_trajectories")
    monitor = RealTimeTrajectoryMonitor()
    
    # 启动实时监控
    monitor.start_monitoring("训练过程轨迹监控")
    
    for episode in range(num_episodes):
        # 开始数据收集
        collector.start_episode(episode)
        
        # 训练循环
        for step in range(max_steps):
            # 执行训练步骤
            actions, rewards = agent.step(observations)
            
            # 记录轨迹数据
            collector.record_step(agvs, actions, rewards)
            
            # 实时监控更新
            if step % 10 == 0:  # 每10步更新一次
                monitor.update_agv_positions(agvs, step)
        
        # 结束回合
        collector.end_episode({'episode_reward': total_reward})
        
        # 定期生成分析报告
        if episode % 50 == 0:
            generate_analysis_report(collector, episode)
    
    # 停止监控
    monitor.stop_monitoring()
```

### 性能分析工具

```python
def analyze_training_performance(collector):
    """分析训练性能"""
    episodes = collector.get_episode_list()
    
    for ep_info in episodes[-10:]:  # 分析最近10个回合
        episode_data = collector.load_episode_data(ep_info['episode_id'])
        
        # 生成可视化报告
        visualizer = TrajectoryVisualizer(collector=collector)
        
        # 静态轨迹分析
        visualizer.visualize_static_trajectory(
            episode_data,
            save_path=f"analysis/episode_{ep_info['episode_id']}_static.png"
        )
        
        # 热力图分析
        visualizer.create_trajectory_heatmap(
            episode_data,
            save_path=f"analysis/episode_{ep_info['episode_id']}_heatmap.png"
        )
```

## 故障排除

### 常见问题

1. **中文字体警告**
   - 安装中文字体或使用英文标签
   - 设置matplotlib字体配置

2. **内存不足**
   - 减少max_episodes参数
   - 增加数据清理频率

3. **可视化窗口无响应**
   - 使用非阻塞模式
   - 适当控制更新频率

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查数据完整性
def validate_trajectory_data(episode_data):
    assert 'trajectories' in episode_data
    assert 'metadata' in episode_data
    for agv_id, traj in episode_data['trajectories'].items():
        assert len(traj) > 0
        print(f"AGV {agv_id}: {len(traj)} 轨迹点")
```

## 总结

AGV轨迹可视化系统提供了完整的轨迹数据管理和可视化解决方案，支持：

- ✅ 实时轨迹监控
- ✅ 离线数据分析  
- ✅ 多样化可视化
- ✅ 灵活的数据格式
- ✅ 高性能数据处理
- ✅ 易于集成和扩展

通过本系统，可以深入了解AGV的运行模式、协作效率和系统性能，为算法优化和系统调试提供强有力的支持。
