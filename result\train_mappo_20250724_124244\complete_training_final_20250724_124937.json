{"mode": "single_stage_training", "stage": "stage3", "iterations": 50, "checkpoint_path": {"checkpoint": {"path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppshj_62h", "filesystem": "<pyarrow._fs.LocalFileSystem object at 0x00000287DDBEF6B0>"}, "metrics": {"custom_metrics": {}, "episode_media": {}, "info": {"learner": {"default_policy": {"learner_stats": {"allreduce_latency": 0.0, "grad_gnorm": "10.772286", "cur_kl_coeff": 0.20000000000000004, "cur_lr": 0.0003, "total_loss": 1.1968671815184455, "policy_loss": -0.05969000872905536, "vf_loss": 2.6565012280799207, "vf_explained_var": 0.25539200850712357, "kl": 0.029647322795203615, "entropy": 7.762288806258991, "entropy_coeff": 0.01}, "model": {}, "custom_metrics": {}, "num_agent_steps_trained": 128.0, "num_grad_updates_lifetime": 465.5, "diff_num_grad_updates_vs_sampler_policy": 464.5}}, "num_env_steps_sampled": 4000, "num_env_steps_trained": 4000, "num_agent_steps_sampled": 4000, "num_agent_steps_trained": 4000}, "env_runners": {"episode_reward_max": 99.9999999999986, "episode_reward_min": 99.9999999999986, "episode_reward_mean": 99.9999999999986, "episode_len_mean": 1000.0, "episode_media": {}, "episodes_timesteps_total": 4000, "policy_reward_min": {"default_policy": 99.9999999999986}, "policy_reward_max": {"default_policy": 99.9999999999986}, "policy_reward_mean": {"default_policy": 99.9999999999986}, "custom_metrics": {}, "hist_stats": {"episode_reward": [99.9999999999986, 99.9999999999986, 99.9999999999986, 99.9999999999986], "episode_lengths": [1000, 1000, 1000, 1000], "policy_default_policy_reward": [99.9999999999986, 99.9999999999986, 99.9999999999986, 99.9999999999986]}, "sampler_perf": {"mean_raw_obs_processing_ms": 0.08833551263952112, "mean_inference_ms": 0.8731876815353836, "mean_action_processing_ms": 0.04926064869502446, "mean_env_wait_ms": 0.04861244073995463, "mean_env_render_ms": 0.0}, "num_faulty_episodes": 0, "connector_metrics": {"ObsPreprocessorConnector_ms": 0.0, "StateBufferConnector_ms": 0.0, "ViewRequirementAgentConnector_ms": 0.0}, "num_episodes": 4, "episode_return_max": 99.9999999999986, "episode_return_min": 99.9999999999986, "episode_return_mean": 99.9999999999986, "episodes_this_iter": 4}, "num_healthy_workers": 4, "actor_manager_num_outstanding_async_reqs": 0, "num_remote_worker_restarts": 0, "num_agent_steps_sampled": 4000, "num_agent_steps_trained": 4000, "num_env_steps_sampled": 4000, "num_env_steps_trained": 4000, "num_env_steps_sampled_this_iter": 4000, "num_env_steps_trained_this_iter": 4000, "num_env_steps_sampled_throughput_per_sec": 595.493735399242, "num_env_steps_trained_throughput_per_sec": 595.493735399242, "timesteps_total": 4000, "num_env_steps_sampled_lifetime": 4000, "num_agent_steps_sampled_lifetime": 4000, "num_steps_trained_this_iter": 4000, "agent_timesteps_total": 4000, "timers": {"training_iteration_time_ms": 6717.115, "restore_workers_time_ms": 0.0, "training_step_time_ms": 6717.115, "sample_time_ms": 1074.923, "load_time_ms": 0.0, "load_throughput": 0.0, "learn_time_ms": 5633.457, "learn_throughput": 710.044, "synch_weights_time_ms": 6.733}, "counters": {"num_env_steps_sampled": 4000, "num_env_steps_trained": 4000, "num_agent_steps_sampled": 4000, "num_agent_steps_trained": 4000}, "done": false, "training_iteration": 1, "trial_id": "default", "date": "2025-07-24_12-42-58", "timestamp": 1753332178, "time_this_iter_s": 6.718119382858276, "time_total_s": 6.718119382858276, "pid": 23620, "hostname": "DESKTOP-HC2JCIA", "node_ip": "127.0.0.1", "config": {"exploration_config": {"type": "StochasticSampling"}, "extra_python_environs_for_driver": {}, "extra_python_environs_for_worker": {}, "placement_strategy": "PACK", "num_gpus": 0, "_fake_gpus": false, "num_cpus_for_main_process": 1, "eager_tracing": true, "eager_max_retraces": 20, "tf_session_args": {"intra_op_parallelism_threads": 2, "inter_op_parallelism_threads": 2, "gpu_options": {"allow_growth": true}, "log_device_placement": false, "device_count": {"CPU": 1}, "allow_soft_placement": true}, "local_tf_session_args": {"intra_op_parallelism_threads": 8, "inter_op_parallelism_threads": 8}, "torch_compile_learner": false, "torch_compile_learner_what_to_compile": {}, "torch_compile_learner_dynamo_backend": "inductor", "torch_compile_learner_dynamo_mode": null, "torch_compile_worker": false, "torch_compile_worker_dynamo_backend": "onnxrt", "torch_compile_worker_dynamo_mode": null, "torch_ddp_kwargs": {}, "torch_skip_nan_gradients": false, "env": "agv_warehouse", "env_config": {"num_agvs": 4, "num_tasks": 16, "max_episode_steps": 1000, "map_name": "warehouse_stage3", "curriculum_stage": "stage3"}, "observation_space": null, "action_space": null, "clip_rewards": null, "normalize_actions": true, "clip_actions": false, "_is_atari": null, "disable_env_checking": false, "render_env": false, "action_mask_key": "action_mask", "env_runner_cls": null, "num_env_runners": 4, "create_local_env_runner": true, "num_envs_per_env_runner": 1, "gym_env_vectorize_mode": "SYNC", "num_cpus_per_env_runner": 1, "num_gpus_per_env_runner": 0, "custom_resources_per_env_runner": {}, "validate_env_runners_after_construction": true, "episodes_to_numpy": true, "max_requests_in_flight_per_env_runner": 1, "sample_timeout_s": 60.0, "_env_to_module_connector": null, "add_default_connectors_to_env_to_module_pipeline": true, "_module_to_env_connector": null, "add_default_connectors_to_module_to_env_pipeline": true, "merge_env_runner_states": "training_only", "broadcast_env_runner_states": true, "episode_lookback_horizon": 1, "rollout_fragment_length": 200, "batch_mode": "truncate_episodes", "compress_observations": false, "remote_worker_envs": false, "remote_env_batch_wait_ms": 0, "enable_tf1_exec_eagerly": false, "sample_collector": {}, "preprocessor_pref": "deepmind", "observation_filter": "NoFilter", "update_worker_filter_stats": true, "use_worker_filter_stats": true, "sampler_perf_stats_ema_coef": null, "_is_online": true, "num_learners": 0, "num_gpus_per_learner": 0, "num_cpus_per_learner": "auto", "num_aggregator_actors_per_learner": 0, "max_requests_in_flight_per_aggregator_actor": 3, "local_gpu_idx": 0, "max_requests_in_flight_per_learner": 3, "gamma": 0.99, "lr": 0.0003, "grad_clip": null, "grad_clip_by": "global_norm", "_train_batch_size_per_learner": null, "train_batch_size": 4000, "num_epochs": 30, "minibatch_size": 128, "shuffle_batch_per_epoch": true, "model": {"fcnet_hiddens": [256, 256], "fcnet_activation": "tanh", "fcnet_weights_initializer": null, "fcnet_weights_initializer_config": null, "fcnet_bias_initializer": null, "fcnet_bias_initializer_config": null, "conv_filters": null, "conv_activation": "relu", "conv_kernel_initializer": null, "conv_kernel_initializer_config": null, "conv_bias_initializer": null, "conv_bias_initializer_config": null, "conv_transpose_kernel_initializer": null, "conv_transpose_kernel_initializer_config": null, "conv_transpose_bias_initializer": null, "conv_transpose_bias_initializer_config": null, "post_fcnet_hiddens": [], "post_fcnet_activation": "relu", "post_fcnet_weights_initializer": null, "post_fcnet_weights_initializer_config": null, "post_fcnet_bias_initializer": null, "post_fcnet_bias_initializer_config": null, "free_log_std": false, "log_std_clip_param": 20.0, "no_final_linear": false, "vf_share_layers": false, "use_lstm": false, "max_seq_len": 20, "lstm_cell_size": 256, "lstm_use_prev_action": false, "lstm_use_prev_reward": false, "lstm_weights_initializer": null, "lstm_weights_initializer_config": null, "lstm_bias_initializer": null, "lstm_bias_initializer_config": null, "_time_major": false, "use_attention": false, "attention_num_transformer_units": 1, "attention_dim": 64, "attention_num_heads": 1, "attention_head_dim": 32, "attention_memory_inference": 50, "attention_memory_training": 50, "attention_position_wise_mlp_dim": 32, "attention_init_gru_gate_bias": 2.0, "attention_use_n_prev_actions": 0, "attention_use_n_prev_rewards": 0, "framestack": true, "dim": 84, "grayscale": false, "zero_mean": true, "custom_model": "attention_enhanced_mappo", "custom_model_config": {"hidden_dim": 256, "feature_dim": 64, "num_heads": 8, "dropout": 0.1}, "custom_action_dist": null, "custom_preprocessor": null, "encoder_latent_dim": null, "always_check_shapes": false, "lstm_use_prev_action_reward": -1, "_use_default_native_models": -1, "_disable_preprocessor_api": false, "_disable_action_flattening": false}, "_learner_connector": null, "add_default_connectors_to_learner_pipeline": true, "learner_config_dict": {}, "optimizer": {}, "_learner_class": null, "callbacks_on_algorithm_init": null, "callbacks_on_env_runners_recreated": null, "callbacks_on_offline_eval_runners_recreated": null, "callbacks_on_checkpoint_loaded": null, "callbacks_on_environment_created": null, "callbacks_on_episode_created": null, "callbacks_on_episode_start": null, "callbacks_on_episode_step": null, "callbacks_on_episode_end": null, "callbacks_on_evaluate_start": null, "callbacks_on_evaluate_end": null, "callbacks_on_evaluate_offline_start": null, "callbacks_on_evaluate_offline_end": null, "callbacks_on_sample_end": null, "callbacks_on_train_result": null, "explore": true, "enable_rl_module_and_learner": false, "enable_env_runner_and_connector_v2": false, "_prior_exploration_config": null, "count_steps_by": "env_steps", "policy_map_capacity": 100, "policy_mapping_fn": {}, "policies_to_train": ["default_policy"], "policy_states_are_swappable": false, "observation_fn": null, "offline_data_class": null, "input_read_method": "read_parquet", "input_read_method_kwargs": {}, "input_read_schema": {}, "input_read_episodes": false, "input_read_sample_batches": false, "input_read_batch_size": null, "input_filesystem": null, "input_filesystem_kwargs": {}, "input_compress_columns": ["obs", "new_obs"], "input_spaces_jsonable": true, "materialize_data": false, "materialize_mapped_data": true, "map_batches_kwargs": {}, "iter_batches_kwargs": {}, "ignore_final_observation": false, "prelearner_class": null, "prelearner_buffer_class": null, "prelearner_buffer_kwargs": {}, "prelearner_module_synch_period": 10, "dataset_num_iters_per_learner": null, "input_config": {}, "actions_in_input_normalized": false, "postprocess_inputs": false, "shuffle_buffer_size": 0, "output": null, "output_config": {}, "output_compress_columns": ["obs", "new_obs"], "output_max_file_size": 67108864, "output_max_rows_per_file": null, "output_write_remaining_data": false, "output_write_method": "write_parquet", "output_write_method_kwargs": {}, "output_filesystem": null, "output_filesystem_kwargs": {}, "output_write_episodes": true, "offline_sampling": false, "evaluation_interval": 10, "evaluation_duration": 10, "evaluation_duration_unit": "episodes", "evaluation_sample_timeout_s": 120.0, "evaluation_auto_duration_min_env_steps_per_sample": 100, "evaluation_auto_duration_max_env_steps_per_sample": 2000, "evaluation_parallel_to_training": false, "evaluation_force_reset_envs_before_iteration": true, "evaluation_config": {"explore": false, "env_config": {"num_agvs": 4, "num_tasks": 16, "max_episode_steps": 1000, "map_name": "warehouse_stage3", "curriculum_stage": "stage3"}}, "off_policy_estimation_methods": {}, "ope_split_batch_by_episode": true, "evaluation_num_env_runners": 0, "in_evaluation": false, "sync_filters_on_rollout_workers_timeout_s": 10.0, "offline_evaluation_interval": null, "num_offline_eval_runners": 0, "offline_evaluation_type": null, "offline_eval_runner_class": null, "offline_loss_for_module_fn": null, "offline_evaluation_duration": 1, "offline_evaluation_parallel_to_training": false, "offline_evaluation_timeout_s": 120.0, "num_cpus_per_offline_eval_runner": 1, "num_gpus_per_offline_eval_runner": 0, "custom_resources_per_offline_eval_runner": {}, "restart_failed_offline_eval_runners": true, "ignore_offline_eval_runner_failures": false, "max_num_offline_eval_runner_restarts": 1000, "offline_eval_runner_restore_timeout_s": 1800.0, "max_requests_in_flight_per_offline_eval_runner": 1, "validate_offline_eval_runners_after_construction": true, "offline_eval_runner_health_probe_timeout_s": 30.0, "offline_eval_rl_module_inference_only": false, "broadcast_offline_eval_runner_states": false, "offline_eval_batch_size_per_runner": 256, "dataset_num_iters_per_eval_runner": 1, "keep_per_episode_custom_metrics": false, "metrics_episode_collection_timeout_s": 60.0, "metrics_num_episodes_for_smoothing": 100, "min_time_s_per_iteration": null, "min_train_timesteps_per_iteration": 0, "min_sample_timesteps_per_iteration": 0, "log_gradients": true, "export_native_model_files": false, "checkpoint_trainable_policies_only": false, "logger_creator": null, "logger_config": null, "log_level": "INFO", "log_sys_usage": true, "fake_sampler": false, "seed": null, "restart_failed_env_runners": true, "ignore_env_runner_failures": false, "max_num_env_runner_restarts": 1000, "delay_between_env_runner_restarts_s": 60.0, "restart_failed_sub_environments": false, "num_consecutive_env_runner_failures_tolerance": 100, "env_runner_health_probe_timeout_s": 30.0, "env_runner_restore_timeout_s": 1800.0, "_model_config": {}, "_rl_module_spec": null, "algorithm_config_overrides_per_module": {}, "_per_module_overrides": {}, "_validate_config": true, "_use_msgpack_checkpoints": false, "_torch_grad_scaler_class": null, "_torch_lr_scheduler_classes": null, "_tf_policy_handles_more_than_one_loss": false, "_disable_preprocessor_api": false, "_disable_action_flattening": false, "_disable_initialize_loss_from_dummy_batch": false, "_dont_auto_sync_env_runner_states": false, "env_task_fn": -1, "enable_connectors": -1, "simple_optimizer": false, "policy_map_cache": -1, "worker_cls": -1, "synchronize_filters": -1, "enable_async_evaluation": -1, "custom_async_evaluation_function": -1, "_enable_rl_module_api": false, "auto_wrap_old_gym_envs": -1, "always_attach_evaluation_results": -1, "replay_sequence_length": null, "_disable_execution_plan_api": -1, "use_critic": true, "use_gae": true, "use_kl_loss": true, "kl_coeff": 0.2, "kl_target": 0.01, "vf_loss_coeff": 0.5, "entropy_coeff": 0.01, "clip_param": 0.2, "vf_clip_param": 10.0, "entropy_coeff_schedule": null, "lr_schedule": null, "sgd_minibatch_size": 64, "vf_share_layers": -1, "num_sgd_iter": 10, "_enable_learner_api": false, "custom_config": {"attention_loss_coeff": 0.1, "attention_regularization": true}, "checkpoint_freq": 50, "keep_checkpoints_num": 5, "class": {}, "lambda": 1.0, "input": "sampler", "policies": {"default_policy": [null, null, null, null]}, "callbacks": {}, "create_env_on_driver": false, "custom_eval_function": null, "framework": "torch"}, "time_since_restore": 6.718119382858276, "iterations_since_restore": 1, "perf": {"cpu_util_percent": 40.87, "ram_util_percent": 13.98}}}, "config": {"learning_rate": 0.0003, "batch_size": 256, "num_epochs": 10, "clip_param": 0.2, "value_loss_coeff": 0.5, "entropy_coeff": 0.01, "attention_loss_coeff": 0.1, "attention_regularization": true, "model_config": {"hidden_dim": 256, "feature_dim": 64, "num_heads": 8, "dropout": 0.1}, "num_workers": 4, "num_envs_per_worker": 1, "rollout_fragment_length": 200, "train_batch_size": 4000, "evaluation_interval": 10, "evaluation_num_episodes": 10, "checkpoint_freq": 50, "keep_checkpoints_num": 5, "max_timesteps": 1000000, "target_reward": 1000, "output_dir": "./result\\train_mappo_20250724_124244"}, "timestamp": "2025-07-24T12:49:37.794660"}