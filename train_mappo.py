"""
基于MARLlib的MAPPO训练脚本
使用双层注意力机制训练多AGV协同调度系统
"""

import os
import sys
import argparse
import json
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.mappo.marllib_integration import MARLlibAGVTrainer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="MAPPO训练脚本")
    
    # 训练模式
    parser.add_argument("--mode", type=str, default="single", 
                       choices=["single", "curriculum", "evaluate"],
                       help="训练模式: single(单阶段), curriculum(课程学习), evaluate(评估)")
    
    # 训练参数
    parser.add_argument("--stage", type=str, default="stage3",
                       choices=["stage1", "stage2", "stage3"],
                       help="训练阶段")
    parser.add_argument("--iterations", type=int, default=1000,
                       help="训练迭代次数")
    parser.add_argument("--num_workers", type=int, default=4,
                       help="并行工作进程数")
    
    # 模型参数
    parser.add_argument("--hidden_dim", type=int, default=256,
                       help="隐藏层维度")
    parser.add_argument("--feature_dim", type=int, default=64,
                       help="特征维度")
    parser.add_argument("--num_heads", type=int, default=8,
                       help="注意力头数")
    parser.add_argument("--learning_rate", type=float, default=3e-4,
                       help="学习率")
    
    # 文件路径
    parser.add_argument("--checkpoint", type=str, default=None,
                       help="检查点路径（用于恢复训练或评估）")
    parser.add_argument("--output_dir", type=str, default="./result",
                       help="输出目录")
    parser.add_argument("--experiment_name", type=str, default=None,
                       help="实验名称")
    
    # 评估参数
    parser.add_argument("--eval_episodes", type=int, default=10,
                       help="评估回合数")
    parser.add_argument("--render", action="store_true",
                       help="评估时是否渲染")
    
    # 课程学习参数
    parser.add_argument("--curriculum_stages", type=str, nargs="+",
                       default=["stage1", "stage2", "stage3"],
                       help="课程学习阶段列表")
    parser.add_argument("--iterations_per_stage", type=int, default=300,
                       help="每个课程阶段的迭代次数")

    # 可视化参数
    parser.add_argument("--enable_visualization", action="store_true", default=True,
                       help="启用实时可视化功能")
    parser.add_argument("--disable_visualization", action="store_true",
                       help="禁用实时可视化功能")

    return parser.parse_args()


def create_training_config(args) -> Dict[str, Any]:
    """
    创建训练配置
    
    Args:
        args: 命令行参数
        
    Returns:
        config: 训练配置字典
    """
    config = {
        # 训练参数
        "learning_rate": args.learning_rate,
        "batch_size": 256,
        "num_epochs": 10,
        "clip_param": 0.2,
        "value_loss_coeff": 0.5,
        "entropy_coeff": 0.01,
        
        # 注意力机制参数
        "attention_loss_coeff": 0.1,
        "attention_regularization": True,
        
        # 模型参数
        "model_config": {
            "hidden_dim": args.hidden_dim,
            "feature_dim": args.feature_dim,
            "num_heads": args.num_heads,
            "dropout": 0.1
        },
        
        # 并行训练参数
        "num_workers": args.num_workers,
        "num_envs_per_worker": 1,  # 保持原参数名，在marllib_integration中会转换
        "rollout_fragment_length": 200,
        "train_batch_size": 4000,
        
        # 评估参数
        "evaluation_interval": 10,
        "evaluation_num_episodes": 10,
        
        # 检查点参数
        "checkpoint_freq": 50,
        "keep_checkpoints_num": 5,
        
        # 停止条件
        "max_timesteps": 1000000,
        "target_reward": 1000
    }
    
    return config


def single_stage_training(trainer: MARLlibAGVTrainer, args) -> str:
    """
    单阶段训练
    
    Args:
        trainer: 训练器
        args: 命令行参数
        
    Returns:
        checkpoint_path: 检查点路径
    """
    print(f"\n{'='*60}")
    print(f"开始单阶段训练")
    print(f"阶段: {args.stage}")
    print(f"迭代次数: {args.iterations}")
    print(f"{'='*60}")
    
    # 确定是否启用可视化
    enable_viz = args.enable_visualization and not args.disable_visualization

    checkpoint_path = trainer.train(
        stage=args.stage,
        num_iterations=args.iterations,
        checkpoint_path=args.checkpoint,
        experiment_name=args.experiment_name,
        enable_visualization=enable_viz
    )
    
    print(f"\n✅ 单阶段训练完成!")
    print(f"检查点路径: {checkpoint_path}")
    
    return checkpoint_path


def curriculum_training(trainer: MARLlibAGVTrainer, args) -> Dict[str, str]:
    """
    课程学习训练
    
    Args:
        trainer: 训练器
        args: 命令行参数
        
    Returns:
        checkpoints: 各阶段检查点路径
    """
    print(f"\n{'='*60}")
    print(f"开始课程学习训练")
    print(f"阶段: {args.curriculum_stages}")
    print(f"每阶段迭代次数: {args.iterations_per_stage}")
    print(f"{'='*60}")
    
    checkpoints = trainer.curriculum_training(
        stages=args.curriculum_stages,
        iterations_per_stage=args.iterations_per_stage
    )
    
    print(f"\n✅ 课程学习训练完成!")
    print("各阶段检查点:")
    for stage, checkpoint in checkpoints.items():
        print(f"  {stage}: {checkpoint}")
    
    return checkpoints


def evaluate_model(trainer: MARLlibAGVTrainer, args) -> Dict[str, float]:
    """
    评估模型
    
    Args:
        trainer: 训练器
        args: 命令行参数
        
    Returns:
        results: 评估结果
    """
    if not args.checkpoint:
        raise ValueError("评估模式需要提供检查点路径 --checkpoint")
    
    print(f"\n{'='*60}")
    print(f"开始模型评估")
    print(f"检查点: {args.checkpoint}")
    print(f"评估回合数: {args.eval_episodes}")
    print(f"{'='*60}")
    
    results = trainer.evaluate(
        checkpoint_path=args.checkpoint,
        num_episodes=args.eval_episodes,
        render=args.render
    )
    
    print(f"\n✅ 模型评估完成!")
    return results


def save_results(results: Dict[str, Any], output_dir: str, experiment_name: str):
    """
    保存结果

    Args:
        results: 结果字典
        output_dir: 输出目录
        experiment_name: 实验名称
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{experiment_name}_{timestamp}.json"
    filepath = os.path.join(output_dir, filename)

    # 转换结果为可序列化的格式
    def make_serializable(obj):
        """递归转换对象为JSON可序列化格式"""
        if hasattr(obj, '__dict__'):
            # 对于有__dict__的对象，转换为字典
            return {k: make_serializable(v) for k, v in obj.__dict__.items()
                   if not k.startswith('_') and not callable(v)}
        elif hasattr(obj, 'metrics') and hasattr(obj, 'checkpoint'):
            # 对于TrainingResult对象，提取关键信息
            return {
                'metrics': make_serializable(obj.metrics),
                'checkpoint_path': str(obj.checkpoint.path) if obj.checkpoint else None
            }
        elif isinstance(obj, dict):
            return {k: make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [make_serializable(item) for item in obj]
        elif isinstance(obj, (int, float, str, bool, type(None))):
            return obj
        else:
            # 对于其他类型，尝试转换为字符串
            return str(obj)

    serializable_results = make_serializable(results)

    # 保存结果
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)

    print(f"✓ 结果已保存: {filepath}")


def main():
    """主函数"""
    args = parse_args()

    # 生成基于脚本名称和日期的输出目录
    script_name = os.path.splitext(os.path.basename(__file__))[0]  # train_mappo
    current_date = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_dir = f"{script_name}_{current_date}"

    # 设置完整的输出路径：./results/train_mappo_20250723_230000
    full_output_dir = os.path.join(args.output_dir, experiment_dir)
    os.makedirs(full_output_dir, exist_ok=True)

    print("=" * 80)
    print("基于MARLlib的MAPPO多AGV协同调度系统训练")
    print("=" * 80)
    print(f"训练模式: {args.mode}")
    print(f"输出目录: {full_output_dir}")
    print(f"实验名称: {experiment_dir}")

    # 创建训练配置
    config = create_training_config(args)

    # 更新配置中的输出目录
    config["output_dir"] = full_output_dir
    
    # 创建训练器
    trainer = MARLlibAGVTrainer(config)
    
    try:
        # 根据模式执行不同的操作
        if args.mode == "single":
            # 单阶段训练
            checkpoint_path = single_stage_training(trainer, args)
            
            # 保存结果
            results = {
                "mode": "single_stage_training",
                "stage": args.stage,
                "iterations": args.iterations,
                "checkpoint_path": checkpoint_path,
                "config": config,
                "timestamp": datetime.now().isoformat()
            }
            
            experiment_name = args.experiment_name or f"single_{args.stage}"
            save_results(results, full_output_dir, experiment_name)
            
        elif args.mode == "curriculum":
            # 课程学习训练
            checkpoints = curriculum_training(trainer, args)
            
            # 保存结果
            results = {
                "mode": "curriculum_training",
                "stages": args.curriculum_stages,
                "iterations_per_stage": args.iterations_per_stage,
                "checkpoints": checkpoints,
                "config": config,
                "timestamp": datetime.now().isoformat()
            }
            
            experiment_name = args.experiment_name or "curriculum"
            save_results(results, full_output_dir, experiment_name)

        elif args.mode == "evaluate":
            # 模型评估
            eval_results = evaluate_model(trainer, args)

            # 保存结果
            results = {
                "mode": "evaluation",
                "checkpoint_path": args.checkpoint,
                "eval_episodes": args.eval_episodes,
                "evaluation_results": eval_results,
                "timestamp": datetime.now().isoformat()
            }

            experiment_name = args.experiment_name or "evaluation"
            save_results(results, full_output_dir, experiment_name)
        
        print(f"\n🎉 {args.mode} 模式执行完成!")
        
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
        raise
    
    finally:
        # 关闭训练器
        trainer.close()


if __name__ == "__main__":
    main()
