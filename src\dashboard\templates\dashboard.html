<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AGV训练监控仪表板</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .metric-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #495057;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-training { background-color: #28a745; }
        .status-paused { background-color: #ffc107; }
        .status-stopped { background-color: #dc3545; }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
            height: 400px;
        }
        
        .control-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .btn-control {
            margin: 0.25rem;
            min-width: 100px;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .attention-heatmap {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 1rem;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid #4a5568;
        }
        
        .log-timestamp {
            color: #a0aec0;
            margin-right: 1rem;
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
    </style>
</head>
<body>
    <!-- 连接状态指示器 -->
    <div class="connection-status">
        <span class="badge bg-success" id="connectionStatus">
            <i class="fas fa-wifi"></i> 已连接
        </span>
    </div>

    <!-- 仪表板头部 -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-robot"></i>
                        AGV训练监控仪表板
                    </h1>
                    <p class="mb-0 opacity-75">基于双层注意力机制的MAPPO多AGV协同调度系统</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="status-indicator" id="trainingStatusIndicator"></span>
                        <span id="trainingStatusText">未知状态</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 控制面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="control-panel">
                    <h5 class="mb-3">
                        <i class="fas fa-gamepad"></i>
                        训练控制
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-success btn-control" onclick="startTraining()">
                                <i class="fas fa-play"></i> 开始训练
                            </button>
                            <button class="btn btn-warning btn-control" onclick="pauseTraining()">
                                <i class="fas fa-pause"></i> 暂停训练
                            </button>
                            <button class="btn btn-danger btn-control" onclick="stopTraining()">
                                <i class="fas fa-stop"></i> 停止训练
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-info btn-control" onclick="exportData()">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                            <button class="btn btn-secondary btn-control" onclick="resetMetrics()">
                                <i class="fas fa-refresh"></i> 重置指标
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关键指标卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="currentEpisode">0</div>
                    <div class="metric-label">当前回合</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="averageReward">0.00</div>
                    <div class="metric-label">平均奖励</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="successRate">0%</div>
                    <div class="metric-label">成功率</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="elapsedTime">00:00:00</div>
                    <div class="metric-label">训练时间</div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row mb-4">
            <!-- 奖励曲线 -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h6 class="mb-3">
                        <i class="fas fa-chart-line"></i>
                        奖励曲线
                    </h6>
                    <canvas id="rewardChart"></canvas>
                </div>
            </div>
            
            <!-- 损失曲线 -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h6 class="mb-3">
                        <i class="fas fa-chart-area"></i>
                        损失曲线
                    </h6>
                    <canvas id="lossChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 注意力可视化 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="attention-heatmap">
                    <h6 class="mb-3">
                        <i class="fas fa-eye"></i>
                        任务分配注意力热图
                    </h6>
                    <div id="taskAttentionHeatmap"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="attention-heatmap">
                    <h6 class="mb-3">
                        <i class="fas fa-project-diagram"></i>
                        协作感知注意力网络
                    </h6>
                    <div id="collaborationAttentionNetwork"></div>
                </div>
            </div>
        </div>

        <!-- 系统日志 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="metric-card">
                    <h6 class="mb-3">
                        <i class="fas fa-terminal"></i>
                        系统日志
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearLogs()">
                            <i class="fas fa-trash"></i> 清空
                        </button>
                    </h6>
                    <div class="log-container" id="systemLogs">
                        <div class="log-entry">
                            <span class="log-timestamp">[2024-01-01 00:00:00]</span>
                            <span>系统初始化完成</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script src="/static/dashboard.js"></script>
</body>
</html>
