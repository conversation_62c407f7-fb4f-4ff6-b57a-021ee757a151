"""
注意力机制模块
包含双层注意力机制的实现
"""

from .task_allocation_attention import (
    AttentionOutput,
    PositionalEncoding,
    ConstraintFusion,
    MultiHeadTaskAllocationAttention,
    TaskAllocationAttentionManager
)

from .collaboration_attention import (
    CollaborationOutput,
    RelativePositionalEncoding,
    AdaptiveTemperature,
    HierarchicalCollaborationAttention,
    CollaborationAttentionManager
)

from .dual_layer_fusion import (
    DualLayerOutput,
    GatedFusion,
    ResidualConnection,
    TemporalConsistency,
    AttentionEnhancedFeatureExtractor,
    DualLayerAttentionFusion
)

__all__ = [
    # 第一层任务分配注意力
    'AttentionOutput',
    'PositionalEncoding',
    'ConstraintFusion',
    'MultiHeadTaskAllocationAttention',
    'TaskAllocationAttentionManager',

    # 第二层协作感知注意力
    'CollaborationOutput',
    'RelativePositionalEncoding',
    'AdaptiveTemperature',
    'HierarchicalCollaborationAttention',
    'CollaborationAttentionManager',

    # 双层注意力融合
    'DualLayerOutput',
    'GatedFusion',
    'ResidualConnection',
    'TemporalConsistency',
    'AttentionEnhancedFeatureExtractor',
    'DualLayerAttentionFusion'
]