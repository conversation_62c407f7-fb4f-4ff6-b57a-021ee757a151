"""
性能监控与可视化系统
提供训练过程的实时监控、指标收集和可视化功能
"""

from .metrics_collector import (
    MetricsCollector,
    TrainingMetrics,
    AttentionMetrics,
    SystemMetrics
)

from .basic_visualizer import BasicVisualizer

from .training_monitor import TrainingMonitor

__all__ = [
    'MetricsCollector',
    'TrainingMetrics',
    'AttentionMetrics',
    'SystemMetrics',
    'BasicVisualizer',
    'TrainingMonitor'
]

# 版本信息
__version__ = "1.0.0"

# 模块信息
__description__ = "基于融合双层注意力机制的MAPPO多AGV协同调度系统 - 性能监控与可视化模块"
__author__ = "AGV Research Team"
