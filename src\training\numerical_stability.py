"""
数值稳定性保证组件
确保训练过程中的数值计算稳定性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass


@dataclass
class NumericalConfig:
    """数值稳定性配置"""
    eps: float = 1e-8
    max_value: float = 1e6
    min_value: float = -1e6
    attention_temperature_min: float = 0.1
    attention_temperature_max: float = 10.0
    softmax_temperature: float = 1.0
    use_mixed_precision: bool = False
    gradient_scaling: bool = True


class StableSoftmax(nn.Module):
    """数值稳定的Softmax"""
    
    def __init__(self, dim: int = -1, temperature: float = 1.0, eps: float = 1e-8):
        """
        初始化稳定Softmax
        
        Args:
            dim: 计算维度
            temperature: 温度参数
            eps: 数值稳定性常数
        """
        super().__init__()
        self.dim = dim
        self.temperature = temperature
        self.eps = eps
    
    def forward(self, x: torch.Tensor, temperature: Optional[float] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量
            temperature: 可选的温度参数
            
        Returns:
            output: Softmax输出
        """
        temp = temperature if temperature is not None else self.temperature
        
        # 应用温度
        x = x / temp
        
        # 数值稳定的softmax计算
        # 减去最大值防止溢出
        x_max = torch.max(x, dim=self.dim, keepdim=True)[0]
        x_shifted = x - x_max
        
        # 计算指数
        exp_x = torch.exp(torch.clamp(x_shifted, min=-50, max=50))  # 防止极值
        
        # 计算softmax
        sum_exp = torch.sum(exp_x, dim=self.dim, keepdim=True)
        softmax_output = exp_x / (sum_exp + self.eps)
        
        return softmax_output


class StableLayerNorm(nn.Module):
    """数值稳定的层归一化"""
    
    def __init__(self, normalized_shape: int, eps: float = 1e-8):
        """
        初始化稳定层归一化
        
        Args:
            normalized_shape: 归一化形状
            eps: 数值稳定性常数
        """
        super().__init__()
        self.normalized_shape = normalized_shape
        self.eps = eps
        
        # 可学习参数
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量
            
        Returns:
            output: 归一化输出
        """
        # 计算均值和方差
        mean = x.mean(dim=-1, keepdim=True)
        var = x.var(dim=-1, keepdim=True, unbiased=False)
        
        # 数值稳定的归一化
        std = torch.sqrt(var + self.eps)
        normalized = (x - mean) / std
        
        # 应用可学习参数
        output = self.weight * normalized + self.bias
        
        return output


class StableAttention(nn.Module):
    """数值稳定的注意力机制"""
    
    def __init__(self, 
                 feature_dim: int,
                 num_heads: int = 8,
                 dropout: float = 0.1,
                 temperature_min: float = 0.1,
                 temperature_max: float = 10.0):
        """
        初始化稳定注意力机制
        
        Args:
            feature_dim: 特征维度
            num_heads: 注意力头数
            dropout: Dropout比率
            temperature_min: 最小温度
            temperature_max: 最大温度
        """
        super().__init__()
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.head_dim = feature_dim // num_heads
        self.temperature_min = temperature_min
        self.temperature_max = temperature_max
        
        assert feature_dim % num_heads == 0, "feature_dim must be divisible by num_heads"
        
        # 线性变换
        self.query = nn.Linear(feature_dim, feature_dim)
        self.key = nn.Linear(feature_dim, feature_dim)
        self.value = nn.Linear(feature_dim, feature_dim)
        self.output = nn.Linear(feature_dim, feature_dim)
        
        # 稳定组件
        self.stable_softmax = StableSoftmax(dim=-1)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = StableLayerNorm(feature_dim)
        
        # 温度参数
        self.temperature = nn.Parameter(torch.ones(1))
        
    def forward(self, 
                query: torch.Tensor,
                key: torch.Tensor,
                value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            query: 查询张量
            key: 键张量
            value: 值张量
            mask: 注意力掩码
            
        Returns:
            output: 注意力输出
            attention_weights: 注意力权重
        """
        batch_size, seq_len, _ = query.shape
        
        # 线性变换
        Q = self.query(query)
        K = self.key(key)
        V = self.value(value)
        
        # 重塑为多头
        Q = Q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1))
        
        # 缩放
        scale = math.sqrt(self.head_dim)
        scores = scores / scale
        
        # 应用温度（限制范围）
        temperature = torch.clamp(self.temperature, self.temperature_min, self.temperature_max)
        scores = scores / temperature
        
        # 应用掩码
        if mask is not None:
            mask = mask.unsqueeze(1).unsqueeze(1)  # 广播到多头
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 稳定softmax
        attention_weights = self.stable_softmax(scores)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力
        attended = torch.matmul(attention_weights, V)
        
        # 重塑输出
        attended = attended.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.feature_dim
        )
        
        # 输出投影
        output = self.output(attended)
        
        # 残差连接和层归一化
        output = self.layer_norm(output + query)
        
        return output, attention_weights.mean(dim=1)  # 平均多头权重


class NumericalStabilizer:
    """数值稳定化器"""
    
    def __init__(self, config: NumericalConfig):
        """
        初始化数值稳定化器
        
        Args:
            config: 数值稳定性配置
        """
        self.config = config
        
        # 混合精度训练
        if config.use_mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler(enabled=config.gradient_scaling)
        else:
            self.scaler = None
    
    def stabilize_tensor(self, tensor: torch.Tensor, name: str = "") -> torch.Tensor:
        """
        稳定化张量
        
        Args:
            tensor: 输入张量
            name: 张量名称（用于日志）
            
        Returns:
            stabilized_tensor: 稳定化后的张量
        """
        # 检查NaN和Inf
        if torch.isnan(tensor).any() or torch.isinf(tensor).any():
            print(f"Warning: NaN/Inf detected in tensor {name}")
            tensor = torch.where(torch.isnan(tensor), torch.zeros_like(tensor), tensor)
            tensor = torch.where(torch.isinf(tensor), torch.sign(tensor) * self.config.max_value, tensor)
        
        # 限制数值范围
        tensor = torch.clamp(tensor, self.config.min_value, self.config.max_value)
        
        return tensor
    
    def stabilize_gradients(self, model: nn.Module) -> Dict[str, float]:
        """
        稳定化梯度
        
        Args:
            model: 模型
            
        Returns:
            grad_stats: 梯度统计信息
        """
        total_norm = 0.0
        nan_count = 0
        inf_count = 0
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                # 检查NaN和Inf
                nan_mask = torch.isnan(param.grad)
                inf_mask = torch.isinf(param.grad)
                
                if nan_mask.any():
                    nan_count += nan_mask.sum().item()
                    param.grad[nan_mask] = 0.0
                
                if inf_mask.any():
                    inf_count += inf_mask.sum().item()
                    param.grad[inf_mask] = torch.sign(param.grad[inf_mask]) * self.config.max_value
                
                # 限制梯度范围
                param.grad = torch.clamp(param.grad, self.config.min_value, self.config.max_value)
                
                # 计算梯度范数
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        
        total_norm = total_norm ** (1. / 2)
        
        return {
            'total_norm': total_norm,
            'nan_count': nan_count,
            'inf_count': inf_count
        }
    
    def stabilize_attention_weights(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """
        稳定化注意力权重
        
        Args:
            attention_weights: 注意力权重
            
        Returns:
            stabilized_weights: 稳定化后的权重
        """
        # 确保权重非负
        attention_weights = torch.clamp(attention_weights, min=0.0)
        
        # 重新归一化
        row_sums = attention_weights.sum(dim=-1, keepdim=True)
        attention_weights = attention_weights / (row_sums + self.config.eps)
        
        # 检查归一化结果
        attention_weights = self.stabilize_tensor(attention_weights, "attention_weights")
        
        return attention_weights
    
    def safe_log(self, x: torch.Tensor) -> torch.Tensor:
        """
        安全的对数计算
        
        Args:
            x: 输入张量
            
        Returns:
            log_x: 对数结果
        """
        return torch.log(torch.clamp(x, min=self.config.eps))
    
    def safe_sqrt(self, x: torch.Tensor) -> torch.Tensor:
        """
        安全的平方根计算
        
        Args:
            x: 输入张量
            
        Returns:
            sqrt_x: 平方根结果
        """
        return torch.sqrt(torch.clamp(x, min=self.config.eps))
    
    def safe_division(self, numerator: torch.Tensor, denominator: torch.Tensor) -> torch.Tensor:
        """
        安全的除法计算
        
        Args:
            numerator: 分子
            denominator: 分母
            
        Returns:
            result: 除法结果
        """
        return numerator / (denominator + self.config.eps)
    
    def check_model_health(self, model: nn.Module) -> Dict[str, Any]:
        """
        检查模型健康状态
        
        Args:
            model: 模型
            
        Returns:
            health_report: 健康报告
        """
        health_report = {
            'total_parameters': 0,
            'nan_parameters': 0,
            'inf_parameters': 0,
            'large_parameters': 0,
            'parameter_stats': {}
        }
        
        for name, param in model.named_parameters():
            param_data = param.data
            
            # 统计参数
            health_report['total_parameters'] += param_data.numel()
            
            # 检查异常值
            nan_count = torch.isnan(param_data).sum().item()
            inf_count = torch.isinf(param_data).sum().item()
            large_count = (torch.abs(param_data) > self.config.max_value).sum().item()
            
            health_report['nan_parameters'] += nan_count
            health_report['inf_parameters'] += inf_count
            health_report['large_parameters'] += large_count
            
            # 参数统计
            health_report['parameter_stats'][name] = {
                'mean': param_data.mean().item(),
                'std': param_data.std().item(),
                'min': param_data.min().item(),
                'max': param_data.max().item(),
                'nan_count': nan_count,
                'inf_count': inf_count
            }
        
        # 计算健康分数
        total_params = health_report['total_parameters']
        problematic_params = (health_report['nan_parameters'] + 
                            health_report['inf_parameters'] + 
                            health_report['large_parameters'])
        
        health_report['health_score'] = 1.0 - (problematic_params / max(total_params, 1))
        
        return health_report
    
    def apply_mixed_precision(self, 
                            loss: torch.Tensor,
                            optimizer: torch.optim.Optimizer,
                            model: nn.Module) -> Dict[str, Any]:
        """
        应用混合精度训练
        
        Args:
            loss: 损失
            optimizer: 优化器
            model: 模型
            
        Returns:
            mp_stats: 混合精度统计
        """
        if self.scaler is None:
            return {}
        
        # 缩放损失
        scaled_loss = self.scaler.scale(loss)
        
        # 反向传播
        scaled_loss.backward()
        
        # 更新参数
        self.scaler.step(optimizer)
        self.scaler.update()
        
        # 清零梯度
        optimizer.zero_grad()
        
        return {
            'scale': self.scaler.get_scale(),
            'growth_factor': self.scaler.get_growth_factor(),
            'backoff_factor': self.scaler.get_backoff_factor(),
            'growth_interval': self.scaler.get_growth_interval()
        }
